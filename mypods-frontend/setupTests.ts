// jest-dom adds custom jest matchers for asserting on DOM nodes.
// allows you to do things like:
// expect(element).toHaveTextContent(/react/i)
// learn more: https://github.com/testing-library/jest-dom
import '@testing-library/jest-dom';
import { Mocked, vi } from 'vitest';
import { TestRentalAgreementViewer } from './src/testUtils/testComponents/TestRentalAgreementViewer';
import * as useFeatureFlags from './src/helpers/useFeatureFlags';
import { createUseFeatureFlagResult } from './src/testUtils/MyPodsFactories';

export const mockScrollIntoView = vi.fn();

beforeEach(() => {
  sessionStorage.clear();
  (window as any).localStorage = storageMock();
  (window as any).scrollTo = vi.fn();
  mockedUseFeatureFlags.useFeatureFlags.mockImplementation(() => createUseFeatureFlagResult());
  Element.prototype.scrollIntoView = mockScrollIntoView;
});

const { alert, open } = window;
global.URL.createObjectURL = vi.fn();

beforeAll(() => {
  delete (window as any).location;
  (window as any).location = { replace: vi.fn(), reload: vi.fn(), href: '' };
  delete (window as any).alert;
  (window as any).alert = vi.fn();
  delete (window as any).open;
  (window as any).open = vi.fn();
});

afterAll(() => {
  window.alert = alert;
  window.open = open;
});

export const mockOrderHasSignedMothAgreement = vi.fn();

let signedMockOrderIds: string[] | undefined;
let documentToken: string | null;
let showWeightedBanner: boolean | null;
let billingToken: string | null;

vi.mock('./src/helpers/storageHelpers', () => ({
  orderHasSignedMothAgreement: mockOrderHasSignedMothAgreement,
  setOrdersWithSignedMothAgreements: (orderIds: string[]) => {
    signedMockOrderIds = orderIds;
  },
  getOrdersWithSignedMothAgreements: () => signedMockOrderIds,
  setDocumentToken: (token: string | null) => {
    documentToken = token;
  },
  getDocumentToken: () => documentToken,
  setWeightTicketBanner: (showWeightedTicketBanner: boolean | null) => {
    showWeightedBanner = showWeightedTicketBanner;
  },
  getWeightTicketBanner: () => showWeightedBanner,
  setBillingDocumentToken: (token: string | null) => {
    billingToken = token;
  },
  getBillingDocumentToken: () => billingToken
}));

vi.mock(
  './src/components/RentalAgreementOnboarding/RentalAgreementDocumentViewer/PdfRentalAgreementViewer',
  () => ({
    PdfRentalAgreementViewer: TestRentalAgreementViewer
  })
);

vi.mock('./src/helpers/useFeatureFlags');
export const mockedUseFeatureFlags = useFeatureFlags as Mocked<typeof useFeatureFlags>;

export const mockSendPasswordOnboardingStart = vi.fn();
export const mockSendPasswordOnboardingComplete = vi.fn();
vi.mock('./src/config/usePasswordOnboardingEvents', () => ({
  useLegacyPasswordOnboardingEvents: () => ({
    sendPasswordOnboardingStart: mockSendPasswordOnboardingStart,
    sendPasswordOnboardingComplete: mockSendPasswordOnboardingComplete
  }),
  usePoetPasswordOnboardingEvents: () => ({
    sendPasswordOnboardingStart: mockSendPasswordOnboardingStart,
    sendPasswordOnboardingComplete: mockSendPasswordOnboardingComplete
  })
}));

function storageMock(): Storage {
  let storage: { [key: string]: string } = {};

  return {
    clear: () => {
      storage = {};
    },
    setItem(key: string, value: string) {
      storage[key] = value || '';
    },
    getItem(key: string) {
      return key in storage ? storage[key] : null;
    },
    removeItem(key: string) {
      delete storage[key];
    },
    get length() {
      return Object.keys(storage).length;
    },
    key(i: number) {
      const keys = Object.keys(storage);
      return keys[i] || null;
    }
  };
}

export const mockNavigate = vi.fn();
vi.mock('react-router', async () => ({
  ...((await vi.importActual('react-router')) as any),
  useNavigate: () => mockNavigate
}));

export const mockAuthorizationEntrypoint = vi.fn();
export const mockLegacyAuthorizationEntrypoint = vi.fn();
export const mockAcceptRentalAgreement = vi.fn();
export const mockLegacyAcceptRentalAgreement = vi.fn();
export const mockGetCustomer = vi.fn();
export const mockLegacyGetCustomer = vi.fn();
export const mockGetCustomerOrders = vi.fn();
export const mockLegacyGetCustomerOrders = vi.fn();
export const mockChallengeEmail = vi.fn();
export const mockLegacyChallengeEmail = vi.fn();
export const mockVerifyChallengeCode = vi.fn();
export const mockLegacyVerifyChallengeCode = vi.fn();
export const mockUpdateEmail = vi.fn();
export const mockLegacyUpdateEmail = vi.fn();
export const mockUpdatePassword = vi.fn();
export const mockLegacyUpdatePassword = vi.fn();
export const mockUpdatePrimaryPhone = vi.fn();
export const mockLegacyUpdatePrimaryPhone = vi.fn();
export const mockUpdateSecondaryPhone = vi.fn();
export const mockLegacyUpdateSecondaryPhone = vi.fn();
export const mockUpdateBillingAddress = vi.fn();
export const mockLegacyUpdateBillingAddress = vi.fn();
export const mockUpdateShippingAddress = vi.fn();
export const mockLegacyUpdateShippingAddress = vi.fn();
export const mockUpdateSmsOptIn = vi.fn();
export const mockLegacyUpdateSmsOptIn = vi.fn();
export const mockUpdatePin = vi.fn();
export const mockLegacyUpdatePin = vi.fn();
export const mockGetFile = vi.fn();
export const mockLegacyGetFile = vi.fn();
export const mockGetSasUrl = vi.fn();
export const mockLegacyGetSasUrl = vi.fn();
export const mockGetRentalAgreement = vi.fn();
export const mockLegacyGetRentalAgreement = vi.fn();
export const mockGetBillingInformation = vi.fn();
export const mockCreateCustomStatement = vi.fn();
export const mockLegacyCreateCustomStatement = vi.fn();
export const mockGetContainerAvailability = vi.fn();
export const mockLegacyGetContainerAvailability = vi.fn();
export const mockGetDocuments = vi.fn();
export const mockLegacyGetDocuments = vi.fn();
export const mockAcceptInitialDeliveryPlacement = vi.fn();
export const mockLegacyAcceptInitialDeliveryPlacement = vi.fn();
export const mockUpdateMoveLeg = vi.fn();
export const mockLegacyUpdateMoveLeg = vi.fn();
export const mockIsSameServiceArea = vi.fn();
export const mockCloneQuoteFromOrder = vi.fn();
export const mockLegacyIsSameServiceArea = vi.fn();
export const mockGetPaymentMethods = vi.fn();
export const mockLegacyGetPaymentMethods = vi.fn();
export const mockSetDefaultPaymentMethod = vi.fn();
export const mockLegacySetDefaultPaymentMethod = vi.fn();
export const mockPayInvoices = vi.fn();
export const mockLegacyPayInvoices = vi.fn();
export const mockRefreshSession = vi.fn();
export const mockLegacyRefreshSession = vi.fn();
export const mockAddPaymentMethod = vi.fn();
export const mockLegacyAddPaymentMethod = vi.fn();
export const mockSignFnpsWaiver = vi.fn();
export const mockLegacySignFnpsWaiver = vi.fn();
export const mockSignMothAgreement = vi.fn();
export const mockLegacySignMothAgreement = vi.fn();
export const mockGetOrderDocuments = vi.fn();
export const mockGetPodsReadyOrders = vi.fn();
export const mockLegacyGetPodsReadyOrders = vi.fn();
export const mockGetCustomerDocuments = vi.fn();
export const mockStartPodsReadySession = vi.fn();
export const mockLegacyStartPodsReadySession = vi.fn();
export const mockCreatePodsReadyAccount = vi.fn();
export const mockLegacyCreatePodsReadyAccount = vi.fn();
export const mockGetPodsReadyOrderDocuments = vi.fn();
export const mockApplyQuoteToOrder = vi.fn();

vi.mock('./src/networkRequests/MyPodsService', () => ({
  useMyPodsService: () => ({
    authorizationEntrypoint: mockAuthorizationEntrypoint,
    acceptRentalAgreement: mockAcceptRentalAgreement,
    getCustomer: mockGetCustomer,
    getCustomerOrders: mockGetCustomerOrders,
    challengeEmail: mockChallengeEmail,
    verifyChallengeCode: mockVerifyChallengeCode,
    updateEmail: mockUpdateEmail,
    updatePassword: mockUpdatePassword,
    updatePrimaryPhone: mockUpdatePrimaryPhone,
    updateSecondaryPhone: mockUpdateSecondaryPhone,
    updateBillingAddress: mockUpdateBillingAddress,
    updateShippingAddress: mockUpdateShippingAddress,
    updateSmsOptIn: mockUpdateSmsOptIn,
    updatePin: mockUpdatePin,
    getFile: mockGetFile,
    getSasUrl: mockGetSasUrl,
    getRentalAgreement: mockGetRentalAgreement,
    getBillingInformation: mockGetBillingInformation,
    createCustomStatement: mockCreateCustomStatement,
    getContainerAvailability: mockGetContainerAvailability,
    acceptInitialDeliveryPlacement: mockAcceptInitialDeliveryPlacement,
    updateMoveLeg: mockUpdateMoveLeg,
    isSameServiceArea: mockIsSameServiceArea,
    cloneQuoteFromOrder: mockCloneQuoteFromOrder,
    getDocuments: mockGetDocuments,
    getPaymentMethods: mockGetPaymentMethods,
    setDefaultPaymentMethod: mockSetDefaultPaymentMethod,
    refreshSession: mockRefreshSession,
    legacyRefreshSession: mockLegacyRefreshSession,
    payInvoices: mockPayInvoices,
    addPaymentMethod: mockAddPaymentMethod,
    signFnpsWaiver: mockSignFnpsWaiver,
    signMothAgreement: mockSignMothAgreement,
    getOrderDocuments: mockGetOrderDocuments,
    getCustomerDocuments: mockGetCustomerDocuments,
    startPodsReadySession: mockStartPodsReadySession,
    createPodsReadyAccount: mockCreatePodsReadyAccount,
    getPodsReadyOrders: mockGetPodsReadyOrders,
    getPodsReadyOrderDocuments: mockGetPodsReadyOrderDocuments,
    applyQuoteToOrder: mockApplyQuoteToOrder
  })
}));

vi.mock('./src/networkRequests/legacy/LegacyMyPodsService', () => ({
  useLegacyMyPodsService: () => ({
    authorizationEntrypoint: mockLegacyAuthorizationEntrypoint,
    acceptRentalAgreement: mockLegacyAcceptRentalAgreement,
    getCustomer: mockLegacyGetCustomer,
    getCustomerOrders: mockLegacyGetCustomerOrders,
    challengeEmail: mockLegacyChallengeEmail,
    verifyChallengeCode: mockLegacyVerifyChallengeCode,
    updateEmail: mockLegacyUpdateEmail,
    updatePassword: mockLegacyUpdatePassword,
    updatePrimaryPhone: mockLegacyUpdatePrimaryPhone,
    updateSecondaryPhone: mockLegacyUpdateSecondaryPhone,
    updateBillingAddress: mockLegacyUpdateBillingAddress,
    updateShippingAddress: mockLegacyUpdateShippingAddress,
    updateSmsOptIn: mockLegacyUpdateSmsOptIn,
    updatePin: mockLegacyUpdatePin,
    getFile: mockLegacyGetFile,
    getSasUrl: mockLegacyGetSasUrl,
    getDocuments: mockLegacyGetDocuments,
    createCustomStatement: mockLegacyCreateCustomStatement,
    getContainerAvailability: mockLegacyGetContainerAvailability,
    getRentalAgreement: mockLegacyGetRentalAgreement,
    updateMoveLeg: mockLegacyUpdateMoveLeg,
    isSameServiceArea: mockLegacyIsSameServiceArea,
    acceptInitialDeliveryPlacement: mockLegacyAcceptInitialDeliveryPlacement,
    addPaymentMethod: mockLegacyAddPaymentMethod,
    signFnpsWaiver: mockLegacySignFnpsWaiver,
    signMothAgreement: mockLegacySignMothAgreement,
    getPaymentMethods: mockLegacyGetPaymentMethods,
    setDefaultPaymentMethod: mockLegacySetDefaultPaymentMethod,
    payInvoices: mockLegacyPayInvoices,
    createPodsReadyAccount: mockLegacyCreatePodsReadyAccount,
    getPodsReadyOrders: mockLegacyGetPodsReadyOrders,
    getPodsReadyDocuments: mockGetPodsReadyOrderDocuments,
    startPodsReadySession: mockLegacyStartPodsReadySession
  })
}));

export const mockLegacyPodsReadyAuthorizationEntrypoint = vi.fn();

vi.mock('./src/networkRequests/legacy/useLegacyPodsReadyService', () => ({
  useLegacyPodsReadyService: () => ({
    authorizationEntrypoint: mockLegacyPodsReadyAuthorizationEntrypoint
  })
}));

const allGetEvents = {
  pageLoadEvent: vi.fn(),
  startAgreement: vi.fn(),
  viewAgreementStep: vi.fn(),
  declineAgreement: vi.fn(),
  submitAgreement: vi.fn(),
  successAgreement: vi.fn(),
  fileDownload: vi.fn(),
  startPlacementFlow: vi.fn(),
  successPlacementFlow: vi.fn(),
  viewPlacementStep: vi.fn(),
  submitGenerateStatement: vi.fn(),
  successGenerateStatement: vi.fn(),
  startAddPayment: vi.fn(),
  submitAddPayment: vi.fn(),
  successAddPayment: vi.fn(),
  submitSetDefaultPayment: vi.fn(),
  successSetDefaultPayment: vi.fn(),
  startEditAccountDetail: vi.fn(),
  submitAccountDetail: vi.fn(),
  successAccountDetail: vi.fn(),
  submitPayment: vi.fn(),
  successPayment: vi.fn(),
  startSchedule: vi.fn(),
  startEditSchedule: vi.fn(),
  pushMoveLegScheduleEvent: vi.fn(),
  errorEvent: vi.fn()
};

vi.mock('./src/config/google/useGtmEvents', () => ({
  useGtmEvents: () => allGetEvents,
  useGtmEventsWithSessionClaims: () => allGetEvents,
  useLegacyGtmEvents: () => allGetEvents,
  useGtmEventsWithCustomer: () => allGetEvents
}));
