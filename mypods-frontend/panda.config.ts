import { defineConfig } from '@pandacss/dev';
import { podsPreset } from 'pods-component-library/panda.config';

export default defineConfig({
  presets: ['@pandacss/preset-base', podsPreset],
  // Whether to use css reset
  preflight: false,
  theme: {
    extend: {
      tokens: {
        zIndex: {
          podsReadyProgressBar: { value: 1 },
          // Google Places Select z-index is 1000,
          legacyNavBar: { value: 998 },
          rightSchedulingDrawer: { value: 999 },
          leftNavDrawer: { value: 1000 },
          appBar: { value: 1100 },
          mapDialog: { value: 1001 }
        }
      },
      keyframes: {
        spin: {
          from: { transform: 'rotate(0deg)' },
          to: { transform: 'rotate(360deg)' }
        }
      }
    }
  },

  // Where to look for your css declarations
  include: [
    './src/**/*.{js,jsx,ts,tsx}',
    './pages/**/*.{js,jsx,ts,tsx}',
    './node_modules/pods-component-library/build/panda.buildinfo.json'
  ],

  // Files to exclude
  exclude: [],

  importMap: 'pods-component-library/styled-system',
  outdir: 'styled-system'
});
