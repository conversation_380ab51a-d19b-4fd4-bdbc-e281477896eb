import ESLint from '@eslint/js';
import globals from 'globals';
import TS_ESLint from 'typescript-eslint';
import react from 'eslint-plugin-react';
import reactHooks from 'eslint-plugin-react-hooks';
import reactRefresh from 'eslint-plugin-react-refresh';
import jsxA11y from 'eslint-plugin-jsx-a11y';
import unusedImports from 'eslint-plugin-unused-imports';
import lodash from 'eslint-plugin-lodash';
import podsComponentLibrary from 'pods-component-library/src/plugin';

export default TS_ESLint.config(
  {
    ignores: [
      'dist/',
      'node_modules/',
      '**/*.d.ts',
      'coverage/',
      'devops/',
      'cypress/',
      'design/',
      'public/',
      'styled-system/',
      '.vite_cache/'
    ]
  },
  ESLint.configs.recommended,
  reactRefresh.configs.recommended,
  {
    languageOptions: {
      globals: {
        ...globals.browser
      }
    }
  },
  {
    files: ['**/*.ts', '**/*.tsx'],
    ignores: ['**/*.test.ts', '**/*.test.tsx', '**/__tests__/**', '.storybook/**'],
    extends: [TS_ESLint.configs.strictTypeChecked],
    plugins: {
      '@typescript-eslint': TS_ESLint.plugin,
      react,
      'react-hooks': reactHooks,
      'jsx-a11y': jsxA11y,
      'unused-imports': unusedImports,
      lodash,
      'pods-component-library': podsComponentLibrary
    },
    settings: {
      react: {
        version: 'detect' // uses the package.json to figure out the version
      }
    },
    languageOptions: {
      parser: TS_ESLint.parser,
      parserOptions: {
        ecmaFeatures: {
          jsx: true
        },
        projectService: true,
        tsconfigRootDir: import.meta.dirname
      }
    },
    rules: {
      ...react.configs.recommended.rules,
      ...reactHooks.configs.recommended.rules,
      ...podsComponentLibrary.configs.recommended.rules,

      '@typescript-eslint/no-unnecessary-boolean-literal-compare': 'off', // this requires compiler option "strictNullChecks"
      '@typescript-eslint/no-unnecessary-condition': 'off', // this requires compiler option "strictNullChecks"
      '@typescript-eslint/no-unsafe-call': 'off',
      '@typescript-eslint/no-unsafe-return': 'off',
      '@typescript-eslint/require-await': 'off',
      '@typescript-eslint/no-unsafe-argument': 'off',
      '@typescript-eslint/use-unknown-in-catch-callback-variable': 'off',
      '@typescript-eslint/no-unsafe-assignment': 'off',
      '@typescript-eslint/no-unsafe-member-access': 'off',
      '@typescript-eslint/no-non-null-assertion': 'off',
      '@typescript-eslint/no-empty-object-type': 'off',
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/restrict-template-expressions': 'off',
      '@typescript-eslint/no-floating-promises': 'off',
      '@typescript-eslint/no-misused-promises': 'off',
      '@typescript-eslint/unbound-method': 'off',
      '@typescript-eslint/no-unsafe-function-type': 'off',
      '@typescript-eslint/no-non-null-asserted-optional-chain': 'off',
      '@typescript-eslint/no-unnecessary-type-conversion': 'off',
      '@typescript-eslint/restrict-plus-operands': 'off',
      '@typescript-eslint/prefer-promise-reject-errors': 'off',
      '@typescript-eslint/no-base-to-string': 'off',
      '@typescript-eslint/no-redundant-type-constituents': 'off',
      '@typescript-eslint/no-unnecessary-type-parameters': 'off',
      '@typescript-eslint/no-misused-spread': 'off',
      '@typescript-eslint/ban-ts-comment': 'warn',
      '@typescript-eslint/no-deprecated': 'warn',
      '@typescript-eslint/no-unsafe-enum-comparison': 'warn',
      '@typescript-eslint/no-unused-vars': [
        'error',
        {
          argsIgnorePattern: '^_',
          varsIgnorePattern: '^_',
          destructuredArrayIgnorePattern: '^_',
          caughtErrorsIgnorePattern: '^_'
        }
      ],

      'react/display-name': 'off',

      'react-refresh/only-export-components': 'off'

      // 'no-restricted-imports': [
      //   'error',
      //   {
      //     paths: [
      //       {
      //         name: '@mui/material',
      //         message: 'Avoid barrel imports. Use direct imports.'
      //       }
      //     ]
      //   }
      // ]
    }
  },
  {
    files: ['**/*.js', '**/*.mjs'],
    extends: [TS_ESLint.configs.disableTypeChecked],
    languageOptions: {
      globals: {}
    },
    rules: {}
  }
);
