{"name": "mypods-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start": "npm run -s i18n:build && vite", "build": "tsc && npm run -s i18n:build && vite build", "test": "vitest", "lint": "eslint ./ --report-unused-disable-directives", "lint:fix": "npm run lint -- --fix", "typecheck": "tsc --noEmit", "format": "prettier --write \"./**/*.{js,jsx,ts,tsx,css,md,json}\"", "format:check": "prettier --check \"./**/*.{js,jsx,ts,tsx,css,md,json}\"", "validate": "npm run lint:fix && npm run format && npm run typecheck && vitest run", "preview": "vite preview", "i18n:build": "node ./i18n/build.cjs && prettier --write src/locales/TranslationKeys.ts", "i18n:report": "node ./i18n/report.cjs", "i18n:update": "node ./i18n/update.cjs", "design:build": "node design/build.cjs && prettier --write src/helpers", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "engines": {"node": ">=20.15.0"}, "dependencies": {"@datadog/browser-logs": "^5.26.0", "@datadog/browser-rum": "^5.23.3", "@emotion/styled": "^11.13.0", "@mui/icons-material": "^5.16.7", "@mui/lab": "^5.0.0-alpha.173", "@mui/material": "^5.16.7", "@mui/system": "^5.16.7", "@mui/x-date-pickers": "^5.0.20", "@pandacss/dev": "^1.0.1", "@phosphor-icons/react": "^2.1.7", "@splitsoftware/splitio": "^10.28.0", "@splitsoftware/splitio-react": "^1.13.0", "@tanstack/react-query": "^5.51.24", "@tanstack/react-query-devtools": "^5.56.2", "@vis.gl/react-google-maps": "^1.1.0", "@wojtekmaj/react-hooks": "^1.21.0", "axios": "^1.7.4", "braintree-web": "^3.97.4", "buffer": "^6.0.3", "date-fns": "^2.30.0", "date-fns-tz": "^2.0.0", "file-saver": "^2.0.5", "history": "^5.3.0", "i18next": "^23.14.0", "i18next-browser-languagedetector": "^8.0.0", "immer": "^10.1.1", "lodash": "^4.17.21", "mui-one-time-password-input": "^2.0.3", "pdfjs-dist": "^4.5.136", "pods-component-library": "2.1.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-gtm-module": "^2.0.11", "react-i18next": "^13.0.1", "react-imask": "^7.6.1", "react-pdf": "^9.1.0", "react-router": "^7.8.0", "universal-cookie": "^7.2.0", "use-immer": "^0.10.0", "uuid": "^10.0.0"}, "devDependencies": {"@chromatic-com/storybook": "^1.8.0", "@emotion/react": "^11.13.3", "@eslint/js": "^9.33.0", "@storybook/addon-essentials": "^8.2.9", "@storybook/addon-interactions": "^8.2.9", "@storybook/addon-links": "^8.2.9", "@storybook/addon-onboarding": "^8.2.9", "@storybook/addon-themes": "^8.2.9", "@storybook/blocks": "^8.2.9", "@storybook/react": "^8.2.9", "@storybook/react-vite": "^8.2.9", "@storybook/test": "^8.2.9", "@testing-library/jest-dom": "^6.4.8", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.5.2", "@types/braintree": "^3.3.11", "@types/braintree-web": "^3.96.9", "@types/eslint-plugin-jsx-a11y": "^6.10.0", "@types/file-saver": "^2.0.7", "@types/jest": "^29.5.12", "@types/lodash": "^4.17.7", "@types/node": "^22.4.2", "@types/react": "^18.3.4", "@types/react-gtm-module": "^2.0.3", "@types/react-helmet": "^6.1.11", "@types/react-helmet-async": "^1.0.3", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.39.1", "@typescript-eslint/parser": "^8.39.1", "@vitejs/plugin-react": "^4.3.1", "change-case": "4.0.0", "csv-parse": "^5.5.6", "csv-writer": "^1.6.0", "eslint": "^9.34.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-jest": "^29.0.1", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-lodash": "^8.0.0", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "eslint-plugin-storybook": "^0.12.0", "eslint-plugin-unused-imports": "^4.1.4", "fs-extra": "11.2.0", "globals": "^16.3.0", "jiti": "^2.5.1", "jsdom": "^24.1.1", "prettier": "3.6.2", "react-helmet": "^6.1.0", "storybook": "^8.2.9", "storybook-react-i18next": "^3.1.7", "typescript": "^5.5.4", "typescript-eslint": "^8.39.1", "vite": "^6.2.5", "vite-plugin-svgr": "^4.2.0", "vite-tsconfig-paths": "^5.0.1", "vitest": "^3.1.1"}}