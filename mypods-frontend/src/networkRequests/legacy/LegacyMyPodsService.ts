import { useContext } from 'react';
import { useLocation } from 'react-router';
import { ApigeeContext } from '../../context/ApigeeContext';
import { EntryPointResult, RefreshSessionClaims } from '../responseEntities/AuthorizationEntities';
import {
  BillingResponse,
  CustomStatement,
  CustomStatementRequest,
  formatCustomRange
} from '../responseEntities/BillingEntities';
import {
  DOCUMENTS_TOKEN_KEY,
  getBillingDocumentToken,
  getDocumentToken
} from '../../helpers/storageHelpers';
import {
  AcceptRentalAgreementRequest,
  Documents,
  SignFnpsWaiverRequest,
  SignMothAgreementRequest
} from '../responseEntities/DocumentApiEntities';
import {
  AddPaymentMethodRequest,
  PayInvoicesRequest,
  PaymentMethodAPI,
  SetDefaultPaymentMethodRequest
} from '../responseEntities/PaymentEntities';
import { SameServiceAreaRequest, UpdateMoveLegRequest } from '../../domain/OrderEntities';
import {
  PodsReadyAccessTokenClaims,
  PodsReadyCreateAccountRequest,
  PodsReadyCreateAccountResponse,
  StartPodsReadySession
} from '../responseEntities/PodsReadyEntities';
import { LegacyUpdateMoveLegResponse, OrderAPI } from '../responseEntities/OrderAPIEntities';
import {
  ContainerAvailabilityRequest,
  ContainerAvailabilityResponse
} from '../responseEntities/AvailabilityAPIEntities';
import {
  ChallengeEmailRequest,
  Customer,
  UpdateBillingAddressRequest,
  UpdateEmailRequest,
  UpdatePasswordRequest,
  UpdatePasswordResponse,
  UpdatePinRequest,
  UpdatePrimaryPhoneRequest,
  UpdateSecondaryPhoneRequest,
  UpdateShippingAddressRequest,
  UpdateSmsOptInRequest,
  VerifyChallengeRequest
} from '../responseEntities/CustomerEntities';
import { getBaseUrl, isPodsReadyLocation } from '../../helpers/podsReadyHelpers';

export const useLegacyMyPodsService = () => {
  const axiosInstance = useContext(ApigeeContext);
  const location = useLocation();

  function getLegacyBaseUrl(controllerName: string): string {
    return getBaseUrl('v1/legacy', isPodsReadyLocation(location), controllerName);
  }

  async function refreshSession(): Promise<RefreshSessionClaims> {
    const res = await axiosInstance.post('v1/session/refresh');
    return res.data;
  }

  async function authorizationEntrypoint(): Promise<EntryPointResult> {
    const route = `${getLegacyBaseUrl('/authorization')}/entrypoint`;
    const res = await axiosInstance.get(route);
    return res.data;
  }

  async function getCustomer(): Promise<Customer> {
    const res = await axiosInstance.get(`${getLegacyBaseUrl('')}/customer`);
    return res.data;
  }

  async function getCustomerOrders(): Promise<OrderAPI[]> {
    const route = `${getLegacyBaseUrl('/customer')}/orders`;
    const res = await axiosInstance.get(route);
    return res.data;
  }

  async function challengeEmail(request: ChallengeEmailRequest): Promise<void> {
    return axiosInstance.put('v1/legacy/customer/email/challenge', request);
  }

  async function verifyChallenge(request: VerifyChallengeRequest): Promise<void> {
    return axiosInstance.post('v1/legacy/customer/email/verify-challenge', request);
  }

  async function updateEmail(request: UpdateEmailRequest): Promise<void> {
    return axiosInstance.patch('v1/legacy/customer/email', request);
  }

  async function updatePassword(request: UpdatePasswordRequest): Promise<UpdatePasswordResponse> {
    const res = await axiosInstance.patch(`v1/legacy/customer/change-password`, request);
    return res.data;
  }

  async function updatePrimaryPhone(request: UpdatePrimaryPhoneRequest): Promise<void> {
    return axiosInstance.patch('v1/legacy/customer/primary-phone', request);
  }

  async function updateSecondaryPhone(request: UpdateSecondaryPhoneRequest): Promise<void> {
    return axiosInstance.patch('v1/legacy/customer/secondary-phone', request);
  }

  async function updateBillingAddress(request: UpdateBillingAddressRequest): Promise<void> {
    return axiosInstance.patch('v1/legacy/customer/billing-address', request);
  }

  async function updateShippingAddress(request: UpdateShippingAddressRequest): Promise<void> {
    return axiosInstance.patch('v1/legacy/customer/shipping-address', request);
  }

  async function updateSmsOptIn(request: UpdateSmsOptInRequest): Promise<void> {
    return axiosInstance.patch('v1/legacy/customer/sms-opt-in', request);
  }

  async function updatePin(request: UpdatePinRequest): Promise<void> {
    return axiosInstance.patch('v1/legacy/customer/pin', request);
  }

  const headersForDocuments = (isBillingDocument: boolean) => {
    const token = isBillingDocument ? getBillingDocumentToken() : getDocumentToken();
    return token ? { headers: { [`${DOCUMENTS_TOKEN_KEY}`]: token } } : undefined;
  };

  async function getBillingInformation(): Promise<BillingResponse> {
    const res = await axiosInstance.get(`v1/legacy/billing/customer/billing-information`, {
      ...headersForDocuments(true)
    });
    return res.data;
  }

  async function getFile(documentId: string, isBillingDocument: boolean) {
    const response = await axiosInstance.get(`${getLegacyBaseUrl('')}/document/${documentId}`, {
      responseType: 'blob',
      ...headersForDocuments(isBillingDocument)
    });
    if (response && response.status === 200) {
      const blob = new Blob([response.data], { type: response.headers['content-type'] });
      return URL.createObjectURL(blob);
    }
    return response.data;
  }

  async function getSasUrl(docRef: string, isBillingDocument: boolean): Promise<string> {
    const response = await axiosInstance.get(`v1/legacy/document/sas-url?docRef=${docRef}`, {
      ...headersForDocuments(isBillingDocument)
    });
    return response.data;
  }

  async function getDocuments(): Promise<Documents> {
    const response = await axiosInstance.get('v1/legacy/document/documents', {
      ...headersForDocuments(false)
    });

    return response.data;
  }

  async function createCustomStatement(request: CustomStatementRequest): Promise<CustomStatement> {
    const response = await axiosInstance.post(`v1/legacy/document/custom-statement`, request, {
      responseType: 'blob'
    });
    const blob = new Blob([response.data], { type: response.headers['content-type'] });
    return {
      content: URL.createObjectURL(blob),
      dateRange: formatCustomRange(request.startDate, request.endDate)
    };
  }

  async function getRentalAgreement(companyCode: string) {
    const baseRoute = getLegacyBaseUrl('/document');
    const response = await axiosInstance.get(`${baseRoute}/rental-agreement/${companyCode}`, {
      responseType: 'blob'
    });
    const blob = new Blob([response.data], { type: response.headers['content-type'] });
    return URL.createObjectURL(blob);
  }

  async function acceptRentalAgreement(request: AcceptRentalAgreementRequest): Promise<void> {
    const baseRoute = getLegacyBaseUrl('/document');
    const res = await axiosInstance.patch(`${baseRoute}/accept-rental-agreement`, request);
    return res.data;
  }

  async function acceptRentalAgreements(request: AcceptRentalAgreementRequest[]): Promise<void> {
    const baseRoute = getLegacyBaseUrl('/document');
    const res = await axiosInstance.patch(`${baseRoute}/accept-rental-agreements`, request);
    return res.data;
  }

  async function signFnpsWaiver(request: SignFnpsWaiverRequest): Promise<void> {
    return axiosInstance.post('v1/legacy/document/sign-fnps', request);
  }

  async function signMothAgreement(request: SignMothAgreementRequest): Promise<string> {
    const baseRoute = getLegacyBaseUrl('/document');
    return axiosInstance.post(`${baseRoute}/sign-moth-agreement`, request);
  }

  // -- move legs --
  async function updateMoveLeg(
    request: UpdateMoveLegRequest
  ): Promise<LegacyUpdateMoveLegResponse> {
    const res = await axiosInstance.post('/v1/legacy/order/update-move-leg', request);
    return res.data;
  }

  async function getContainerAvailability(
    request: ContainerAvailabilityRequest
  ): Promise<ContainerAvailabilityResponse> {
    const res = await axiosInstance.post('/v1/legacy/availability/container-availability', request);
    return res.data;
  }

  async function isSameServiceArea(request: SameServiceAreaRequest): Promise<boolean> {
    const res = await axiosInstance.post('/v1/legacy/order/is-same-service-area', request);
    return res.data;
  }

  async function acceptInitialDeliveryPlacement(orderId: string): Promise<void> {
    await axiosInstance.patch(`/v1/legacy/order/${orderId}/accept-initial-delivery-placement`);
  }

  async function addPaymentMethod(request: AddPaymentMethodRequest): Promise<void> {
    const res = await axiosInstance.post(`/v1/legacy/payment/payment-method`, request);
    return res.data;
  }

  async function getPaymentMethods(): Promise<PaymentMethodAPI[]> {
    const res = await axiosInstance.get(`${getLegacyBaseUrl('/payment')}/methods`);
    return res.data;
  }

  async function setDefaultPaymentMethod(request: SetDefaultPaymentMethodRequest): Promise<void> {
    return axiosInstance.patch('v1/legacy/payment/set-default-payment-method', request);
  }

  async function payInvoices(request: PayInvoicesRequest): Promise<void> {
    return axiosInstance.post('v1/legacy/payment/pay-invoices', request);
  }

  async function startPodsReadySession(
    tokenRequest: StartPodsReadySession
  ): Promise<PodsReadyAccessTokenClaims> {
    const res = await axiosInstance.post('/v1/legacy/pods-ready/session', tokenRequest);
    return res.data;
  }

  async function createPodsReadyAccount(
    request: PodsReadyCreateAccountRequest
  ): Promise<PodsReadyCreateAccountResponse> {
    const res = await axiosInstance.post('v1/legacy/pods-ready/create-account', request);
    return res.data;
  }

  async function getPodsReadyOrders(): Promise<OrderAPI[]> {
    const res = await axiosInstance.get('v1/legacy/pods-ready/orders');
    return res.data;
  }

  async function getPodsReadyDocuments(): Promise<Documents> {
    const res = await axiosInstance.get('v1/legacy/pods-ready/documents', {
      ...headersForDocuments(false)
    });
    return res.data;
  }

  return {
    refreshSession,
    authorizationEntrypoint,
    getCustomer,
    getCustomerOrders,
    updateEmail,
    challengeEmail,
    verifyChallenge,
    updatePassword,
    updatePrimaryPhone,
    updateSecondaryPhone,
    updateBillingAddress,
    updateShippingAddress,
    updateSmsOptIn,
    updatePin,
    getBillingInformation,
    createCustomStatement,
    getFile,
    getSasUrl,
    getDocuments,
    getRentalAgreement,
    acceptRentalAgreement,
    acceptRentalAgreements,
    signFnpsWaiver,
    signMothAgreement,
    updateMoveLeg,
    isSameServiceArea,
    acceptInitialDeliveryPlacement,
    getContainerAvailability,
    addPaymentMethod,
    getPaymentMethods,
    setDefaultPaymentMethod,
    payInvoices,
    startPodsReadySession,
    createPodsReadyAccount,
    getPodsReadyOrders,
    getPodsReadyDocuments
  };
};
