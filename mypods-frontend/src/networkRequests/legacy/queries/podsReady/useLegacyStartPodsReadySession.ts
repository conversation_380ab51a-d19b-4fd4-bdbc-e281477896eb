import { useSuspenseQuery } from '@tanstack/react-query';
import { useLocation } from 'react-router';
import axios from 'axios';
import { useLegacyMyPodsService } from '../../LegacyMyPodsService';
import { PodsReadyAccessTokenClaims } from '../../../responseEntities/PodsReadyEntities';
import { QueryCacheKeys } from '../../../QueryCacheKeys';
import { redirectToLoginWithEmail } from '../../../../context/ApigeeContext';
import { parseJwt } from '../../../../helpers/jwtHelper';
import { useSplitEvents } from '../../../../config/useSplitEvents';
import { SplitEventType } from '../../../../config/SplitEventTypes';

export const useLegacyStartPodsReadySession = () => {
  const VALID_TOKEN_BUT_PASSWORD_ONBOARDING_OFF = 403;
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const token = queryParams.get('token') || '';
  const startPodsReadySessionRequest = { token };
  const { startPodsReadySession } = useLegacyMyPodsService();

  const hasToken = token.length > 0;
  const claims = parseJwt(token);
  const { send } = useSplitEvents(claims?.customerId);

  const queryResult = useSuspenseQuery({
    queryKey: [QueryCacheKeys.PODS_READY_SESSION_KEY],
    queryFn: () => startPodsReadySession(startPodsReadySessionRequest),
    retry: (failureCount, error) => {
      if (axios.isAxiosError(error)) {
        const status = error.response?.status;
        if (status === 400 || status === 401) {
          return false;
        }
        if (status === VALID_TOKEN_BUT_PASSWORD_ONBOARDING_OFF) {
          send(SplitEventType.PASSWORD_ONBOARDING_START);
          try {
            const email = claims?.email;
            redirectToLoginWithEmail(email || '');
          } catch (e) {
            redirectToLoginWithEmail('');
          }
        }
      }
      return failureCount < 3;
    }
  });
  const podsReadySessionClaims: PodsReadyAccessTokenClaims = { ...queryResult.data };

  return {
    ...queryResult,
    hasToken,
    podsReadySessionClaims
  };
};
