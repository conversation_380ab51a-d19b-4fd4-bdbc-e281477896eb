import React, { act, Suspense } from 'react';
import { renderHook, waitFor } from '@testing-library/react';
import { QueryClientProvider } from '@tanstack/react-query';
import { testQueryClient } from '../../testUtils/RenderHelpers';
import { MemoryRouter } from 'react-router';
import { useGetPaymentMethods } from '../queries/useGetPaymentMethods';
import { vi } from 'vitest';
import { createPaymentMethodAPI } from '../../testUtils/MyPodsFactories';
import { mockGetPaymentMethods } from '../../../setupTests';
import { paymentMethodApiToDomain } from '../responseEntities/PaymentEntities';

describe('useGetPaymentMethods', () => {
  beforeEach(() => {
    mockGetPaymentMethods.mockReset();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  async function renderUseGetPaymentMethods() {
    return act(() => {
      return renderHook(() => useGetPaymentMethods(), {
        wrapper: (props) => (
          <QueryClientProvider client={testQueryClient()}>
            <Suspense fallback={<div>Loading (Test Render Promise Awaiting)...</div>}>
              <MemoryRouter>{props.children}</MemoryRouter>
            </Suspense>
          </QueryClientProvider>
        )
      });
    });
  }

  it('requests and returns payment methods for a customer', async () => {
    const getPaymentMethodValue = [createPaymentMethodAPI()];
    mockGetPaymentMethods.mockResolvedValue(getPaymentMethodValue);
    const domainPaymentMethods = getPaymentMethodValue.map((paymentMethod) =>
      paymentMethodApiToDomain(paymentMethod)
    );

    const paymentsResult = await renderUseGetPaymentMethods();

    await waitFor(() => {
      expect(paymentsResult.result.current.paymentMethods).toEqual(domainPaymentMethods);
    });
  });
});
