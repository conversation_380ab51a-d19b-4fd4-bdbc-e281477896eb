import { vi } from 'vitest';
import React, { act, Suspense } from 'react';
import { renderHook, waitFor } from '@testing-library/react';
import { useGetPaymentMethods } from '../queries/useGetPaymentMethods';
import { QueryClientProvider } from '@tanstack/react-query';
import { testQueryClient } from '../../testUtils/RenderHelpers';
import { MemoryRouter } from 'react-router';
import { createPaymentMethod, createPaymentMethodAPI } from '../../testUtils/MyPodsFactories';

vi.unmock('../MyPodsService');

const mock = vi.hoisted(() => ({
  get: vi.fn(),
  post: vi.fn(),
  patch: vi.fn()
}));

vi.mock('axios', async (importActual) => {
  const actual = await importActual<typeof import('axios')>();

  return {
    default: {
      ...actual.default,
      create: vi.fn(() => ({
        ...actual.default.create(),
        get: mock.get,
        post: mock.post,
        patch: mock.patch
      }))
    }
  };
});

async function renderUseGetPaymentMethods() {
  return act(() => {
    return renderHook(() => useGetPaymentMethods(), {
      wrapper: (props) => (
        <QueryClientProvider client={testQueryClient()}>
          <Suspense fallback={<div>Loading (Test Render Promise Awaiting)...</div>}>
            <MemoryRouter>{props.children}</MemoryRouter>
          </Suspense>
        </QueryClientProvider>
      )
    });
  });
}

describe('mypods service', () => {
  describe('get payment methods', () => {
    it('should parse correctly', async () => {
      mock.get.mockResolvedValue({ data: [createPaymentMethodAPI()] });

      const paymentsResult = await renderUseGetPaymentMethods();

      await waitFor(() => {
        expect(paymentsResult.result.current.paymentMethods).toEqual([createPaymentMethod()]);
      });
    });
  });
});
