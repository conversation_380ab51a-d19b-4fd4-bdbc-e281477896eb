import React, { act, Suspense } from 'react';
import { renderHook, waitFor } from '@testing-library/react';
import { QueryClientProvider } from '@tanstack/react-query';
import { MemoryRouter } from 'react-router';
import { testQueryClient } from '../../testUtils/RenderHelpers';
import {
  ordersChanged,
  convertOrders,
  useGetCustomerOrders
} from '../queries/useGetCustomerOrders';
import { mockGetCustomerOrders } from '../../../setupTests';
import { createOrderApi } from '../../testUtils/MyPodsFactories';
import { OrderAPI } from '../responseEntities/OrderAPIEntities';

describe('useGetCustomerOrders', () => {
  let orders: OrderAPI[];
  let ordersBeforeTheUpdate: Map<string, string>;

  beforeEach(() => {
    orders = [createOrderApi({ updatedDate: '2024-04-25T09:53:00.3731149' })];
    ordersBeforeTheUpdate = new Map<string, string>();
    convertOrders(orders, ordersBeforeTheUpdate);
  });

  const renderUseGetCustomerOrders = () => {
    return act(() => {
      return renderHook(() => useGetCustomerOrders(), {
        wrapper: (props) => (
          <QueryClientProvider client={testQueryClient()}>
            <Suspense fallback={<div>Loading (Test Render Promise Awaiting)...</div>}>
              <MemoryRouter>{props.children}</MemoryRouter>
            </Suspense>
          </QueryClientProvider>
        )
      });
    });
  };

  describe('refetchUntilTheOrdersUpdate', () => {
    const mockOrdersUpdated = vi.fn();
    const renderRefetchUntilTheOrdersUpdate = async () => {
      const customerOrders = await renderUseGetCustomerOrders();
      return customerOrders.result.current.refetchUntilTheOrdersUpdate;
    };

    it('if the orders updatedDate changes from the cache stop polling', async () => {
      mockGetCustomerOrders.mockResolvedValue(orders);
      orders[0].updatedDate = '2024-14-20T00:23:00.3531111';
      const refetchUntilTheContainersUpdate = await renderRefetchUntilTheOrdersUpdate();
      refetchUntilTheContainersUpdate(mockOrdersUpdated, ordersBeforeTheUpdate, 0);

      await waitFor(() => {
        expect(mockOrdersUpdated).toHaveBeenCalledWith(true);
      });
    });

    it('if the containers do not change and the number of tries exceed max tries stop polling', async () => {
      const maxTries = 3;
      mockGetCustomerOrders.mockResolvedValue(orders);
      const refetchUntilTheContainersUpdate = await renderRefetchUntilTheOrdersUpdate();
      refetchUntilTheContainersUpdate(
        mockOrdersUpdated,
        ordersBeforeTheUpdate,
        maxTries + 1,
        maxTries
      );

      await waitFor(() => {
        expect(mockOrdersUpdated).toHaveBeenCalledWith(true);
      });
    });
  });

  describe('ordersChanged', () => {
    it('returns false when the updatedDate changed', () => {
      expect(ordersChanged(ordersBeforeTheUpdate, orders)).toBe(false);
    });

    it('returns true when the containers have changed', () => {
      orders[0].updatedDate = '2024-14-20T00:23:00.3531111';

      expect(ordersChanged(ordersBeforeTheUpdate, orders)).toBe(true);
    });
  });
});
