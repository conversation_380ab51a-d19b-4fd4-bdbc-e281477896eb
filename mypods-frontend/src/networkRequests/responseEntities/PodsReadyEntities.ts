// -- Session --
import { SharedSessionClaims } from './AuthorizationEntities';

export type StartPodsReadySession = {
  token: string;
};

export type PodsReadyAccessTokenClaims = SharedSessionClaims & {
  email: string;
  hasPassword: boolean;
};

// -- Account --
export type PodsReadyCreateAccountRequest = {
  password: string;
};

export type PodsReadyCreateAccountResponse = {
  accountCreationStatus: PodsReadyCreateAccountStatus;
};

export enum PodsReadyCreateAccountStatus {
  SUCCESS = 'SUCCESS',
  USERNAME_ALREADY_TAKEN = 'USERNAME_ALREADY_TAKEN',
  CUSTOMER_ID_TAKEN = 'CUSTOMER_ID_TAKEN',
  INVALID_PASSWORD = 'INVALID_PASSWORD',
  ERROR = 'ERROR'
}

export type ShowPodsReadySingleOrderResponse = {
  isFetching: boolean;
  showPodsReadySingleOrder: boolean;
};

export type SingleOrderStatus = {
  service: ServiceType;
  origin: CityState;
  destination: CityState;
  scheduledDate?: string;
  orderId: string;
  containers: ContainerSummary[];
};

export type CityState = {
  city: string;
  state: string;
};

export type ServiceType = 'MOVING' | 'STORAGE';

export type ContainerSummary = {
  containerSize: string;
  count: number;
};
