/* eslint-disable @typescript-eslint/no-unused-vars */

export type OrderDocumentType =
  | RentalAgreementDocumentType
  | 'FRAGILE_AND_NON_PAVED_SURFACE_WAIVER'
  | 'ORDER_CONFIRMATION'
  | 'ORDER_UPDATE_CONFIRMATION'
  | 'MONTHLY_STATEMENT_OR_INVOICE'
  | 'PLACEMENT_WAIVER'
  | 'FORM_II_RC_159_SUPPLEMENTAL_DECLARATION'
  | 'UNKNOWN';

export type PoetDocumentStatus = 'SENT' | 'COMPLETED' | 'DECOMMISSIONED' | 'UNKNOWN';

export type CustomerDocumentType =
  | 'ACH_AUTOPAY_AUTHORIZATION_FORM'
  | 'ANCILLARY_ITEM_DOCUMENTATION'
  | 'AUCTION_PAPERWORK'
  | 'BAD_DEBT_PAPERWORK'
  | 'BANKRUPTCY_PAPERWORK'
  | 'BOOKING_CONFIRMATION'
  | 'CBP_FORM_7533_INWARD_CARGO'
  | 'CREDIT_CARD_AUTHORIZATION'
  | 'CREDIT_CHECK_FORM'
  | 'CREDIT_CARD_CHARGEBACKS'
  | 'CERTIFICATE_OF_INSURANCE'
  | 'CUSTOMER_INCIDENT_NOTE'
  | 'DAMAGE_WAIVERS_AND_RELEASES'
  | 'DAMAGE_PICTURES'
  | 'COPY_OF_DEATH_CERTIFICATE'
  | 'DEED_OR_LEASE_VISITOR'
  | 'DEFAULT_ON_PAYMENT_LETTER'
  | 'UNACCOMPANIED_ARTICLES'
  | 'ADD_A_GENERAL_FORM_TO_CAPTURE_OTHER_FORMS'
  | 'HOUSEHOLD_GOODS_INVENTORY_LIST'
  | 'IMAGE_FILE'
  | 'INVENTORY_ACKNOWLEDGEMENT_OF_HAZARDOUS_MATERIALS'
  | 'RETAIL_CUSTOMER_INVOICE'
  | 'LEGAL_CORRESPONDENCE'
  | 'LEGAL_STATUS'
  | 'CERTIFIED_NAME_CHANGE'
  | 'GENERAL_NOTE_WITH_FILE_ATTACHMENT'
  | 'GENERAL_NOTE_WITH_IMAGE_FILE_ATTACHMENT'
  | 'OCEAN_TRANSPORT_QUOTE'
  | 'COPY_OF_PASSPORTS'
  | 'ATTACH_PDF_FILE'
  | 'FORM_B4A_PERSONAL_EFFECTS_ACCOUNTING'
  | 'FORM_B4E_PERSONAL_EFFECTS_ACCOUNTING_DOCUMENT'
  | 'FORM_5291_CUSTOMS_POWER_OF_ATTORNEY'
  | 'PODS_CANADA_CUSTOMS_FORM'
  | 'PURPOSE_OF_THE_MOVE_AND_CONTACT_PHONE_NUMBER_DOCUMENT'
  | 'SETTLEMENT_AGREEMENT'
  | 'DEED_OR_LEASE_VACATION'
  | 'W9'
  | 'COPY_OF_WILL_AND_OR_TRUST'
  | 'MONTHLY_STATEMENT_OR_INVOICE'
  | 'UNKNOWN';

export const weightTicketDocumentTypes = [
  'MILITARY_WEIGHT_TICKET_EMPTY',
  'MILITARY_WEIGHT_TICKET_EMPTY_WITH_TRUCK',
  'MILITARY_WEIGHT_TICKET_FULL',
  'MILITARY_WEIGHT_TICKET_FULL_WITH_TRUCK'
];
type WeightTicketDocumentType = (typeof weightTicketDocumentTypes)[number];

export const rentalAgreementDocumentTypes = [
  'CONTRACT',
  'RENTAL_AGREEMENT',
  'IF_RENTAL_AGREEMENT',
  'LOCAL_RENTAL_AGREEMENT',
  'IF_RENTAL_AGREEMENT_ELECTRONIC_ACCEPTANCE',
  'LOCAL_RENTAL_AGREEMENT_ELECTRONIC_ACCEPTANCE',
  'MASTER_RENTAL_AGREEMENT'
];
type RentalAgreementDocumentType = (typeof rentalAgreementDocumentTypes)[number];

export const excludeFromDocPageTypes = [
  'SPONGY_MOTH_FORM',
  'SPOTTED_LANTERN_FLY_FORM',
  'MONTHLY_STATEMENT_OR_INVOICE'
];

export const mothFormDocumentTypes = ['SPONGY_MOTH_FORM', 'SPOTTED_LANTERN_FLY_FORM'];
type MothFormDocumentType = (typeof mothFormDocumentTypes)[number];
