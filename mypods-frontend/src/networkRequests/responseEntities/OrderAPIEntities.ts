import { isAfter, isToday, parseISO, toDate } from 'date-fns';
import {
  Container,
  MoveLeg,
  MoveLegAddress,
  MoveLegType,
  Order,
  OrderType,
  ScheduledStatus,
  ServiceCountdownType
} from '../../domain/OrderEntities';

export interface OrderAPI {
  orderId: string;
  quoteId: number;
  price: number;
  orderType: OrderType;
  containers: ContainerAPI[];
  orderDate: string;
  updatedDate: string;
  initialDeliveryPlacementIsReviewed: boolean;
}

export interface ContainerAPI {
  containerId: string;
  containerOrderId?: string;
  containerSize: string;
  moveLegs: MoveLegAPI[];
  upNextMoveLegId?: string;
}

export interface MoveLegAPI {
  moveLegId: string;
  moveLegType: MoveLegType;
  serviceCountdownType: ServiceCountdownType;
  scheduledDate?: string; // '2024-01-01' LocalDate
  eta?: string;
  siteIdentity: string;
  displayAddress: MoveLegAddress;
  originationAddress: MoveLegAddress;
  destinationAddress: MoveLegAddress;
  isCityService: boolean;
  isHawaii: boolean;
  isCrossBorder: boolean;
  isUpNext: boolean;
  isTransitLeg: boolean;
  isCurrentMoveLeg: boolean;
  transitDays: number;
  isSchedulableOnline: boolean;
  containerVisitDate?: string;
  firstAvailableDate: string; // '2024-01-01' LocalDate
  lastAvailableDate: string; // '2024-01-01' LocalDate
  arrivalDate?: string;
  moveDate?: string;
  scheduledStatus?: ScheduledStatus;
}

export interface LegacyUpdateMoveLegResponse {
  priceDifference?: string;
  quoteId?: number | string;
}

export interface UpdateMoveLegResponse {
  priceDifference?: string;
  quoteId?: string;
}

export interface ApplyQuoteToOrderResponse {
  sfQuoteId: string;
}

export const scheduledStatus = (scheduledDate: Date | undefined): ScheduledStatus => {
  if (!scheduledDate) return 'UNSCHEDULED';
  const jsDate = toDate(scheduledDate);
  return isToday(jsDate) || isAfter(jsDate, new Date()) ? 'FUTURE' : 'PAST';
};

export function moveLegApiToDomain(moveLeg: MoveLegAPI): MoveLeg {
  const scheduledDate = moveLeg.scheduledDate ? parseISO(moveLeg.scheduledDate) : undefined;
  const containerVisitDate = moveLeg.containerVisitDate
    ? parseISO(moveLeg.containerVisitDate)
    : undefined;
  return {
    ...moveLeg,
    scheduledDate,
    containerVisitDate,
    scheduledStatus: moveLeg.scheduledStatus ?? scheduledStatus(scheduledDate),
    firstAvailableDate: parseISO(moveLeg.firstAvailableDate),
    lastAvailableDate: parseISO(moveLeg.lastAvailableDate),
    arrivalDate: moveLeg.arrivalDate ? parseISO(moveLeg.arrivalDate) : undefined,
    moveDate: moveLeg.moveDate ? parseISO(moveLeg.moveDate) : undefined
  };
}

export function containerApiToDomain(container: ContainerAPI): Container {
  return {
    ...container,
    moveLegs: container.moveLegs.map((moveLeg) => moveLegApiToDomain(moveLeg))
  };
}

export function orderApiToDomain(order: OrderAPI): Order {
  return {
    ...order,
    containers: order.containers.map((container) => containerApiToDomain(container)),
    orderDate: parseISO(order.orderDate)
  };
}
