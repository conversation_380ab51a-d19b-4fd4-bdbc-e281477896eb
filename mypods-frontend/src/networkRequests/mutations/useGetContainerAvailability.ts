import { useMutation } from '@tanstack/react-query';
import { useMyPodsService } from '../MyPodsService';
import { MutationCacheKeys } from '../MutationCacheKeys';
import { ContainerAvailabilityRequest } from '../responseEntities/AvailabilityAPIEntities';
import { useLegacyMyPodsService } from '../legacy/LegacyMyPodsService';
import { Stack, useWhichStack } from '../../context/WhichStackContext';

export const useGetContainerAvailability = () => {
  const stack = useWhichStack();
  const poet = useMyPodsService();
  const legacy = useLegacyMyPodsService();

  const getContainerAvailability =
    stack === Stack.LEGACY ? legacy.getContainerAvailability : poet.getContainerAvailability;
  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.CONTAINER_AVAILABILITY_KEY],
    mutationFn: (request: ContainerAvailabilityRequest) => getContainerAvailability(request)
  });

  return {
    ...mutationResult
  };
};
