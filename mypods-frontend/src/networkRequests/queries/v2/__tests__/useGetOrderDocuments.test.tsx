import { ReactNode, Suspense } from 'react';
import { QueryClientProvider } from '@tanstack/react-query';
import { renderHook } from '@testing-library/react';
import { useGetOrderDocuments } from '../useGetOrderDocuments';
import { runPendingPromises, testQueryClient } from '../../../../testUtils/RenderHelpers';
import { createOrderDocument } from '../../../../testUtils/MyPodsFactories';
import {
  mockAddSignedRentalAgreements,
  mockGetOrderDocuments,
  mockRentalAgreementHasBeenSigned
} from '../../../../../setupTests';
import { OrderDocument } from '../../../../domain/DocumentEntities';
import { excludeFromDocPageTypes } from '../../../responseEntities/DocumentSharedSubtypes';

const SUSPENSE_FALLBACK = 'Loading Order Documents...';

describe('useGetOrderDocuments.tsx', () => {
  const orderConfirmation: OrderDocument = createOrderDocument({
    id: '1',
    docType: 'ORDER_CONFIRMATION'
  });
  const unsignedRentalAgreement: OrderDocument = createOrderDocument({
    id: '2a',
    docType: 'CONTRACT',
    docStatus: 'SENT'
  });
  const signedRentalAgreement: OrderDocument = createOrderDocument({
    id: '2b',
    docType: 'CONTRACT',
    docStatus: 'COMPLETED'
  });
  const signedRentalAgreementButNotInDaas: OrderDocument = createOrderDocument({
    id: '2c',
    docType: 'CONTRACT',
    docStatus: 'SENT'
  });
  const unsignedMothForm: OrderDocument = createOrderDocument({
    id: '3',
    docType: 'SPONGY_MOTH_FORM',
    docStatus: 'SENT'
  });
  const signedLanternFlyForm: OrderDocument = createOrderDocument({
    id: '4',
    docType: 'SPOTTED_LANTERN_FLY_FORM',
    docStatus: 'SENT'
  });
  const emptyWeightTicket: OrderDocument = createOrderDocument({
    id: '5a',
    docType: 'MILITARY_WEIGHT_TICKET_EMPTY',
    docStatus: 'SENT'
  });
  const filledWeightTicketWithTruck: OrderDocument = createOrderDocument({
    id: '5a',
    docType: 'MILITARY_WEIGHT_TICKET_FULL_WITH_TRUCK',
    docStatus: 'SENT'
  });
  const excludedDocument: OrderDocument = createOrderDocument({
    id: '6',
    docType: 'MONTHLY_STATEMENT_OR_INVOICE',
    docStatus: 'SENT'
  });

  const renderGetOrderDocuments = async () => {
    const wrapper = ({ children }: { children: ReactNode }) => (
      <QueryClientProvider client={testQueryClient()}>
        <Suspense fallback={SUSPENSE_FALLBACK}>{children}</Suspense>
      </QueryClientProvider>
    );
    const result = renderHook(() => useGetOrderDocuments(), { wrapper });
    await runPendingPromises();
    return result;
  };

  it('returns the query result', async () => {
    mockGetOrderDocuments.mockResolvedValue({ documents: [orderConfirmation] });
    const { result } = await renderGetOrderDocuments();

    expect(result.current.data.documents).toContain(orderConfirmation);
  });

  it('filters moth forms from the exported order documents', async () => {
    mockGetOrderDocuments.mockResolvedValue({
      documents: [orderConfirmation, unsignedMothForm, signedLanternFlyForm]
    });
    const { result } = await renderGetOrderDocuments();

    expect(result.current.data.documents).toContain(orderConfirmation);
    expect(result.current.data.documents).toContain(unsignedMothForm);
    expect(result.current.orderDocuments).not.toContain(unsignedMothForm);
    expect(result.current.orderDocuments).not.toContain(signedLanternFlyForm);
    expect(result.current.orderDocuments[0]).toEqual<OrderDocument>(orderConfirmation);
  });

  describe('rental agreements', () => {
    beforeEach(() => {
      mockGetOrderDocuments.mockResolvedValue({
        documents: [unsignedRentalAgreement, signedRentalAgreement]
      });
    });

    it('returns a list of unsigned rental agreements', async () => {
      const { result } = await renderGetOrderDocuments();

      expect(result.current.outstandingRentalAgreements).toHaveLength(1);
      expect(result.current.outstandingRentalAgreements[0]).toEqual(unsignedRentalAgreement);
    });

    it('updates the cache when a rental agreement has been signed', async () => {
      const { result, rerender } = await renderGetOrderDocuments();

      expect(result.current.outstandingRentalAgreements).toHaveLength(1);
      expect(result.current.outstandingRentalAgreements[0]).toEqual(unsignedRentalAgreement);

      result.current.markRentalAgreementAsCompleted(unsignedRentalAgreement.id);

      rerender();
      expect(result.current.outstandingRentalAgreements).toHaveLength(0);
    });

    describe('locally cache signed rental agreement', () => {
      beforeEach(() => {
        mockGetOrderDocuments.mockResolvedValue({
          documents: [signedRentalAgreementButNotInDaas]
        });
        mockRentalAgreementHasBeenSigned.mockReturnValue(true);
      });

      it('filters out previously signed rental agreements', async () => {
        const { result } = await renderGetOrderDocuments();

        expect(result.current.outstandingRentalAgreements).toHaveLength(0);
      });

      it('markRentalAgreementAsCompleted saves rental agreement as signed', async () => {
        const { result } = await renderGetOrderDocuments();

        result.current.markRentalAgreementAsCompleted(signedRentalAgreementButNotInDaas.id);

        expect(mockAddSignedRentalAgreements).toHaveBeenCalledWith([
          signedRentalAgreementButNotInDaas.id
        ]);
      });

      it('markAllRentalAgreementsAsCompleted saves rental agreement as signed', async () => {
        const { result } = await renderGetOrderDocuments();

        result.current.markAllRentalAgreementsAsCompleted([
          signedRentalAgreementButNotInDaas.id,
          unsignedRentalAgreement.id
        ]);

        expect(mockAddSignedRentalAgreements).toHaveBeenCalledWith([
          signedRentalAgreementButNotInDaas.id,
          unsignedRentalAgreement.id
        ]);
      });
    });

    test.each(excludeFromDocPageTypes)(
      'verify %s is not present in the filtered order documents',
      async (documentType) => {
        excludedDocument.docType = documentType;
        mockGetOrderDocuments.mockResolvedValue({
          documents: [signedRentalAgreement, excludedDocument]
        });

        const { result } = await renderGetOrderDocuments();

        expect(result.current.orderDocuments[0].docType).toMatch(signedRentalAgreement.docType);
        expect(
          result.current.orderDocuments?.find((x) => x.docType == excludedDocument.docType)
        ).toBeUndefined();
      }
    );
  });
  describe('weight tickets', () => {
    it('returns true when there are weight tickets, regardless of doc status', async () => {
      mockGetOrderDocuments.mockResolvedValue({
        documents: [emptyWeightTicket, filledWeightTicketWithTruck]
      });
      const { result } = await renderGetOrderDocuments();

      expect(result.current.hasWeightTickets).toBe(true);
    });

    it('returns false when there are no weight tickets, regardless of doc status', async () => {
      mockGetOrderDocuments.mockResolvedValue({
        documents: [orderConfirmation]
      });
      const { result } = await renderGetOrderDocuments();

      expect(result.current.hasWeightTickets).toBe(false);
    });
  });
});
