import { ReactNode, Suspense } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { renderHook } from '@testing-library/react';
import { useGetCustomerDocuments } from '../useGetCustomerDocuments';
import { runPendingPromises, testQueryClient } from '../../../../testUtils/RenderHelpers';
import {
  createCustomerDocumentAPI,
  createCustomerDocuments
} from '../../../../testUtils/MyPodsFactories';
import { mockGetCustomerDocuments } from '../../../../../setupTests';
import { CustomerDocuments } from '../../../responseEntities/DocumentApiEntities';
import {
  CustomerDocumentType,
  excludeFromDocPageTypes
} from '../../../responseEntities/DocumentSharedSubtypes';

// -- consts --
const SUSPENSE_FALLBACK = 'Loading Customer Documents...';

// -- tests --
describe('useGetCustomerDocuments', () => {
  let customerDocuments: CustomerDocuments;
  let queryClient: QueryClient;

  beforeEach(() => {
    customerDocuments = createCustomerDocuments({
      documents: [
        createCustomerDocumentAPI(),
        createCustomerDocumentAPI({ docType: 'MONTHLY_STATEMENT_OR_INVOICE' })
      ]
    });
    queryClient = testQueryClient();
  });

  const renderGetCustomerDocuments = () => {
    const wrapper = ({ children }: { children: ReactNode }) => (
      <QueryClientProvider client={queryClient}>
        <Suspense fallback={SUSPENSE_FALLBACK}>{children}</Suspense>
      </QueryClientProvider>
    );
    return renderHook(() => useGetCustomerDocuments(), { wrapper });
  };

  it('returns all the documents in customer documents', async () => {
    mockGetCustomerDocuments.mockResolvedValue({ documents: [customerDocuments] });
    const { result } = renderGetCustomerDocuments();

    await runPendingPromises();
    expect(result.current.data.documents).toContain(customerDocuments);
  });

  test.each(excludeFromDocPageTypes)(
    'verify %s is not present in the filtered customer documents',
    async (documentType) => {
      const nonExcludedDocument = createCustomerDocumentAPI({ docType: 'RETAIL_CUSTOMER_INVOICE' });
      const excludedDocument = createCustomerDocumentAPI({
        docType: documentType as CustomerDocumentType
      });

      mockGetCustomerDocuments.mockResolvedValue(
        createCustomerDocuments({
          documents: [nonExcludedDocument, excludedDocument]
        })
      );
      const { result } = renderGetCustomerDocuments();

      await runPendingPromises();
      expect(result.current.documentsPageDocuments[0].docType).toMatch(nonExcludedDocument.docType);
      expect(
        result.current.documentsPageDocuments?.find((x) => x.docType == excludedDocument.docType)
      ).toBeUndefined();
    }
  );

  it('Verify the filtered list When documents list are empty', async () => {
    mockGetCustomerDocuments.mockResolvedValue({ documents: [] });
    const { result } = renderGetCustomerDocuments();

    await runPendingPromises();
    expect(result.current.customerDocuments).toHaveLength(0);
    expect(result.current.documentsPageDocuments).toHaveLength(0);
  });
});
