import { useMyPodsService } from '../MyPodsService';
import { useSuspenseQuery } from '@tanstack/react-query';
import { QueryCacheKeys } from '../QueryCacheKeys';
import { SharedSessionClaims } from '../responseEntities/AuthorizationEntities';
import { useLocation } from 'react-router';
import { isPodsReadyLocation } from '../../helpers/podsReadyHelpers';

export const useSharedSession = () => {
  const { refreshSession, startPodsReadySession } = useMyPodsService();

  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const token = queryParams.get('token') || '';
  const startPodsReadySessionRequest = { token };

  const queryFunction: () => Promise<SharedSessionClaims> = isPodsReadyLocation(location)
    ? () => startPodsReadySession(startPodsReadySessionRequest)
    : () => refreshSession();

  const queryResult = useSuspenseQuery({
    queryKey: [QueryCacheKeys.REFRESH_KEY],
    queryFn: queryFunction
  });

  // Ensure that the error throws to the nearest boundary
  if (queryResult.error && !queryResult.isFetching) {
    throw queryResult.error;
  }
  const sessionClaims: SharedSessionClaims = { ...queryResult.data };

  return {
    ...queryResult,
    sessionClaims
  };
};
