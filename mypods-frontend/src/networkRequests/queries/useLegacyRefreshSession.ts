import { useSuspenseQuery } from '@tanstack/react-query';
import { useLocation } from 'react-router';
import { RefreshSessionClaims } from '../responseEntities/AuthorizationEntities';
import { QueryCacheKeys } from '../QueryCacheKeys';
import { PodsReadyAccessTokenClaims } from '../responseEntities/PodsReadyEntities';
import { isPodsReadyLocation } from '../../helpers/podsReadyHelpers';
import { useLegacyMyPodsService } from '../legacy/LegacyMyPodsService';

export const useLegacyRefreshSession = () => {
  const { refreshSession, startPodsReadySession } = useLegacyMyPodsService();

  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const token = queryParams.get('token') || '';
  const startPodsReadySessionRequest = { token };

  const queryFunction = isPodsReadyLocation(location)
    ? () => startPodsReadySession(startPodsReadySessionRequest)
    : () => refreshSession();

  const queryResult = useSuspenseQuery<PodsReadyAccessTokenClaims | RefreshSessionClaims>({
    queryKey: [QueryCacheKeys.REFRESH_KEY],
    queryFn: queryFunction
  });

  // Ensure that the error throws to the nearest boundary
  if (queryResult.error && !queryResult.isFetching) {
    throw queryResult.error;
  }
  const sessionClaims = { ...queryResult.data };

  return {
    ...queryResult,
    sessionClaims
  };
};
