import { useSuspenseQuery } from '@tanstack/react-query';
import { useMyPodsService } from '../MyPodsService';
import { RefreshSessionClaims } from '../responseEntities/AuthorizationEntities';
import { QueryCacheKeys } from '../QueryCacheKeys';

export const useRefreshSession = () => {
  const { refreshSession } = useMyPodsService();

  const queryResult = useSuspenseQuery({
    queryKey: [QueryCacheKeys.REFRESH_KEY],
    queryFn: refreshSession
  });

  // Ensure that the error throws to the nearest boundary
  if (queryResult.error && !queryResult.isFetching) {
    throw queryResult.error;
  }
  const sessionClaims: RefreshSessionClaims = { ...queryResult.data };

  return {
    ...queryResult,
    sessionClaims
  };
};
