import { useGtmEvents } from './google/useGtmEvents';
import { usePoetSplitEvents } from './usePoetSplitEvents';

export const useFileDownloadEvents = () => {
  const SPLIT_DOWNLOAD_EVENT_TYPE = 'pb_document_download';
  const gtmEvents = useGtmEvents();
  const { send } = usePoetSplitEvents();

  const sendDocumentDownloadedEvent = (documentType: string) => {
    gtmEvents.fileDownload(documentType);
    send(SPLIT_DOWNLOAD_EVENT_TYPE, 1, { documentType });
  };

  return {
    sendDocumentDownloadedEvent
  };
};
