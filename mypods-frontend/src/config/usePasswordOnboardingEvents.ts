import { SplitEventType } from './SplitEventTypes';
import { usePoetSplitEvents } from './usePoetSplitEvents';

export const usePasswordOnboardingEvents = () => {
  const { send } = usePoetSplitEvents();

  const sendPasswordOnboardingStart = () => {
    send(SplitEventType.PASSWORD_ONBOARDING_START);
  };

  const sendPasswordOnboardingComplete = () => {
    send(SplitEventType.PASSWORD_ONBOARDING_COMPLETE);
  };

  return {
    sendPasswordOnboardingStart,
    sendPasswordOnboardingComplete
  };
};
