import { useGtmEvents } from './google/useGtmEvents';
import { usePoetSplitEvents } from './usePoetSplitEvents';
import { GtmAccountDetailType } from './google/GoogleEntities';

export const useAccountUpdateEvents = () => {
  const START_EDITING_ACCOUNT_EVENT_TYPE = 'pb_start_edit_account_detail';
  const EDIT_ACCOUNT_SUCCESS_EVENT_TYPE = 'pb_success_account_detail';
  const gtmEvents = useGtmEvents();
  const { send } = usePoetSplitEvents();

  const sendStartEditingAccountDetailsEvent = (type: GtmAccountDetailType) => {
    send(START_EDITING_ACCOUNT_EVENT_TYPE, 1, { type });
    gtmEvents.startEditAccountDetail(type);
  };

  const sendEditAccountDetailsSuccessEvent = (type: GtmAccountDetailType) => {
    send(EDIT_ACCOUNT_SUCCESS_EVENT_TYPE, 1, { type });
    gtmEvents.successAccountDetail(type);
  };

  return {
    sendStartEditingAccountDetailsEvent,
    sendSubmitAccountDetailsEvent: gtmEvents.submitAccountDetail,
    sendEditAccountDetailsSuccessEvent,
    sendErrorEvent: gtmEvents.errorEvent
  };
};
