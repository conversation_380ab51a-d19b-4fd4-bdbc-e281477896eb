import { useGtmEvents } from './google/useGtmEvents';
import { usePoetSplitEvents } from './usePoetSplitEvents';

export const useCustomStatementEvents = () => {
  const GENERATE_STATEMENT_SUCCESS_EVENT_TYPE = 'pb_generate_statement_success';
  const gtmEvents = useGtmEvents();
  const { send } = usePoetSplitEvents();

  const sendGenerateStatementAttemptEvent = (startDate: string, endDate: string) => {
    gtmEvents.submitGenerateStatement(startDate, endDate);
  };

  const sendGenerateStatementSuccessEvent = (startDate: string, endDate: string) => {
    gtmEvents.submitGenerateStatement(startDate, endDate);
    send(GENERATE_STATEMENT_SUCCESS_EVENT_TYPE);
  };

  return {
    sendGenerateStatementAttemptEvent,
    sendGenerateStatementSuccessEvent
  };
};
