import { useGtmEvents } from './google/useGtmEvents';
import { usePoetSplitEvents } from './usePoetSplitEvents';
import { gtmCardType, GtmSubmitPaymentType } from './google/GoogleEntities';

export const usePaymentEvents = () => {
  const START_UPDATE_PAYMENT_EVENT_TYPE = 'pb_payment_method_update_start';
  const UPDATE_PAYMENT_SUCCESS_EVENT_TYPE = 'pb_payment_method_update_success';
  const START_SUBMIT_PAYMENT_EVENT_TYPE = 'pb_payment_start';
  const SUBMIT_PAYMENT_SUCCESS_EVENT_TYPE = 'pb_payment_success';
  const gtmEvents = useGtmEvents();
  const { send } = usePoetSplitEvents();

  const sendStartUpdatePaymentMethodEvent = (cardType: string) => {
    send(START_UPDATE_PAYMENT_EVENT_TYPE);
    gtmEvents.successAddPayment(gtmCardType(cardType));
  };

  const sendUpdatePaymentMethodSuccessEvent = (cardType: string) => {
    send(UPDATE_PAYMENT_SUCCESS_EVENT_TYPE);
    gtmEvents.submitAddPayment(gtmCardType(cardType));
  };

  const sendStartSubmitPaymentEvent = (paymentType: GtmSubmitPaymentType) => {
    send(START_SUBMIT_PAYMENT_EVENT_TYPE);
    gtmEvents.submitPayment(paymentType);
  };

  const sendSubmitPaymentSuccessEvent = (paymentType: GtmSubmitPaymentType) => {
    send(SUBMIT_PAYMENT_SUCCESS_EVENT_TYPE);
    gtmEvents.successPayment(paymentType);
  };

  return {
    sendStartUpdatePaymentMethodEvent,
    sendUpdatePaymentMethodSuccessEvent,
    sendStartSubmitPaymentEvent,
    sendSubmitPaymentSuccessEvent
  };
};
