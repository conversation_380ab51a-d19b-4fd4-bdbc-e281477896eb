import React, { ReactNode } from 'react';
import { css } from 'pods-component-library/styled-system/css';

type MapPinIconProps = {
  numberOfContainers: number;
  children: ReactNode;
};

export const MapPinIcon = ({ numberOfContainers, children }: MapPinIconProps) => (
  <div className={styles.container}>
    <svg width="28" height="32" viewBox="0 0 28 31" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M14 0C21.732 0 28 6.26801 28 14C28 20.9121 22.9906 26.6529 16.4043 27.793L14 31L11.5947 27.793C5.00892 26.6525 0 20.9118 0 14C0 6.26801 6.26801 0 14 0Z"
        fill="#083544"
      />
    </svg>
    {children}
    <div className={styles.numberOfContainers}>{numberOfContainers}</div>
  </div>
);

const styles = {
  container: css({
    position: 'relative',
    width: '28px',
    height: '31px'
  }),
  numberOfContainers: css({
    position: 'absolute',
    right: '-5px',
    top: '-5px',
    display: 'flex',
    paddingTop: '1px',
    paddingBottom: '1.5px',
    paddingX: '5px',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: '24px',
    backgroundColor: 'secondary500',
    color: 'neutral100',
    fontFamily: 'secondary',
    fontSize: '9px',
    fontStyle: 'normal',
    fontWeight: '600',
    lineHeight: '9x'
  })
};
