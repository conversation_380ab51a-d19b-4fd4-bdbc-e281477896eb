import React from 'react';
import { css } from 'pods-component-library/styled-system/css';
import { HouseLineIcon } from '@phosphor-icons/react';
import { MapPinIcon } from './MapPinIcon';

type HomeOnMapPinIconProps = {
  numberOfContainers: number;
};

export const HomeOnMapPinIcon = ({ numberOfContainers }: HomeOnMapPinIconProps) => (
  <MapPinIcon numberOfContainers={numberOfContainers}>
    <HouseLineIcon height={20} width={24} className={style} />
  </MapPinIcon>
);

const style = css({
  position: 'absolute',
  color: 'neutral100',
  left: '2px',
  top: '4px'
});
