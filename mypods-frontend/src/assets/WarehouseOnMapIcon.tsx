import React from 'react';
import { css } from 'pods-component-library/styled-system/css';
import { WarehouseIcon } from '@phosphor-icons/react';
import { MapPinIcon } from './MapPinIcon';

type WarehouseOnMapPinIconProps = {
  numberOfContainers: number;
};

export const WarehouseOnMapPinIcon = ({ numberOfContainers }: WarehouseOnMapPinIconProps) => (
  <MapPinIcon numberOfContainers={numberOfContainers}>
    <WarehouseIcon height={20} width={24} className={style} />
  </MapPinIcon>
);

const style = css({
  position: 'absolute',
  color: 'neutral100',
  left: '2px',
  top: '4px'
});
