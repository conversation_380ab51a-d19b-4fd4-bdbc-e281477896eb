import React, { useContext, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FormControl, Grid, InputLabel, MenuItem, Select } from '@mui/material';
import { v4 as uuidv4 } from 'uuid';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { NotificationContext } from '../../../components/notifications/NotificationContext';
import { MinimalPaymentCard } from './MinimalPaymentCard';
import { Design } from '../../../helpers/Design';
import { AddPaymentLinkCard } from './AddPaymentLinkCard';
import { useGetPaymentMethods } from '../../../networkRequests/queries/useGetPaymentMethods';

interface Props {
  onChange: (value: string) => void;
}

export const PaymentMethodSelect: React.FC<Props> = ({ onChange }: Props) => {
  const { t: translate } = useTranslation();
  const { setNotification } = useContext(NotificationContext);
  const containerRef = useRef<HTMLDivElement | null>(null);
  const [containerWidth, setContainerWidth] = useState(0);

  const { paymentMethods, error, isSuccess, isFetching: isLoading } = useGetPaymentMethods();

  // Using the containers ref to constrain the entire FormControl's maxWidth for long PayPal emails.
  // To truncate long emails ellipsis gets add in MinimalPaymentCard.
  useEffect(() => {
    const updateWidth = () => {
      if (containerRef.current) {
        setContainerWidth(containerRef.current.offsetWidth);
      }
    };
    updateWidth();
    window.addEventListener('resize', updateWidth);
    return () => {
      window.removeEventListener('resize', updateWidth);
    };
  }, []);

  const defaultPayment = paymentMethods.find((it) => it.isPrimary);

  const styles = style();

  useEffect(() => {
    if (error) {
      setNotification({
        message: translate(TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE),
        isError: true
      });
    }
  }, [error]);

  // TODO: Add skeletal loading on line 48

  return (
    <Grid container ref={containerRef} {...styles.container}>
      <FormControl sx={{ maxWidth: containerWidth ?? 'auto' }}>
        {isLoading && translate(TranslationKeys.MakePaymentsPage.SELECT_METHOD_LOADING_TEXT)}
        {isSuccess && paymentMethods.length > 0 && (
          <>
            <InputLabel id="payment-method-select" {...styles.inputLabel}>
              {translate(TranslationKeys.MakePaymentsPage.SELECT_LABEL)}
            </InputLabel>
            <Select
              labelId="payment-method-select"
              color="secondary"
              onChange={(event) => {
                onChange(event.target.value);
              }}
              defaultValue={defaultPayment?.paymentMethodId ?? 'NA'}>
              {paymentMethods.map((paymentMethod) => (
                <MenuItem
                  value={paymentMethod.paymentMethodId || 'NA'}
                  key={paymentMethod.paymentMethodId || uuidv4()}
                  {...styles.menuItem}>
                  <MinimalPaymentCard paymentMethod={paymentMethod} />
                </MenuItem>
              ))}
              <MenuItem value="NA" key={uuidv4()} {...styles.menuItem}>
                <AddPaymentLinkCard />
              </MenuItem>
            </Select>
          </>
        )}
        {isSuccess && !paymentMethods.length && <AddPaymentLinkCard />}
      </FormControl>
    </Grid>
  );
};

// -- styles --
const style = () => ({
  container: {
    sx: {
      flexDirection: 'column'
    }
  },
  inputLabel: {
    sx: { backgroundColor: 'white', paddingX: '4px' }
  },
  menuItem: {
    sx: {
      '&.Mui-selected, &.Mui-selected:active': { backgroundColor: Design.Alias.Color.secondary100 }
    }
  }
});
