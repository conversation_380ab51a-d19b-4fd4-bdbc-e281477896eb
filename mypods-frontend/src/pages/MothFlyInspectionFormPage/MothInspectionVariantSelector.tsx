import React, { useContext, useEffect } from 'react';
import { useImmer } from 'use-immer';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate } from 'react-router';
import isEmpty from 'lodash/isEmpty';
import { ROUTES } from '../../Routes';
import { PageLayout } from '../../components/PageLayout';
import { getTimezoneOffset } from '../../helpers/dateHelpers';
import { PoetPaginatedMothFlyInspectionForm } from './Paginated/PaginatedMothFlyInspectionForm';
import { MothCategoryState, MothCheckboxConfig } from './MothInspectionFormTypes';
import { mothFormConfig } from './mothInspectionConfig';
import { ViewAgreementSteps } from '../../config/google/GoogleEntities';
import { useGtmEventsWithCustomer } from '../../config/google/useGtmEvents';
import { addSignedMothOrderId } from '../../helpers/entrypointHelpers';
import { MOTH_PAGINATION_ENABLED, useFeatureFlags } from '../../helpers/useFeatureFlags';
import { useGetOrderDocuments } from '../../networkRequests/queries/v2/useGetOrderDocuments';
import { useSignMothAgreement } from '../../networkRequests/mutations/useSignMothAgreement';
import { useSplitEvents } from '../../config/useSplitEvents';
import { NonPaginatedMothContainer } from './NonPaginatedMothContainer';
import { TranslationKeys } from '../../locales/TranslationKeys';
import {
  OrderDocumentAPI,
  SignMothAgreementRequest
} from '../../networkRequests/responseEntities/DocumentApiEntities';
import { useGetCustomer } from '../../networkRequests/queries/useGetCustomer';
import { formatAddress } from '../../networkRequests/responseEntities/CustomerEntities';
import { Order } from '../../domain/OrderEntities';
import { useGetCustomerOrders } from '../../networkRequests/queries/useGetCustomerOrders';
import { NotificationContext } from '../../components/notifications/NotificationContext';
import { SplitEventType } from '../../config/SplitEventTypes';

const Tx = TranslationKeys.MothInspectionFormPage;
const OTHER_CHECKBOX_LABEL = 'Other';

export const MothInspectionVariantSelector = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { customer } = useGetCustomer();
  const { t: translate } = useTranslation();
  const { customerOrders } = useGetCustomerOrders();
  const { setNotification } = useContext(NotificationContext);
  const { outstandingRentalAgreements, outstandingMothAgreements } = useGetOrderDocuments();
  const splitEvents = useSplitEvents(customer.id);
  const gtmEvents = useGtmEventsWithCustomer(customer);
  const { isMothPaginationEnabled } = useFeatureFlags([MOTH_PAGINATION_ENABLED]);
  const signMothAgreement = useSignMothAgreement();
  const [checkboxState, setCheckboxState] = useImmer<MothCheckboxConfig>(mothFormConfig);
  const [firstOutstandingMothAgreement] = outstandingMothAgreements;

  useEffect(() => {
    splitEvents.send(SplitEventType.MOTH_FORM_START);
    gtmEvents.viewAgreementStep(
      ViewAgreementSteps.moth.form,
      firstOutstandingMothAgreement.orderId
    );
  }, []);

  const findFirstMoveLegsOfTypeInitialDelivery = () => {
    const currentOrder = customerOrders.find(
      (order: Order) => order.orderId === firstOutstandingMothAgreement.orderId
    );
    return currentOrder?.containers?.[0].moveLegs.find(
      (moveLeg) => moveLeg.moveLegType === 'INITIAL_DELIVERY'
    );
  };

  const formattedInspectionAddress = () => {
    const firstMoveLeg = findFirstMoveLegsOfTypeInitialDelivery();
    if (firstMoveLeg) {
      return formatAddress(firstMoveLeg.displayAddress);
    }
    return formatAddress(customer.billingAddress);
  };

  const handleSubmit = () => {
    const { selectedBoxes, otherTextBoxes }: { [k: string]: any[] } = getSelectedCheckboxes();
    const request: SignMothAgreementRequest = {
      orderId: firstOutstandingMothAgreement.orderId,
      firstName: customer.firstName,
      lastName: customer.lastName,
      email: customer.email?.address ?? '',
      phone: customer.primaryPhone?.number ?? customer.secondaryPhone?.number ?? '',
      address: formattedInspectionAddress(),
      selectedCheckboxes: selectedBoxes,
      otherCheckboxes: otherTextBoxes,
      ...getTimezoneOffset()
    };
    gtmEvents.submitAgreement('moth_fly_inventory', request.orderId);
    splitEvents.send(SplitEventType.MOTH_FORM_SUBMIT);
    signMothAgreement.mutate(request, {
      onSuccess: () => {
        splitEvents.send(SplitEventType.MOTH_FORM_SUCCESS);
        gtmEvents.successAgreement('moth_fly_inventory', request.orderId);
        setNotification({
          isError: false,
          message: translate(Tx.SUCCESS_MESSAGE)
        });
        const remainingMothAgreements = outstandingMothAgreements?.filter(
          (current: OrderDocumentAPI) => current.id !== firstOutstandingMothAgreement.id
        );
        if (isEmpty(remainingMothAgreements) && isEmpty(outstandingRentalAgreements)) {
          splitEvents.send(SplitEventType.PODS_READY_COMPLETE);
        }
        addSignedMothOrderId(request.orderId);
        navigate(location.state?.onSuccessRoute ?? ROUTES.HOME, { replace: true });
      },
      onError: () => {
        splitEvents.send(SplitEventType.MOTH_FORM_ERROR);
        setNotification({
          isError: true,
          message: translate(TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE)
        });
      }
    });
  };

  const getSelectedCheckboxes = () =>
    checkboxState.reduce(
      (selections: { [k: string]: any[] }, element: MothCategoryState) => {
        element.subcategories.forEach((subCategories) => {
          subCategories.checkboxes.forEach((checkbox) => {
            if (!checkbox.checked) {
              return;
            }

            if (checkbox.label === OTHER_CHECKBOX_LABEL) {
              selections.otherTextBoxes.push({
                id: checkbox.id,
                description: checkbox.description ?? ''
              });
            } else {
              selections.selectedBoxes.push(checkbox.id);
            }
          });
        });
        return selections;
      },
      { selectedBoxes: [], otherTextBoxes: [] }
    );

  return (
    <PageLayout columnsLg={9}>
      {isMothPaginationEnabled() ? (
        <PoetPaginatedMothFlyInspectionForm
          checkboxState={checkboxState}
          handleSubmit={handleSubmit}
          setCheckboxState={setCheckboxState}
          orderId={firstOutstandingMothAgreement.orderId}
          isPending={signMothAgreement.isPending}
          customer={customer}
        />
      ) : (
        <NonPaginatedMothContainer
          checkboxState={checkboxState}
          setCheckboxState={setCheckboxState}
          handleSubmit={handleSubmit}
          firstOutstandingMothAgreementOrderId={firstOutstandingMothAgreement.orderId}
          isPending={signMothAgreement.isPending}
          customer={customer}
        />
      )}
    </PageLayout>
  );
};
