import React from 'react';
import { Grid, <PERSON>, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { Design } from '../../../helpers/Design';

interface Props {
  handleLearnMoreClick: () => void;
}

export const IntroSection = ({ handleLearnMoreClick }: Props) => {
  const { t: translate } = useTranslation();

  return (
    <Grid container flexDirection="column" {...introStyles.introContainer}>
      <Grid {...introStyles.introBody}>
        <Typography {...introStyles.introTitle}>
          {translate(TranslationKeys.MothInspectionFormPage.INTRO_TITLE)}
        </Typography>
        <Typography {...introStyles.introDesc}>
          {translate(TranslationKeys.MothInspectionFormPage.INTRO_DESC)}
          <Link
            underline="none"
            onClick={() => {
              handleLearnMoreClick();
            }}
            sx={{
              color: Design.Alias.Color.secondary500,
              cursor: 'pointer'
            }}>
            &nbsp;{translate(TranslationKeys.MothInspectionFormPage.INTRO_LEARN_MORE)}
          </Link>
        </Typography>
        <Typography {...introStyles.introInfo}>
          {translate(TranslationKeys.MothInspectionFormPage.INTRO_INFO)}
        </Typography>
      </Grid>
    </Grid>
  );
};

// -- styles --
const introStyles = {
  introContainer: {
    sx: {
      padding: '32px',
      backgroundColor: Design.Alias.Color.neutral100,
      border: `1px solid ${Design.Alias.Color.neutral300}`,
      borderRadius: '8px',
      boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.18)',
      flex: 'none',
      order: 1,
      alignSelf: 'stretch',
      flexGrow: 0
    }
  },
  introBody: {
    sx: {
      gap: Design.Primitives.Spacing.xs,
      flex: 'none',
      alignSelf: 'stretch',
      flexGrow: 0,
      flexDirection: 'column',
      display: 'flex'
    }
  },
  introTitle: {
    sx: {
      fontSize: Design.Primitives.Font.Size.Heading.Desktop.md,
      fontWeight: Design.Primitives.Font.Weight.bold,
      color: Design.Alias.Color.accent900
    }
  },
  introDesc: {
    sx: {
      fontSize: Design.Primitives.Font.Size.BodyUniversal.md,
      ontWeight: Design.Primitives.Font.Weight.regular,
      color: Design.Alias.Color.neutral800
    }
  },
  introInfo: {
    sx: {
      fontSize: Design.Primitives.Font.Size.BodyUniversal.md,
      fontWeight: Design.Primitives.Font.Weight.bold,
      color: Design.Alias.Color.neutral700
    }
  },
  learnMoreButton: {
    sx: {
      variant: 'text',
      cursor: 'pointer',
      color: Design.Primitives.Color.Blue.lightOasis
    }
  }
};
