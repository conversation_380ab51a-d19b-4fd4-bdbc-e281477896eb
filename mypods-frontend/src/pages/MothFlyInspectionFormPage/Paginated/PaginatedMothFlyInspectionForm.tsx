import { Dialog, Grid, useMediaQuery } from '@mui/material';
import React, { useEffect, useState } from 'react';
import { Updater } from 'use-immer';
import Button from '@mui/material/Button';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router';
import { MothCategoryState, MothCheckboxConfig } from '../MothInspectionFormTypes';
import { ViewAgreementSteps } from '../../../config/google/GoogleEntities';
import { useLegacyGtmEvents } from '../../../config/google/useGtmEvents';
import { PaginatedLegalSection } from './PaginatedLegalSection';
import { MothInspectionLearnMore } from '../../MothInspectionPage/MothInspectionLearnMore';
import { MothFlyInspectionSubSection } from './MothFlyInspectionSubSection';
import { BackHyperLink } from '../../../components/buttons/BackHyperLink';
import { PaginatedMothButtonGrid } from './PaginatedMothButtonGrid';
import { StepTracker } from './StepTracker';
import { IntroSection } from './IntroSection';
import { isNonProdEnv } from '../../DebugPage/DebugPage';
import { AddSignatureButtonCustomer } from '../../../components/buttons/AddSignature';
import { theme } from '../../../PodsTheme';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { isPodsReadyLocation } from '../../../helpers/podsReadyHelpers';
import { Customer } from '../../../networkRequests/responseEntities/CustomerEntities';

const Tx = TranslationKeys;

interface Props {
  handleSubmit: () => void;
  checkboxState: MothCheckboxConfig;
  setCheckboxState: Updater<MothCheckboxConfig>;
  orderId: string;
  isPending: boolean;
  customer: Customer;
}

export const LegacyPaginatedMothFlyInspectionForm = ({
  handleSubmit,
  checkboxState,
  setCheckboxState,
  orderId,
  isPending,
  customer
}: Props) => (
  <PaginatedMothFlyInspectionForm
    handleSubmit={handleSubmit}
    checkboxState={checkboxState}
    setCheckboxState={setCheckboxState}
    isPending={isPending}
    orderId={orderId}
    customer={customer}
  />
);

export const PoetPaginatedMothFlyInspectionForm = ({
  handleSubmit,
  checkboxState,
  setCheckboxState,
  orderId,
  isPending,
  customer
}: Props) => (
  <PaginatedMothFlyInspectionForm
    handleSubmit={handleSubmit}
    checkboxState={checkboxState}
    setCheckboxState={setCheckboxState}
    isPending={isPending}
    orderId={orderId}
    customer={customer}
  />
);

interface FormProps {
  handleSubmit: () => void;
  checkboxState: MothCheckboxConfig;
  setCheckboxState: Updater<MothCheckboxConfig>;
  orderId: string;
  isPending: boolean;
  customer: Customer;
}

export const PaginatedMothFlyInspectionForm = ({
  handleSubmit,
  checkboxState,
  setCheckboxState,
  orderId,
  isPending,
  customer
}: FormProps) => {
  const [isSigned, setIsSigned] = useState(false);
  const { t: translate } = useTranslation();
  const location = useLocation();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = mothStyles(isMobile);
  const gtmEvents = useLegacyGtmEvents();
  const [showLearnMore, setShowLearnMore] = useState(false);
  // total steps count includes checkbox steps and signature page
  const totalSteps = checkboxState.length;
  const [activeStep, setActiveStep] = useState(0);
  const topOfPage = React.createRef<HTMLDivElement>();
  const isOnFirstStep = activeStep === 0;
  const isOnLastStep = activeStep === totalSteps;

  const linkLabel = isPodsReadyLocation(location)
    ? translate(Tx.Onboarding.RETURN_TO_TASK_BUTTON)
    : translate(Tx.Navigation.HOME);

  const handleNext = () => {
    const newStep = activeStep + 1;
    if (newStep <= totalSteps) {
      setActiveStep(newStep);
    }
  };

  const handlePrevious = () => {
    if (activeStep === totalSteps) setIsSigned(false);
    const newStep = activeStep - 1;
    if (newStep >= 0) {
      setActiveStep(newStep);
    }
  };

  useEffect(() => {
    topOfPage?.current?.scrollIntoView({ behavior: 'smooth' });
  }, [activeStep]);

  useEffect(() => {
    gtmEvents.viewAgreementStep(ViewAgreementSteps.moth.form, orderId);
  }, []);

  const selectAll = () => {
    checkboxState.forEach((category: MothCategoryState, categoryIndex) => {
      category.subcategories.forEach((subCategories, subcategoryIndex) => {
        subCategories.checkboxes.forEach((_, checkboxIndex) => {
          handleCheckboxChange(categoryIndex, subcategoryIndex, checkboxIndex);
        });
      });
    });
  };

  const hasRestrictedItems = !!checkboxState.find(
    (category: MothCategoryState) =>
      !!category.subcategories.find(
        (subcategory) =>
          !!subcategory.checkboxes.find((checkbox) => checkbox.checked && checkbox.restricted)
      )
  );

  const handleLearnMoreClick = () => {
    setShowLearnMore(true);
  };
  const handleOnClosed = () => {
    setShowLearnMore(false);
  };

  const handleCheckboxChange = (
    categoryIndex: number,
    subcategoryIndex: number,
    checkboxIndex: number
  ) => {
    setCheckboxState((prev) => {
      const checkboxToUpdate =
        prev[categoryIndex].subcategories[subcategoryIndex].checkboxes[checkboxIndex];
      checkboxToUpdate.checked = !checkboxToUpdate.checked;
    });
  };

  return (
    <Grid data-testid="moth-form-paginated" ref={topOfPage} container gap={6} {...styles.mainGrid}>
      {isOnFirstStep && (
        <Grid container gap={3}>
          <BackHyperLink route={-1} label={linkLabel} />
          <IntroSection handleLearnMoreClick={handleLearnMoreClick} />
        </Grid>
      )}

      {isNonProdEnv() && (
        <Grid>
          <Button variant="contained" color="secondary" onClick={selectAll}>
            Select All
          </Button>
        </Grid>
      )}

      <Grid container flexDirection="column" gap={3}>
        <StepTracker activeStep={activeStep} totalSteps={totalSteps}></StepTracker>

        {!isOnLastStep && (
          <Grid role="tabpanel" key={checkboxState[activeStep].titleKey}>
            <MothFlyInspectionSubSection
              category={checkboxState[activeStep]}
              titleIndex={activeStep}
              checkboxState={checkboxState}
              setCheckboxState={setCheckboxState}
            />
          </Grid>
        )}

        {isOnLastStep && (
          <Grid container>
            <PaginatedLegalSection isRestrictedSelected={hasRestrictedItems}>
              <AddSignatureButtonCustomer
                isSigned={isSigned}
                handleSignClicked={() => {
                  setIsSigned(true);
                }}
                isDisabled={hasRestrictedItems}
                customer={customer}
              />
            </PaginatedLegalSection>
          </Grid>
        )}

        <PaginatedMothButtonGrid
          isFirstStep={isOnFirstStep}
          isLastStep={isOnLastStep}
          isPending={isPending}
          isSigned={isSigned}
          hasRestrictedItems={hasRestrictedItems}
          handlePrevious={handlePrevious}
          handleNext={handleNext}
          handleSubmit={handleSubmit}
        />
      </Grid>

      <Dialog
        PaperProps={{ ...styles.dialogProps }}
        open={showLearnMore}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description">
        <MothInspectionLearnMore handleOnClosed={handleOnClosed} />
      </Dialog>
    </Grid>
  );
};

const mothStyles = (isMobile: boolean) => {
  const marginSides = isMobile ? '16px' : '32px';
  return {
    mainGrid: {
      sx: {
        padding: '32px 0px'
      }
    },
    dialogProps: {
      sx: {
        borderRadius: '15px',
        padding: isMobile ? '0px' : '20px',
        transition: 'none',
        margin: `0px ${marginSides}`,
        maxHeight: isMobile ? 'calc(100% - 32px)' : 'calc(100% - 64px)'
      }
    }
  };
};
