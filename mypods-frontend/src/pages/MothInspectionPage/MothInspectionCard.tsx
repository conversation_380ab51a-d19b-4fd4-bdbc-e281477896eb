import React from 'react';
import { Grid, Typography, useMediaQuery } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { theme } from '../../PodsTheme';
import { TranslationKeys } from '../../locales/TranslationKeys';
import { CalendarIllustration } from './images/CalendarIllustration';
import { HawaiiIllustration } from './images/HawaiiIllustration';
import { FormIllustration } from './images/FormIllustration';
import { Design } from '../../helpers/Design';

// -- types --
interface Props {
  cardType: InspectionCardType;
}

type InspectionCardType = 'hawaii' | 'fiveDays' | 'dontRiskIt';

const byInspectionCardType = <T,>(
  cardType: InspectionCardType,
  options: { hawaii: T; fiveDays: T; dontRiskIt: T }
): T => options[cardType];

// -- constants --
const Tx = TranslationKeys.MothInspectionPage.Cards;

// -- impls --
export const MothInspectionCard: React.FC<Props> = ({ cardType }: Props) => {
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { t: translate } = useTranslation();

  const styles = cardStyles(isMobile);

  return (
    <Grid container {...styles.card}>
      <Grid item {...styles.imageContainer}>
        {byInspectionCardType(cardType, {
          fiveDays: <CalendarIllustration {...styles.image} />,
          hawaii: <HawaiiIllustration {...styles.image} />,
          dontRiskIt: <FormIllustration {...styles.image} />
        })}
      </Grid>
      <Grid
        item
        container
        direction="column"
        alignItems="stretch"
        {...(!isMobile && { textAlign: 'center' })}
        {...styles.textContainer}>
        <Grid item {...styles.titleContainer}>
          <Typography {...styles.title}>{translate(Tx.TITLE, { context: cardType })}</Typography>
        </Grid>
        <Grid item {...styles.bodyContainer}>
          <Typography {...styles.body}>{translate(Tx.BODY_TEXT, { context: cardType })}</Typography>
        </Grid>
      </Grid>
    </Grid>
  );
};

// -- styles --
const cardStyles = (isMobile: boolean) => ({
  card: {
    sx: {
      gap: isMobile ? Design.Primitives.Spacing.sm : Design.Primitives.Spacing.md,
      ...(!isMobile && { justifyContent: 'center' })
    }
  },
  imageContainer: {
    sx: {
      display: 'flex',
      maxWidth: 'fit-content',
      ...(isMobile && { gap: Design.Primitives.Spacing.lg })
    }
  },
  image: {
    sx: {
      height: isMobile ? '60px' : '96px',
      width: isMobile ? '60px' : '96px'
    }
  },
  textContainer: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      height: '100%',
      ...(isMobile && { alignItems: 'flex-start' }),
      ...(isMobile && { flex: '1' })
    }
  },
  titleContainer: {
    sx: {
      display: 'flex',
      alignItems: 'start',
      justifyContent: 'center',
      flex: '.25'
    }
  },
  title: {
    sx: {
      ...(isMobile ? Design.Alias.Text.Heading.Mobile.Sm : Design.Alias.Text.Heading.Desktop.Xs),
      color: Design.Alias.Color.accent900
    }
  },
  bodyContainer: {
    sx: {
      display: 'flex',
      alignItems: 'start',
      justifyContent: 'center',
      flex: '1'
    }
  },
  body: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.Sm,
      color: Design.Alias.Color.neutral700
    }
  }
});
