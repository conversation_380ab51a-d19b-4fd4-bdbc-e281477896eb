import { Grid, Link, useMediaQuery } from '@mui/material';
import React from 'react';
import { useNavigate } from 'react-router';
import { Design } from '../../helpers/Design';
import { ROUTES } from '../../Routes';
import { Txt } from '../../components/Txt';
import { TranslationKeys } from '../../locales/TranslationKeys';
import { theme } from '../../PodsTheme';

const Tx = TranslationKeys.ManagePaymentMethodsPage;

export const NoBillingAddressFound: React.FC = () => {
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const navigate = useNavigate();
  const styles = noBillingAddressStyles(isMobile);

  const handleOnClick = () => {
    navigate(ROUTES.ACCOUNT);
  };
  return (
    <Grid container direction="column" gap={2}>
      <Grid item color={Design.Alias.Color.primary100}>
        <Txt i18nKey={Tx.NO_BILLING_ADDRESS_FOUND} {...styles.title}></Txt>
      </Grid>
      <Grid item color={Design.Alias.Color.secondary100}>
        <Txt
          i18nKey={Tx.GO_TO_ACCOUNT_PAGE}
          components={{
            url: (
              <Link
                key="billing-address-link"
                onClick={handleOnClick}
                target="_blank"
                aria-label={Tx.GO_TO_ACCOUNT_PAGE_LABEL}
                {...styles.linkedText}
              />
            )
          }}
          {...styles.subtitle}></Txt>
      </Grid>
    </Grid>
  );
};

const noBillingAddressStyles = (isMobile: boolean) => ({
  linkedText: {
    sx: {
      fontWeight: 700,
      color: Design.Alias.Color.secondary500,
      textDecorationColor: Design.Alias.Color.secondary500,
      cursor: 'pointer'
    }
  },
  title: {
    sx: {
      ...(isMobile ? Design.Alias.Text.Heading.Mobile.Xxl : Design.Alias.Text.Heading.Desktop.Xxl)
    }
  },
  subtitle: {
    sx: {
      ...(isMobile ? Design.Alias.Text.Heading.Mobile.Md : Design.Alias.Text.Heading.Desktop.Md),
      color: Design.Alias.Color.accent900
    }
  }
});
