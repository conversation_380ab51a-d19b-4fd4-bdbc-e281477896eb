import { Grid } from '@mui/material';
import { Button } from 'pods-component-library';
import { css } from 'pods-component-library/styled-system/css';
import React from 'react';

export const SharedLibraryDemo = () => (
  <Grid item>
    <Button variant="filled" color="primary" buttonSize="large">
      Shared Filled Button
    </Button>
    <Button variant="outlined" color="primary" buttonSize="medium" css={stylesheet.button}>
      Shared Outlined Button
    </Button>
    <Button variant="filled" buttonSize="small" color="secondary" css={stylesheet.button}>
      Custom Blue Button
    </Button>
  </Grid>
);

const stylesheet = {
  button: css.raw({
    marginTop: '1rem'
  })
};
