import React, { useContext, useState } from 'react';
import {
  ButtonBase,
  Grid,
  styled,
  Tooltip,
  tooltipClasses,
  TooltipProps,
  Typography,
  useMediaQuery
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import { format } from 'date-fns';
import { Design } from '../../helpers/Design';
import { TranslationKeys } from '../../locales/TranslationKeys';
import {
  PaymentMethod,
  SetDefaultPaymentMethodRequest
} from '../../networkRequests/responseEntities/PaymentEntities';
import { CardIcon, PaymentTypeIcon } from '../../components/icons/PaymentTypeIcon';
import { theme } from '../../PodsTheme';
import { useSetDefaultPaymentMethod } from '../../networkRequests/mutations/useSetDefaultPaymentMethod';
import { NotificationContext } from '../../components/notifications/NotificationContext';
import { InfoIcon } from '../../components/icons/InfoIcon';
import { useGtmEvents } from '../../config/google/useGtmEvents';
import { gtmDefaultPaymentCardType } from '../../config/google/GoogleEntities';
import { MakeDefaultButton } from './MakeDefaultButton';
import { useGetPaymentMethods } from '../../networkRequests/queries/useGetPaymentMethods';

const LightTooltip = styled(({ className, ...props }: TooltipProps) => (
  <Tooltip {...props} classes={{ popper: className }} />
))(() => ({
  [`& .${tooltipClasses.tooltip}`]: {
    color: Design.Alias.Color.neutral100,
    textAlign: 'center',
    ...Design.Alias.Text.BodyUniversal.Sm
  }
}));

// -- types --
interface Props {
  paymentMethod: PaymentMethod;
  hasPrimaryLoan: boolean;
  hasPrimaryCitiBank: boolean;
  openCallModal: () => void;
}

// -- impls --
export const PaymentMethodCard: React.FC<Props> = ({
  paymentMethod,
  hasPrimaryLoan,
  hasPrimaryCitiBank,
  openCallModal
}: Props) => {
  const { t: translate } = useTranslation();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const setDefaultPaymentMethod = useSetDefaultPaymentMethod();
  const { refetch: refetchPayments } = useGetPaymentMethods();
  const { setNotification } = useContext(NotificationContext);
  const styles = style(isMobile);
  const isPayPal = paymentMethod.cardType === CardIcon.PAYPAL;
  const isLineOfCredit = paymentMethod.cardType === CardIcon.LINE_OF_CREDIT;
  const [tooltipIsOpen, setTooltipIsOpen] = useState(false);
  const gtmEvents = useGtmEvents();

  const handleMakeDefault = () => {
    if (!paymentMethod.paymentMethodId) {
      setNotification({
        message: translate(TranslationKeys.PaymentMethodsPage.Messages.PAYMENT_METHOD_ISSUE),
        isError: true
      });
      return;
    }
    const request: SetDefaultPaymentMethodRequest = {
      paymentMethodId: paymentMethod.paymentMethodId,
      paymentMethodType: 'CREDIT_CARD'
    };
    const gtmCardType = gtmDefaultPaymentCardType(paymentMethod.cardType);
    gtmEvents.submitSetDefaultPayment(gtmCardType);
    setDefaultPaymentMethod.mutate(request, {
      onSuccess: () => {
        gtmEvents.successSetDefaultPayment(gtmCardType);
        refetchPayments();
        setNotification({
          message: translate(TranslationKeys.PaymentMethodsPage.Messages.SUCCESS),
          isError: false
        });
      },
      onError: () => {
        setNotification({
          message: translate(TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE),
          isError: true
        });
      }
    });
  };

  const renderPaymentDetails = ({
    accountId,
    cardNumberLastFourDigits,
    cardExpirationDate
  }: PaymentMethod) => {
    if (isLineOfCredit) {
      return undefined;
    }
    if (isPayPal) {
      return (
        <Grid item {...styles.textContainer}>
          <Typography>{accountId}</Typography>
        </Grid>
      );
    }

    return (
      <Grid item {...styles.textContainer}>
        <Typography>
          {translate(TranslationKeys.PaymentMethodsPage.PaymentType.CREDIT_CARD)}...
          {cardNumberLastFourDigits}
        </Typography>
        <Typography {...styles.separator}>|</Typography>
        <Typography>Exp. {format(cardExpirationDate, 'MM/yy')}</Typography>
      </Grid>
    );
  };

  const renderActionSection = () => {
    if (hasPrimaryLoan) {
      if (hasPrimaryCitiBank) {
        return (
          <MakeDefaultButton action={openCallModal} isLoading={setDefaultPaymentMethod.isPending} />
        );
      }
      return (
        <LightTooltip
          arrow
          disableTouchListener
          placement="top"
          open={tooltipIsOpen}
          title={translate(TranslationKeys.PaymentMethodsPage.INFO_ICON_TEXT)}>
          <ButtonBase
            onClick={() => {
              setTooltipIsOpen(!tooltipIsOpen);
            }}
            aria-label="Info Icon">
            <InfoIcon
              sx={{ height: '20px', width: '20px', fill: Design.Alias.Color.secondary500 }}
              data-testid="payment-info-icon"
            />
          </ButtonBase>
        </LightTooltip>
      );
    }
    return (
      <MakeDefaultButton action={handleMakeDefault} isLoading={setDefaultPaymentMethod.isPending} />
    );
  };

  return (
    <Grid {...styles.cardContainer} data-testid={`payment-method-${paymentMethod.paymentMethodId}`}>
      <Grid container {...styles.card}>
        <Grid item style={{ alignContent: 'center' }}>
          <PaymentTypeIcon cardType={paymentMethod.cardType as CardIcon} />
        </Grid>
        <Grid {...styles.cardDetails}>
          <Grid item {...styles.textContainer}>
            <Typography {...styles.cardTitle}>{paymentMethod.displayCardType}</Typography>
          </Grid>
          {renderPaymentDetails(paymentMethod)}
        </Grid>
        <Grid item style={{ alignContent: 'center' }}>
          {renderActionSection()}
        </Grid>
      </Grid>
    </Grid>
  );
};

// -- styles --
const style = (isMobile?: boolean) => ({
  cardContainer: {
    sx: {
      width: '100%',
      textAlign: 'unset'
    }
  },
  card: {
    sx: {
      gap: Design.Primitives.Spacing.sm,
      display: 'flex',
      flexDirection: 'row',
      flexWrap: 'nowrap',
      padding: Design.Primitives.Spacing.sm,
      justifyContent: 'space-between',
      borderRadius: '8px',
      border: '1px solid var(--color-neutral-neutral300, #CBCBCB)',
      background: 'var(--color-defaults-neutral100-D, #FFF)',
      boxShadow: '0px 4px 6px 0px rgba(0, 0, 0, 0.10)'
    }
  },
  cardDetails: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'flexStart',
      flex: 1
    }
  },
  textContainer: {
    sx: {
      display: 'flex',
      flex: 1,
      flexDirection: isMobile ? 'column' : 'row',
      alignContent: 'center',
      gap: isMobile ? 0 : Design.Primitives.Spacing.xxs,
      ...Design.Alias.Text.BodyUniversal.Md
    }
  },
  cardTitle: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.LgBold,
      color: Design.Alias.Color.accent900
    }
  },
  separator: {
    display: isMobile ? 'none' : 'block'
  },
  foo: {
    sx: {
      'text-transform': 'capitalize'
    }
  }
});
