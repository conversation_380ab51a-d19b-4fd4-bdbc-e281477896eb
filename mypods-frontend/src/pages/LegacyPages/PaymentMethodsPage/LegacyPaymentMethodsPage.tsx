import React, { useContext, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Grid, Typography, useMediaQuery } from '@mui/material';
import Button from '@mui/material/Button';
import { useNavigate } from 'react-router';
import { theme } from '../../../PodsTheme';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { Design } from '../../../helpers/Design';
import { PageLayout } from '../../../components/PageLayout';
import { useBillingContext } from '../../../context/BillingContext';
import { NotificationContext } from '../../../components/notifications/NotificationContext';
import {
  isCitiBank,
  PaymentMethod
} from '../../../networkRequests/responseEntities/PaymentEntities';
import { ROUTES } from '../../../Routes';
import { BackLink } from '../../../components/buttons/BackLink';
import { LegacyPaymentMethodCard } from './LegacyPaymentMethodCard';
import { CardIcon } from '../../../components/icons/PaymentTypeIcon';
import { DefaultPaymentMethodCard } from '../../PaymentMethodsPage/DefaultPaymentMethodCard';
import { CallToChangeModal } from '../../PaymentMethodsPage/CallToChangeModal';

const Tx = TranslationKeys.PaymentMethodsPage;

// -- impls --
export const LegacyPaymentMethodsPage: React.FC = () => {
  const { t: translate } = useTranslation();
  const navigate = useNavigate();
  const { setNotification } = useContext(NotificationContext);
  const { paymentMethods, paymentsError, isPaymentsSuccess, isPaymentsError, isPaymentsFetching } =
    useBillingContext();
  const [callModalOpen, setCallModalOpen] = useState<boolean>(false);
  const hasPrimaryLoan = paymentMethods.some(
    (it) => it.isPrimary && it.cardType === CardIcon.LINE_OF_CREDIT
  );
  const hasPrimaryCitiBank = paymentMethods.some((it) => it.isPrimary && isCitiBank(it));

  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = pageStyle(isMobile);

  useEffect(() => {
    if (paymentsError) {
      setNotification({
        message: translate(TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE),
        isError: true
      });
    }
  }, [paymentsError]);

  return (
    <PageLayout columnsLg={6}>
      <Grid {...styles.page}>
        <Grid {...styles.headerSection}>
          <BackLink route={ROUTES.BILLING} />
          <Grid container {...styles.headerTitleRow}>
            <Grid item sm={7} flexDirection="column" display="flex">
              <Typography variant="h1">{translate(Tx.HEADER)}</Typography>
            </Grid>
            <Grid item sm={5} textAlign={isMobile ? 'left' : 'right'}>
              <Button
                aria-label={translate(Tx.ADD_PAYMENT_METHOD_BUTTON)}
                color="secondary"
                variant="contained"
                sx={{ width: '220px' }}
                onClick={() => navigate(ROUTES.MANAGE_PAYMENT_METHOD)}>
                {translate(Tx.ADD_PAYMENT_METHOD_BUTTON)}
              </Button>
            </Grid>
          </Grid>
        </Grid>
        <Grid {...styles.paymentMethods}>
          {isPaymentsFetching && <Typography>Loading payment methods...</Typography>}
          {isPaymentsError && <Grid> Whoops :( </Grid>}
          {isPaymentsSuccess &&
            paymentMethods &&
            paymentMethods.map((paymentMethod: PaymentMethod, index) =>
              paymentMethod.isPrimary ? (
                <DefaultPaymentMethodCard
                  paymentMethod={paymentMethod}
                  key={`paymentmethod-${index}`}
                />
              ) : (
                <LegacyPaymentMethodCard
                  paymentMethod={paymentMethod}
                  key={`paymentmethod-${index}`}
                  hasPrimaryLoan={hasPrimaryLoan}
                  hasPrimaryCitiBank={hasPrimaryCitiBank}
                  openCallModal={() => {
                    setCallModalOpen(true);
                  }}
                />
              )
            )}
        </Grid>
      </Grid>
      <CallToChangeModal
        open={callModalOpen}
        handleOnClosed={() => {
          setCallModalOpen(false);
        }}
      />
    </PageLayout>
  );
};

// -- styles --
const pageStyle = (isMobile: boolean) => ({
  page: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      gap: '1.5rem'
    }
  },
  headerSection: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      gap: Design.Primitives.Spacing.sm
    }
  },
  headerTitleRow: {
    sx: {
      alignItems: 'stretch',
      flexDirection: isMobile ? 'column' : 'row'
    },
    spacing: 2
  },
  paymentMethods: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      gap: Design.Primitives.Spacing.xxs
    }
  }
});
