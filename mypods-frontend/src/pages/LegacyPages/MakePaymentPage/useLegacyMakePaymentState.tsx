import { Dispatch, SetStateAction, useState } from 'react';
import { useNavigate } from 'react-router';
import { useTranslation } from 'react-i18next';
import { AxiosError } from 'axios';
import {
  PayInvoicesRequest,
  PaymentDeclinedErrors,
  PaymentMethod
} from '../../../networkRequests/responseEntities/PaymentEntities';
import { ROUTES } from '../../../Routes';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { BillingInvoice } from '../../../networkRequests/responseEntities/BillingEntities';
import useNotificationContext from '../../../components/notifications/NotificationContext';
import { useBillingContext } from '../../../context/BillingContext';
import { useGtmEvents } from '../../../config/google/useGtmEvents';
import { ErrorResponse } from '../../../networkRequests/responseEntities/ErrorEntities';
import { PodsAlertIcon, PodsAlertProps, PodsAlertType } from '../../../components/alert/PodsAlert';
import { BillingPageLocationState } from '../BillingPage/LegacyBillingPage';
import { useLegacyMakePayment } from '../../../networkRequests/legacy/mutations/useLegacyMakePayment';

const Tx = TranslationKeys.MakePaymentsPage;

interface IMakePaymentState {
  handleSubmitPayment: () => void;
  isPaymentPending: boolean;
  setPaymentAmount: Dispatch<SetStateAction<number>>;
  paymentAmount: number | undefined;
  paymentMethod: PaymentMethod | undefined;
  handlePaymentMethodChange: (value: string) => void;
  submitButtonTx: string;
}

export const useLegacyMakePaymentState = (invoicesToPay: BillingInvoice[]) => {
  const { setNotification } = useNotificationContext();
  const { t: translate } = useTranslation();
  const navigate = useNavigate();
  const payFullBalance = useLegacyMakePayment();
  const { billingInformation, paymentMethods, refetchBillingInfo } = useBillingContext();
  const [paymentAmount, setPaymentAmount] = useState<number | undefined>(
    billingInformation.totalBalance
  );
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod | undefined>(
    paymentMethods.find((method) => method.isPrimary)
  );
  const [isPaymentPending, setIsPaymentPending] = useState<boolean>(false);
  const gtmEvents = useGtmEvents();
  const [submitButtonTx, setSubmitButtonTx] = useState<string>(Tx.SUBMIT_BUTTON);

  const handleSubmitPayment = () => {
    if (paymentAmount && paymentMethod && billingInformation.invoices) {
      const request: PayInvoicesRequest = {
        paymentMethodId: paymentMethod.paymentMethodId!,
        totalPaymentAmount: paymentAmount,
        invoices: invoicesToPay.map((invoice) => ({
          invoiceNumber: invoice.invoiceNumber,
          paymentAmount: invoice.balanceDue,
          dueDate: invoice.dueDate
        }))
      };
      setIsPaymentPending(true);

      const gtmPayload = {
        accountBalance: billingInformation.totalBalance,
        paymentMethod,
        paymentAmount
      };
      gtmEvents.submitPayment(gtmPayload);

      payFullBalance.mutate(request, {
        onSuccess: () => {
          gtmEvents.successPayment(gtmPayload);
          setNotification({
            message: translate(Tx.Notifications.SUCCESS),
            isError: false
          });
          setIsPaymentPending(false);
          navigate(ROUTES.BILLING);
          refetchBillingInfo();
        },
        onError: (error: unknown) => {
          if (error instanceof AxiosError && error.response?.data?.status) {
            const errorResponse = error.response.data as ErrorResponse;
            const status = errorResponse.status as PaymentDeclinedErrors;
            let messageKey = TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE;
            switch (status) {
              case 'NO_BALANCE_REMAINING':
                messageKey = TranslationKeys.MakePaymentsPage.Error.Declined.NO_BALANCE_REMAINING;
                break;
              case 'FRAUD_SUSPECTED':
                messageKey = TranslationKeys.MakePaymentsPage.Error.Declined.FRAUD_SUSPECTED;
                break;
              case 'CARD_CLOSED':
                messageKey = TranslationKeys.MakePaymentsPage.Error.Declined.CARD_CLOSED;
                break;
              case 'INACTIVE_CARD':
                messageKey = TranslationKeys.MakePaymentsPage.Error.Declined.INACTIVE_CARD;
                break;
              case 'INVALID_ACCOUNT':
                messageKey = TranslationKeys.MakePaymentsPage.Error.Declined.INVALID_ACCOUNT;
                break;
              case 'INSUFFICIENT_FUNDS':
                messageKey = TranslationKeys.MakePaymentsPage.Error.Declined.INSUFFICIENT_FUNDS;
                break;
              case 'PROCESSOR_DECLINED':
                messageKey = TranslationKeys.MakePaymentsPage.Error.Declined.PROCESSOR_DECLINED;
                break;
              case 'CARD_ISSUER_DECLINED':
                messageKey = TranslationKeys.MakePaymentsPage.Error.Declined.CARD_ISSUER_DECLINED;
                break;
              case 'BANK_PAYMENT_UNAUTHORISED':
                messageKey =
                  TranslationKeys.MakePaymentsPage.Error.Declined.BANK_PAYMENT_UNAUTHORISED;
                break;
              case 'PAYPAL_ACCOUNT_ISSUE':
                messageKey = TranslationKeys.MakePaymentsPage.Error.Declined.PAYPAL_ACCOUNT_ISSUE;
                break;
              case 'LIMIT_EXCEEDED':
                messageKey = TranslationKeys.MakePaymentsPage.Error.Declined.LIMIT_EXCEEDED;
                break;
              case 'SOME_PAYMENTS_FAILED': {
                const alertProps: PodsAlertProps = {
                  title: translate(TranslationKeys.MakePaymentsPage.Error.Warning.TITLE),
                  description: translate(TranslationKeys.MakePaymentsPage.Error.Warning.BODY),
                  alertType: PodsAlertType.ERROR,
                  icon: PodsAlertIcon.ERROR
                };
                const locationState: BillingPageLocationState = { alertProps };
                refetchBillingInfo();
                navigate(ROUTES.BILLING, { state: locationState });
                return;
              }
              case 'DECLINED':
                messageKey = TranslationKeys.MakePaymentsPage.Error.Declined.DECLINED;
                break;
              default:
                break;
            }
            setNotification({
              message: translate(messageKey),
              isError: true
            });
          } else {
            // Generic error
            setNotification({
              message: translate(TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE),
              isError: true
            });
          }
          setIsPaymentPending(false);
          setSubmitButtonTx(Tx.SUBMIT_BUTTON);
        }
      });
    }
  };

  const handlePaymentMethodChange = (value: string) => {
    if (value === 'NA') {
      setPaymentMethod(undefined);
    } else {
      setPaymentMethod(paymentMethods.find((method) => method.paymentMethodId === value));
    }
  };

  return {
    handleSubmitPayment,
    isPaymentPending,
    setPaymentAmount,
    paymentAmount,
    paymentMethod,
    handlePaymentMethodChange,
    submitButtonTx
  } as IMakePaymentState;
};
