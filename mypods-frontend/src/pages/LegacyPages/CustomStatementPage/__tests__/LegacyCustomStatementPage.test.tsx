import { fireEvent, RenderResult, screen, waitFor, within } from '@testing-library/react';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import { LegacyCustomStatementPage } from '../LegacyCustomStatementPage';
import userEvent, { UserEvent } from '@testing-library/user-event';
import {
  renderWithLegacyProvidersAndState,
  runPendingPromises
} from '../../../../testUtils/RenderHelpers';
import {
  mockGetPaymentMethods,
  mockLegacyCreateCustomStatement,
  mockNavigate,
  mockRefreshSession
} from '../../../../../setupTests';
import { ROUTES } from '../../../../Routes';
import { createRefreshSessionClaims } from '../../../../testUtils/MyPodsFactories';

const Tx = TranslationKeys.CustomStatementPage;

const views = {
  header: () => screen.getByText(Tx.HEADER),
  subheader: () => screen.getByText(Tx.SUBTITLE),
  startDateInput: () => screen.getByLabelText('customStatementPage.datePickers.startDate'),
  endDateInput: () => screen.getByLabelText('customStatementPage.datePickers.endDate'),
  createStatementButton: () =>
    screen.getByRole('button', { name: 'customStatementPage.button[default]' })
};

describe('Legacy Custom Statement Page', () => {
  let user: UserEvent;

  beforeEach(() => {
    mockRefreshSession.mockResolvedValue(createRefreshSessionClaims());
    user = userEvent.setup();
  });

  const enterStartAndEndDate = async (start: string, end: string) => {
    fireEvent.change(views.startDateInput()!, { target: { value: start } });
    fireEvent.change(views.endDateInput()!, { target: { value: end } });
  };

  const renderCustomStatementPage = async (): Promise<RenderResult> => {
    const result = renderWithLegacyProvidersAndState(<LegacyCustomStatementPage />);
    await runPendingPromises();
    return result;
  };

  it('should show the title & subtitle', async () => {
    await renderCustomStatementPage();

    expect(views.header()).toBeInTheDocument();
    expect(views.subheader()).toBeInTheDocument();
  });

  describe('date pickers', () => {
    it('should show fields for picking a for start date & end date, and a disabled Create button on load', async () => {
      await renderCustomStatementPage();

      expect(views.startDateInput()).toBeInTheDocument();
      expect(views.endDateInput()).toBeInTheDocument();

      expect(screen.getByRole('button', { name: /customStatementPage.button/ })).toBeDisabled();
    });

    it('should show an enabled Create Statement button when both dates are selected', async () => {
      await renderCustomStatementPage();

      await enterStartAndEndDate('04/01/2024', '06/01/2024');

      expect(views.createStatementButton()).toBeEnabled();
    });
  });

  describe('generating a custom statement', () => {
    beforeEach(() => {
      mockLegacyCreateCustomStatement.mockResolvedValue({
        dateRange: '04/24 - 06/24',
        content: 'someBlob'
      });
    });

    it('shows a statement card in the Generated Statements section, when successful', async () => {
      await renderCustomStatementPage();

      await enterStartAndEndDate('04/01/2024', '06/01/2024');

      await waitFor(() => user.click(views.createStatementButton()));

      expect(
        screen.getByRole('button', {
          name: 'billingPage.statements.statement.title 04/24 - 06/24'
        })
      ).toBeInTheDocument();
    });

    it('shows an alert when no statement is returned', async () => {
      mockLegacyCreateCustomStatement.mockRejectedValue('oops');
      await renderCustomStatementPage();

      await enterStartAndEndDate('04/01/2024', '06/01/2024');

      await waitFor(() => user.click(views.createStatementButton()));

      expect(screen.getByRole('alert')).toHaveTextContent(
        TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE
      );
    });

    it('should display alert when start date is after end date', async () => {
      await renderCustomStatementPage();
      await enterStartAndEndDate('06/01/2024', '04/01/2024');

      await waitFor(() => user.click(views.createStatementButton()));

      let alert = within(screen.getByTestId('custom-statement-alert'));
      expect(alert.getByText(Tx.ErrorMessages.INVALID_DATES_DESCRIPTION)).toBeInTheDocument();
      expect(alert.getByText(Tx.ErrorMessages.INVALID_DATES_TITLE)).toBeInTheDocument();

      expect(mockLegacyCreateCustomStatement).not.toHaveBeenCalled();
    });
  });

  it('should navigate back to the billing page', async () => {
    mockGetPaymentMethods.mockResolvedValue([]);
    await renderCustomStatementPage();

    const link = screen.getByTestId('back-link');
    await userEvent.click(link);

    expect(mockNavigate).toHaveBeenCalledWith(ROUTES.BILLING);
  });
});
