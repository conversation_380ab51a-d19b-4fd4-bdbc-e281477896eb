import React, { useContext, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Grid, useMediaQuery } from '@mui/material';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import isEmpty from 'lodash/isEmpty';
import Divider from '@mui/material/Divider';
import { format, isAfter } from 'date-fns';
import { AxiosError } from 'axios';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { PageLayout } from '../../../components/PageLayout';
import { Design } from '../../../helpers/Design';
import { theme } from '../../../PodsTheme';
import { NotificationContext } from '../../../components/notifications/NotificationContext';
import { Txt } from '../../../components/Txt';
import { StretchableLoadingButton } from '../../../components/buttons/StretchableLoadingButton';
import { CustomStatement } from '../../../networkRequests/responseEntities/BillingEntities';
import {
  ErrorResponse,
  GenericErrorStatus
} from '../../../networkRequests/responseEntities/ErrorEntities';
import { BackLink } from '../../../components/buttons/BackLink';
import { ROUTES } from '../../../Routes';
import { PodsAlert, PodsAlertIcon, PodsAlertType } from '../../../components/alert/PodsAlert';
import { CustomStatementDatePicker } from '../../CustomStatementPage/CustomStatementDatePicker';
import { CustomStatementCard } from '../../CustomStatementPage/CustomStatementCard';
import { useLegacyCreateCustomStatement } from '../../../networkRequests/legacy/mutations/useLegacyCreateCustomStatement';
import { useCustomStatementEvents } from '../../../config/useCustomStatementEvents';

const Tx = TranslationKeys.CustomStatementPage;

// -- impls --
export const LegacyCustomStatementPage: React.FC = () => {
  // -- hooks --
  const createCustomStatementMutation = useLegacyCreateCustomStatement();
  const { setNotification } = useContext(NotificationContext);
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { t: translate } = useTranslation();
  // -- constants --
  const styles = customStatementPageStyles(isMobile);
  // -- state --
  const [statements, setStatements] = useState<CustomStatement[]>([]);
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [customStatementError, setCustomStatementError] = useState<boolean>(false);
  const { sendGenerateStatementAttemptEvent, sendGenerateStatementSuccessEvent } =
    useCustomStatementEvents();
  const submitEnabled = () => !!startDate && !!endDate;

  // -- handlers --
  const onCreateStatementClicked = () => {
    if (!startDate || !endDate) return;
    if (isAfter(startDate, endDate)) {
      setCustomStatementError(true);
      return;
    }
    const request = {
      startDate: format(startDate, 'yyyy-MM-dd'),
      endDate: format(endDate, 'yyyy-MM-dd')
    };

    sendGenerateStatementAttemptEvent(request.startDate, request.endDate);

    createCustomStatementMutation.mutate(request, {
      onSuccess: (data) => {
        sendGenerateStatementSuccessEvent(request.startDate, request.endDate);
        setStatements((prevState) => [data, ...prevState]);
      },
      onError: async (error: unknown) => {
        if (error instanceof AxiosError && error.response?.data instanceof Blob) {
          // Because the response type is a blob, you need to read the response from .text()
          const json = JSON.parse(await error.response.data.text());
          const errorResponse = json as ErrorResponse;
          const status = errorResponse.status as GenericErrorStatus;
          if (status === 'NOT_FOUND') {
            setNotification({
              message: translate(TranslationKeys.CustomStatementPage.ErrorMessages.NOT_FOUND),
              isError: true
            });
            return;
          }
        }
        setNotification({
          message: translate(TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE),
          isError: true
        });
      }
    });
  };

  const handleStartDateChange = (value: Date | null) => {
    setCustomStatementError(false);
    setStartDate(value ?? null);
  };
  const handleEndDateChange = (value: Date | null) => {
    setCustomStatementError(false);
    setEndDate(value ?? null);
  };

  return (
    <PageLayout columnsLg={6}>
      <LocalizationProvider dateAdapter={AdapterDateFns}>
        <Grid {...styles.customStatementPage}>
          <Grid {...styles.headerSection}>
            <BackLink route={ROUTES.BILLING} />
            <Txt i18nKey={Tx.HEADER} variant="h1" />
            <Txt i18nKey={Tx.SUBTITLE} {...styles.subtitle} />
          </Grid>
          {customStatementError && (
            <Grid data-testid="custom-statement-alert" container>
              <PodsAlert
                title={translate(Tx.ErrorMessages.INVALID_DATES_TITLE)}
                description={translate(Tx.ErrorMessages.INVALID_DATES_DESCRIPTION)}
                icon={PodsAlertIcon.INFO}
                alertType={PodsAlertType.ERROR}
              />
            </Grid>
          )}
          <Grid container {...styles.selectionContent}>
            <Grid container item {...styles.datePickers}>
              <CustomStatementDatePicker
                label={translate(Tx.DatePickers.START_DATE)}
                selectedDate={startDate}
                onChange={handleStartDateChange}
              />
              <CustomStatementDatePicker
                label={translate(Tx.DatePickers.END_DATE)}
                selectedDate={endDate}
                onChange={handleEndDateChange}
              />
            </Grid>
          </Grid>
          <Grid container {...styles.buttonContainer}>
            <StretchableLoadingButton
              label={
                createCustomStatementMutation.isPending
                  ? translate(Tx.BUTTON, { context: 'loading' })
                  : translate(Tx.BUTTON, { context: 'default' })
              }
              disabled={!submitEnabled()}
              isLoading={createCustomStatementMutation.isPending}
              onClick={onCreateStatementClicked}
              isMobile={isMobile}
            />
          </Grid>
          <Grid container item {...styles.dividerContainer}>
            <Divider />
          </Grid>
          <Txt {...styles.generatedStatementsHeader} i18nKey={Tx.GeneratedStatements.HEADER} />
          <Grid container item {...styles.generatedStatements}>
            {isEmpty(statements) && <Txt i18nKey={Tx.GeneratedStatements.EMPTY} />}
            {statements &&
              statements.map((statement, index) => (
                <CustomStatementCard customStatement={statement} key={index} />
              ))}
          </Grid>
        </Grid>
      </LocalizationProvider>
    </PageLayout>
  );
};

// -- styles --
const customStatementPageStyles = (isMobile: boolean) => ({
  customStatementPage: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      gap: Design.Primitives.Spacing.md
    }
  },
  headerSection: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      gap: Design.Primitives.Spacing.md
    }
  },
  selectionContent: {
    sx: {
      display: 'flex',
      flexDirection: 'column'
    }
  },
  subtitle: {
    sx: {
      ...(isMobile ? Design.Alias.Text.Heading.Mobile.Md : Design.Alias.Text.Heading.Desktop.Md),
      color: Design.Alias.Color.accent900
    }
  },
  datePickers: {
    sx: {
      display: 'flex',
      flexDirection: isMobile ? 'column' : 'row',
      gap: Design.Primitives.Spacing.sm
    }
  },

  buttonContainer: {
    sx: {
      flexDirection: 'row',
      justifyContent: 'stretch'
    }
  },
  dividerContainer: {
    sx: {
      flexDirection: 'column',
      justifyContent: 'center',
      height: isMobile ? Design.Primitives.Spacing.lgPlus : '6rem'
    }
  },
  generatedStatementsHeader: {
    ...Design.Alias.Text.Heading.Desktop.Md
  },
  generatedStatements: {
    gap: Design.Primitives.Spacing.xxs
  }
});
