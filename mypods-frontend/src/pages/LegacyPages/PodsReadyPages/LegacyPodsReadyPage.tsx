import React, { useEffect } from 'react';
import { useNavigate } from 'react-router';
import { useTranslation } from 'react-i18next';
import { useLegacyStartPodsReadySession } from '../../../networkRequests/legacy/queries/podsReady/useLegacyStartPodsReadySession';
import { redirectToLoginWithEmail } from '../../../context/ApigeeContext';
import { PodsReadyRoutes } from '../../../PodsReadyRoutes';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import LoadingScreen from '../../../components/Loading/LoadingScreen';
import { PODS_READY_SINGLE_ORDER_ENABLED, useFeatureFlags } from '../../../helpers/useFeatureFlags';
import { useLegacyShowPodsReadySingleOrder } from '../../../helpers/legacy/useLegacyShowPodsReadySingleOrder';
import { useLegacyPasswordOnboardingEvents } from '../../../config/usePasswordOnboardingEvents';

export const LegacyPodsReadyPage = () => {
  // Note: Password Onboarding flags are checked on the backend now
  const { isReady } = useFeatureFlags([PODS_READY_SINGLE_ORDER_ENABLED]);
  const { sendPasswordOnboardingStart } = useLegacyPasswordOnboardingEvents();
  const { t: translate } = useTranslation();
  const navigate = useNavigate();
  const {
    hasToken,
    podsReadySessionClaims: { hasPassword, email }
  } = useLegacyStartPodsReadySession();

  // This checks for only one order, so it handles filtering completed tasks & multiple orders
  const { showPodsReadySingleOrder, isFetching } = useLegacyShowPodsReadySingleOrder();

  const handleNavigation = () => {
    if (!isReady || isFetching) return;

    if (hasPassword && hasToken) {
      redirectToLoginWithEmail(email);
    } else if (showPodsReadySingleOrder) {
      navigate(PodsReadyRoutes.TASKS);
    } else if (!hasPassword && hasToken) {
      sendPasswordOnboardingStart();
      navigate(PodsReadyRoutes.SET_PASSWORD, { replace: true });
    } else {
      if (!hasPassword) {
        sendPasswordOnboardingStart();
      }
      redirectToLoginWithEmail(email);
    }
  };

  useEffect(handleNavigation, [hasPassword, email, isReady, isFetching, showPodsReadySingleOrder]);

  return <LoadingScreen loadingText={translate(TranslationKeys.LoadingScreens.HOME_PAGE)} />;
};
