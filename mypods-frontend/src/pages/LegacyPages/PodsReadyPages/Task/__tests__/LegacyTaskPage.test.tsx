import React from 'react';
import { screen, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import {
  renderWithLegacyProvidersAndState,
  runPendingPromises
} from '../../../../../testUtils/RenderHelpers';
import { LegacyTaskPage } from '../LegacyTaskPage';
import { TranslationKeys } from '../../../../../locales/TranslationKeys';
import {
  mockedUseFeatureFlags,
  mockLegacyGetPodsReadyOrders,
  mockLegacyStartPodsReadySession,
  mockNavigate,
  mockOrderHasSignedMothAgreement,
  mockSendSplitEvent
} from '../../../../../../setupTests';
import {
  createContainerApi,
  createEntryPointResult,
  createMoveLegApi,
  createOrderApi,
  createOutstandingMothAgreement,
  createOutstandingRentalAgreement,
  createPodsReadyClaims,
  createUseFeatureFlagResult
} from '../../../../../testUtils/MyPodsFactories';
import { initialEntryPointState } from '../../../../../context/EntryPointContext';
import { PodsReadyRoutes } from '../../../../../PodsReadyRoutes';
import { SplitEventType } from '../../../../../config/SplitEventTypes';

describe('LegacyTaskPage', () => {
  const moveLegs = [createMoveLegApi()];
  const containers = [createContainerApi({ moveLegs })];
  const noRentalAgreement = createEntryPointResult();
  const entryPointState = {
    ...initialEntryPointState,
    entryPointResult: noRentalAgreement
  };

  async function renderPage() {
    const result = renderWithLegacyProvidersAndState(<LegacyTaskPage />, {
      entryPointState: entryPointState
    });
    await runPendingPromises();
    return result;
  }
  beforeEach(() => {
    mockOrderHasSignedMothAgreement.mockReturnValue(true);
    mockLegacyGetPodsReadyOrders.mockResolvedValue([createOrderApi({ containers })]);
    mockLegacyStartPodsReadySession.mockResolvedValue(
      createPodsReadyClaims({ hasPassword: false })
    );
  });

  it('displays welcome', async () => {
    await renderPage();

    expect(
      await screen.findByText(TranslationKeys.TaskPage.WelcomePanel.TITLE)
    ).toBeInTheDocument();
  });

  it('sends a PODS_READY_START event on load', async () => {
    await renderPage();

    expect(mockSendSplitEvent).toHaveBeenCalledWith(SplitEventType.PODS_READY_START);
  });

  describe('has a unsigned rental agreement', () => {
    const singleRentalAgreement = createEntryPointResult({
      outstandingRentalAgreements: [createOutstandingRentalAgreement()]
    });

    beforeEach(() => {
      entryPointState.entryPointResult = singleRentalAgreement;
    });

    it('displays rental agreement task card', async () => {
      await renderPage();

      expect(
        await screen.findByText(TranslationKeys.TaskPage.RentalAgreement.TITLE)
      ).toBeInTheDocument();
      const taskCard = await screen.findByTestId('rental-agreement-task-card');
      expect(await within(taskCard).findByText('taskPage.dueDate[May 23]')).toBeInTheDocument();
      expect(within(taskCard).queryByText(TranslationKeys.TaskPage.DONE)).not.toBeInTheDocument();
    });

    describe('when clicked, it navigates to the rental agreement with a previous component and onSuccess route', () => {
      it('passes the success page route, when passwordOnboarding is disabled', async () => {
        await renderPage();

        const taskCard = await screen.findByTestId('rental-agreement-task-card');
        await waitFor(async () => {
          await userEvent.click(taskCard);
        });

        expect(mockNavigate).toHaveBeenCalledWith(PodsReadyRoutes.RENTAL_AGREEMENT, {
          state: {
            onSuccessRoute: PodsReadyRoutes.SUCCESS
          }
        });
      });

      it('passes the success page route, when passwordOnboarding is enabled & the user has a password', async () => {
        mockedUseFeatureFlags.useFeatureFlags.mockImplementation(() =>
          createUseFeatureFlagResult({ isPodsReadyPasswordOnboardingEnabled: () => true })
        );

        mockLegacyStartPodsReadySession.mockResolvedValue(
          createPodsReadyClaims({ hasPassword: true })
        );

        await renderPage();

        const taskCard = await screen.findByTestId('rental-agreement-task-card');
        await waitFor(async () => {
          await userEvent.click(taskCard);
        });

        expect(mockNavigate).toHaveBeenCalledWith(PodsReadyRoutes.RENTAL_AGREEMENT, {
          state: {
            onSuccessRoute: PodsReadyRoutes.SUCCESS
          }
        });
      });

      it('passes the set password page route, when passwordOnboarding is enabled  & the user needs a password', async () => {
        mockedUseFeatureFlags.useFeatureFlags.mockImplementation(() =>
          createUseFeatureFlagResult({ isPodsReadyPasswordOnboardingEnabled: () => true })
        );

        await renderPage();

        const taskCard = await screen.findByTestId('rental-agreement-task-card');
        await waitFor(async () => {
          await userEvent.click(taskCard);
        });

        expect(mockNavigate).toHaveBeenCalledWith(PodsReadyRoutes.RENTAL_AGREEMENT, {
          state: {
            onSuccessRoute: PodsReadyRoutes.SET_PASSWORD
          }
        });
      });
    });
  });

  describe('has a signed rental agreement', () => {
    beforeEach(() => {
      entryPointState.entryPointResult = noRentalAgreement;
    });

    it('displays rental agreement task card with done', async () => {
      await renderPage();

      const taskCard = await screen.findByTestId('rental-agreement-task-card');
      expect(await within(taskCard).findByText(TranslationKeys.TaskPage.DONE)).toBeInTheDocument();
      expect(within(taskCard).queryByText('Due May 23')).not.toBeInTheDocument();
    });

    it('clicking rental agreement task does nothing', async () => {
      await renderPage();

      const taskCard = await screen.findByTestId('rental-agreement-task-card');
      await waitFor(async () => {
        await userEvent.click(taskCard);
      });

      expect(mockNavigate).not.toHaveBeenCalledWith(PodsReadyRoutes.RENTAL_AGREEMENT);
    });
  });

  describe('does not have a moth Inspection Form', async () => {
    const noMothForm = createEntryPointResult({
      outstandingMothAgreements: []
    });

    beforeEach(() => {
      entryPointState.entryPointResult = noMothForm;
    });

    it('does not display moth Inspection task card', async () => {
      mockOrderHasSignedMothAgreement.mockReturnValue(false);
      await renderPage();

      expect(
        screen.queryByText(TranslationKeys.TaskPage.InvasiveSpecies.TITLE)
      ).not.toBeInTheDocument();
    });
  });
  describe('has an unsigned moth Inspection Form', () => {
    const singleMothForm = createEntryPointResult({
      outstandingMothAgreements: [createOutstandingMothAgreement()]
    });

    beforeEach(() => {
      entryPointState.entryPointResult = singleMothForm;
    });

    describe('but that form was previously signed', () => {
      beforeEach(() => {
        mockOrderHasSignedMothAgreement.mockReturnValue(true);
      });

      it('shows the moth form as done', async () => {
        await renderPage();

        expect(
          await within(await screen.findByTestId('moth-inspection-task-card')).findByText(
            TranslationKeys.TaskPage.DONE
          )
        ).toBeInTheDocument();
      });
    });

    describe("and that form wasn't previously signed", () => {
      beforeEach(() => {
        mockOrderHasSignedMothAgreement.mockReturnValue(false);
      });

      it('displays moth Inspection task card, with upcoming due date', async () => {
        await renderPage();

        expect(
          await screen.findByText(TranslationKeys.TaskPage.InvasiveSpecies.TITLE)
        ).toBeInTheDocument();
        expect(
          await screen.findByText(TranslationKeys.TaskPage.InvasiveSpecies.DESCRIPTION)
        ).toBeInTheDocument();

        expect(
          await within(await screen.findByTestId('moth-inspection-task-card')).findByText(
            'taskPage.dueDate[May 23]'
          )
        ).toBeInTheDocument();
      });

      describe('when clicked, it navigates to the moth form with a previous component and onSuccess route', () => {
        const bothTasksIncomplete = createEntryPointResult({
          outstandingMothAgreements: [createOutstandingMothAgreement()],
          outstandingRentalAgreements: [createOutstandingRentalAgreement()]
        });

        it('passes the tasks route, when there are remaining tasks', async () => {
          entryPointState.entryPointResult = bothTasksIncomplete;
          await renderPage();
          const mothFormCard = screen.getByTestId('moth-inspection-task-card');

          await waitFor(async () => {
            await userEvent.click(mothFormCard);
          });

          expect(mockNavigate).toHaveBeenCalledWith(PodsReadyRoutes.MOTH_FLY_INSPECTION, {
            state: {
              onSuccessRoute: PodsReadyRoutes.TASKS
            }
          });
        });
        describe('when all tasks are complete', () => {
          const rentalAgreementComplete = createEntryPointResult({
            outstandingMothAgreements: [createOutstandingMothAgreement()]
          });

          beforeEach(() => {
            entryPointState.entryPointResult = rentalAgreementComplete;
          });

          it('passes the success page route, when password onboarding is disabled', async () => {
            await renderPage();
            const mothFormCard = screen.getByTestId('moth-inspection-task-card');

            await waitFor(async () => {
              await userEvent.click(mothFormCard);
            });

            expect(mockNavigate).toHaveBeenCalledWith(PodsReadyRoutes.MOTH_FLY_INSPECTION, {
              state: {
                onSuccessRoute: PodsReadyRoutes.SUCCESS
              }
            });
          });
          it('passes the success page route, when password onboarding is enabled & the user needs a password', async () => {
            mockedUseFeatureFlags.useFeatureFlags.mockImplementation(() =>
              createUseFeatureFlagResult({ isPodsReadyPasswordOnboardingEnabled: () => true })
            );
            await renderPage();
            const mothFormCard = screen.getByTestId('moth-inspection-task-card');

            await waitFor(async () => {
              await userEvent.click(mothFormCard);
            });

            expect(mockNavigate).toHaveBeenCalledWith(PodsReadyRoutes.MOTH_FLY_INSPECTION, {
              state: {
                onSuccessRoute: PodsReadyRoutes.SET_PASSWORD
              }
            });
          });

          it('passes the success page route, when password onboarding is enabled & the user has a password', async () => {
            mockedUseFeatureFlags.useFeatureFlags.mockImplementation(() =>
              createUseFeatureFlagResult({ isPodsReadyPasswordOnboardingEnabled: () => true })
            );
            mockLegacyStartPodsReadySession.mockResolvedValue(
              createPodsReadyClaims({ hasPassword: true })
            );
            await renderPage();
            const mothFormCard = screen.getByTestId('moth-inspection-task-card');

            await waitFor(async () => {
              await userEvent.click(mothFormCard);
            });

            expect(mockNavigate).toHaveBeenCalledWith(PodsReadyRoutes.MOTH_FLY_INSPECTION, {
              state: {
                onSuccessRoute: PodsReadyRoutes.SUCCESS
              }
            });
          });
        });
      });
    });
  });
  describe('has a signed moth Inspection Form', () => {
    const singleMothForm = createEntryPointResult({
      outstandingMothAgreements: []
    });

    beforeEach(() => {
      entryPointState.entryPointResult = singleMothForm;
    });

    it('it displays the moth inspection task card, with done status ', async () => {
      await renderPage();

      expect(
        screen.queryByText(TranslationKeys.TaskPage.InvasiveSpecies.TITLE)
      ).toBeInTheDocument();
      expect(
        screen.queryByText(TranslationKeys.TaskPage.InvasiveSpecies.DESCRIPTION)
      ).toBeInTheDocument();

      expect(
        within(screen.getByTestId('moth-inspection-task-card')).queryByText(
          TranslationKeys.TaskPage.DONE
        )
      ).toBeInTheDocument();
    });

    it('does not navigate away from the tasks page when clicked', async () => {
      await renderPage();
      const mothFormCard = screen.getByTestId('moth-inspection-task-card');
      await waitFor(async () => {
        await userEvent.click(mothFormCard);
      });

      expect(mockNavigate).not.toHaveBeenCalledWith(PodsReadyRoutes.MOTH_FLY_INSPECTION);
    });
  });

  describe('task completion percentage calculation', () => {
    it('shows 100% completion when all tasks are complete', async () => {
      entryPointState.entryPointResult = createEntryPointResult({
        outstandingRentalAgreements: [],
        outstandingMothAgreements: []
      });
      mockOrderHasSignedMothAgreement.mockReturnValue(true);

      await renderPage();

      const progressBar = await screen.findByRole('progressbar');
      expect(progressBar).toHaveAttribute('aria-valuenow', '100');

      expect(await screen.findByText(/100/)).toBeInTheDocument();
    });

    it('shows 67% completion when 2 out of 3 tasks are complete (rental agreement incomplete)', async () => {
      entryPointState.entryPointResult = createEntryPointResult({
        outstandingRentalAgreements: [createOutstandingRentalAgreement()],
        outstandingMothAgreements: []
      });
      mockOrderHasSignedMothAgreement.mockReturnValue(true);

      await renderPage();

      const progressBar = await screen.findByRole('progressbar');
      expect(progressBar).toHaveAttribute('aria-valuenow', '67');

      expect(await screen.findByText(/67/)).toBeInTheDocument();
    });

    it('shows 67% completion when 2 out of 3 tasks are complete (moth form incomplete)', async () => {
      entryPointState.entryPointResult = createEntryPointResult({
        outstandingRentalAgreements: [],
        outstandingMothAgreements: [createOutstandingMothAgreement()]
      });
      mockOrderHasSignedMothAgreement.mockReturnValue(false);

      await renderPage();

      const progressBar = await screen.findByRole('progressbar');
      expect(progressBar).toHaveAttribute('aria-valuenow', '67');

      expect(await screen.findByText(/67/)).toBeInTheDocument();
    });

    it('shows 33% completion when only 1 out of 3 tasks are complete', async () => {
      entryPointState.entryPointResult = createEntryPointResult({
        outstandingRentalAgreements: [createOutstandingRentalAgreement()],
        outstandingMothAgreements: [createOutstandingMothAgreement()]
      });
      mockOrderHasSignedMothAgreement.mockReturnValue(false);

      await renderPage();

      const progressBar = await screen.findByRole('progressbar');
      expect(progressBar).toHaveAttribute('aria-valuenow', '33');

      expect(await screen.findByText(/33/)).toBeInTheDocument();
    });

    it('shows 100% completion when moth form is previously signed but outstanding', async () => {
      entryPointState.entryPointResult = createEntryPointResult({
        outstandingRentalAgreements: [],
        outstandingMothAgreements: [createOutstandingMothAgreement()]
      });
      mockOrderHasSignedMothAgreement.mockReturnValue(true);

      await renderPage();

      const progressBar = await screen.findByRole('progressbar');
      expect(progressBar).toHaveAttribute('aria-valuenow', '100');

      expect(await screen.findByText(/100/)).toBeInTheDocument();
    });

    it('verifies progress bar receives correct percentage value', async () => {
      entryPointState.entryPointResult = createEntryPointResult({
        outstandingRentalAgreements: [createOutstandingRentalAgreement()],
        outstandingMothAgreements: []
      });
      mockOrderHasSignedMothAgreement.mockReturnValue(true);

      await renderPage();

      const progressBar = await screen.findByRole('progressbar');
      expect(progressBar).toHaveAttribute('aria-valuenow', '67');
    });
  });
});
