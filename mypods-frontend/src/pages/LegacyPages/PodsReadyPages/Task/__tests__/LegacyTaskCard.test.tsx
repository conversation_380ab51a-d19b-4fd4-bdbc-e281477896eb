import { render, screen, waitFor } from '@testing-library/react';
import { SignatureIcon } from '@phosphor-icons/react';
import { TranslationKeys } from '../../../../../locales/TranslationKeys';
import userEvent, { UserEvent } from '@testing-library/user-event';
import { TaskCard } from '../../../../PodsReadyPages/Task/TaskCard';

describe('TaskCard', () => {
  const TITLE = 'TITLE?';
  const DESCRIPTION = 'DESCRIPTION GOES HERE';
  const dueDate = new Date(2065, 1, 14);
  const onClick = vi.fn();
  let user: UserEvent;

  interface RenderProps {
    completed: boolean;
    isMobile: boolean;
  }

  const renderTaskCard = (props: RenderProps) => {
    const { completed, isMobile } = props;
    return render(
      <TaskCard
        Icon={SignatureIcon}
        title={TITLE}
        description={DESCRIPTION}
        dueDate={dueDate}
        isComplete={completed}
        isMobile={isMobile}
        onClick={onClick}
      />
    );
  };

  beforeEach(() => {
    user = userEvent.setup();
  });

  describe('when complete false', () => {
    describe('and isMobile false', () => {
      beforeEach(() => {
        renderTaskCard({ completed: false, isMobile: false });
      });

      it('renders next-icon and due date', async () => {
        expect(await screen.findByText(TITLE)).toBeInTheDocument();
        expect(await screen.findByText(DESCRIPTION)).toBeInTheDocument();
        expect(await screen.findByText('taskPage.dueDate[Feb 14]')).toBeInTheDocument();
        expect(await screen.findByTestId('icon-in-circle')).toBeInTheDocument();
        expect(await screen.findByTestId('next-icon')).toBeInTheDocument();
      });

      it('clicking triggers onClick', async () => {
        const button = await screen.findByText(TITLE);

        await waitFor(() => user.click(button));

        expect(onClick).toHaveBeenCalled();
      });
    });

    describe('and isMobile true', () => {
      beforeEach(() => {
        renderTaskCard({ completed: false, isMobile: true });
      });

      it('renders review and sign button instead of next icon', async () => {
        expect(
          await screen.findByText(TranslationKeys.TaskPage.REVIEW_AND_SIGN_BUTTON)
        ).toBeInTheDocument();
        expect(screen.queryByTestId('next-icon')).not.toBeInTheDocument();
      });
    });
  });

  describe('when complete true', () => {
    beforeEach(() => {
      renderTaskCard({ completed: true, isMobile: false });
    });

    it('renders without next icon and done instead of due date', async () => {
      expect(await screen.findByText(TITLE)).toBeInTheDocument();
      expect(await screen.findByText(DESCRIPTION)).toBeInTheDocument();
      expect(await screen.findByText(TranslationKeys.TaskPage.DONE)).toBeInTheDocument();
      expect(await screen.findByTestId('icon-in-circle')).toBeInTheDocument();
      expect(screen.queryByTestId('next-icon')).not.toBeInTheDocument();
    });

    it('clicking does not trigger onClick', async () => {
      const button = await screen.findByText(TITLE);

      await waitFor(() => user.click(button));

      expect(onClick).not.toHaveBeenCalled();
    });
  });
});
