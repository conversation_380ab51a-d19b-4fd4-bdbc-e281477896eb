import { Grid } from '@mui/material';
import React, { useEffect } from 'react';
import { datadogLogs } from '@datadog/browser-logs';
import { ACORN_FINANCING_ENABLED, useFeatureFlags } from '../../../../helpers/useFeatureFlags';
import {
  financingEligible,
  findMostExpensiveEligibleOrder
} from '../../../BillingPage/utility/FinancingEligibility';
import { ENV_VARS } from '../../../../environment';
import { useLegacyGetPaymentMethods } from '../../../../networkRequests/legacy/queries/useLegacyGetPaymentMethods';
import useLegacyOrdersContext from '../../../../context/legacy/LegacyOrdersContext';
import { useLegacyGetCustomer } from '../../../../networkRequests/legacy/queries/useLegacyGetCustomer';
import { AcornFinancingHelper } from '../../../BillingPage/utility/AcornFinancingHelper';

type LegacyAcornFinancingProps = {
  widgetKind?: 'card' | 'banner' | 'button';
};

export const LegacyAcornFinancing = ({ widgetKind = 'card' }: LegacyAcornFinancingProps) => {
  const { customer, isSuccess: isGetCustomerSuccess } = useLegacyGetCustomer();
  const { orders } = useLegacyOrdersContext();
  const { isAcornFinancingEnabled } = useFeatureFlags([ACORN_FINANCING_ENABLED]);
  const { paymentMethods, isSuccess: isPaymentsSuccess } = useLegacyGetPaymentMethods();
  const mostExpensiveOrder = findMostExpensiveEligibleOrder(orders);
  const isFinancingEligible = financingEligible(
    customer,
    isAcornFinancingEnabled(),
    paymentMethods,
    orders,
    mostExpensiveOrder
  );
  const acornHelper = AcornFinancingHelper(widgetKind);

  useEffect(() => {
    if (
      !isFinancingEligible ||
      !isPaymentsSuccess ||
      !isGetCustomerSuccess ||
      acornHelper.getBannerClosed()
    )
      return;

    const handleAcornProcessStatus = (event: {}) => {
      acornHelper.setUserClosed();
      datadogLogs.logger.info('Customer acorn financing event', {
        customerId: customer.id,
        orderId: mostExpensiveOrder?.orderId,
        acornProcessStatusEvent: event
      });
    };

    window.addEventListener('acornProcessStatus', handleAcornProcessStatus);

    const intervalId = setInterval(() => {
      if (typeof acornParameters !== 'undefined' && typeof setAcornWidget !== 'undefined') {
        acornParameters = {};
        acornParameters.loanAmount = mostExpensiveOrder?.price;
        acornParameters.merCustomerId = `POD-${customer.id}`;
        acornParameters.merRefId = `POD-${mostExpensiveOrder?.quoteId}`;
        acornParameters.merOrderId = `POD-${mostExpensiveOrder?.orderId}`;
        acornParameters.merDeliveryDate = mostExpensiveOrder?.scheduledDate;
        acornParameters.utm_source = `MyPODS-Payments`;
        acornParameters.docSource = `MyPODS-Payments`;
        acornParameters.personalInfo = {
          firstName: customer.firstName,
          lastName: customer.lastName,
          email: customer.email?.address,
          streetAddress: customer.billingAddress?.address1,
          apartment: customer.billingAddress?.address2,
          city: customer.billingAddress?.city,
          state: customer.billingAddress?.state,
          zipCode: customer.billingAddress?.postalCode?.split('-')[0],
          country: customer.billingAddress?.regionCode,
          phoneNumber: customer.primaryPhone?.number?.replace(/-/g, '')
        };

        setAcornWidget(
          widgetKind === 'card' ? 'acorn-mypods-widget' : 'acorn-mypods-widget-notification'
        );
        clearInterval(intervalId); // Stop the polling
      }
    }, 300);

    return () => {
      clearInterval(intervalId);
      window.removeEventListener('acornProcessStatus', handleAcornProcessStatus);
    };
  }, [isFinancingEligible, isPaymentsSuccess, isGetCustomerSuccess]);

  if (!isFinancingEligible) return;

  datadogLogs.logger.info('Customer is eligible for financing', {
    customerId: customer.id,
    orderId: mostExpensiveOrder?.orderId
  });

  const card = `<div class="widget-notification"><acorn-mypods-widget env="${ENV_VARS.ACORN_DEPLOYMENT_ENVIRONMENT_NAME}" id="acorn-mypods-widget" /></div>`;

  const banner = `
<div class="widget-notification-section">
  <div class="widget-notification">
   <acorn-mypods-widget widgetType="banner" notification="true" showclose=true env="${ENV_VARS.ACORN_DEPLOYMENT_ENVIRONMENT_NAME}" id="acorn-mypods-widget-notification"></acorn-mypods-widget>
  </div>
</div>`;

  return (
    <Grid
      container
      item
      sx={{ display: 'block' }}
      dangerouslySetInnerHTML={{
        __html: widgetKind === 'card' ? card : banner
      }}
    />
  );
};
