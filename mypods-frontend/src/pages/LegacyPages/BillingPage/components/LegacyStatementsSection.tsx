import React from 'react';
import { Accordion, AccordionDetails, AccordionSummary, Grid, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { RightChevronIcon } from '../../../../components/icons/RightChevronIcon';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import { BlueLink } from '../../../../components/buttons/NavigationLink';
import { ROUTES } from '../../../../Routes';
import { MonthlyBillingStatement } from '../../../../networkRequests/responseEntities/BillingEntities';
import { BillingCardData, LegacyBillingCard } from './LegacyBillingCard';

type Props = {
  sortedStatements: MonthlyBillingStatement[];
  styles: any;
};

export const LegacyStatementsSection: React.FC<Props> = ({ sortedStatements, styles }: Props) => {
  const { t: translate } = useTranslation();
  const convertStatementToBillingCardData = (
    statement: MonthlyBillingStatement
  ): BillingCardData => ({
    // TODO("Figure out how to get the amount, currency type, and date from the statement object")
    amount: 0.0,
    currencyType: 'USD',
    totalAmount: 0.0,
    date: statement.commercialInvoiceStatementDate!,
    id: statement.commercialInvoiceStatementId!,
    documentId: statement.docuRefIdentity!.toString(),
    invoiceNumber: '',
    type: 'statement',
    isPoet: false
  });
  return (
    <Grid data-testid="statements-section">
      <Accordion disableGutters defaultExpanded {...styles.accordion}>
        <AccordionSummary
          {...styles.accordionSummary}
          expandIcon={<RightChevronIcon {...styles.rightChevronIcon} />}>
          <Grid container {...styles.accordionHeader}>
            <Typography variant="h4" {...styles.accordionTitle}>
              {translate(TranslationKeys.BillingPage.Statements.HEADER)}
            </Typography>
            <BlueLink to={ROUTES.CUSTOM_STATEMENT} style={{ flex: 1 }}>
              {translate(TranslationKeys.CustomStatementPage.LINK)}
            </BlueLink>
          </Grid>
        </AccordionSummary>
        <AccordionDetails {...styles.accordionDetails}>
          <Grid container data-testid="statements-content" gap="8px">
            {sortedStatements?.map((statement) => (
              <LegacyBillingCard
                key={statement.commercialInvoiceStatementId}
                {...convertStatementToBillingCardData(statement)}
              />
            ))}
          </Grid>
        </AccordionDetails>
      </Accordion>
    </Grid>
  );
};
