import React from 'react';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Grid,
  Typography,
  useMediaQuery
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import { useBeforeUnload, useLocation, useNavigate } from 'react-router';
import { PageLayout } from '../../../components/PageLayout';
import {
  BillingInvoice,
  MonthlyBillingStatement
} from '../../../networkRequests/responseEntities/BillingEntities';
import { RightChevronIcon } from '../../../components/icons/RightChevronIcon';
import { BillingCardData, LegacyBillingCard } from './components/LegacyBillingCard';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { LegacyAccountBalanceCard } from './components/LegacyAccountBalanceCard';
import { ROUTES } from '../../../Routes';
import { theme } from '../../../PodsTheme';
import { PrimaryPaymentMethodCard } from '../../BillingPage/components/PrimaryPaymentMethodCard';
import { useBillingContext } from '../../../context/BillingContext';
import { LegacyAcornFinancing } from './components/LegacyAcornFinancing';
import { PodsAlert, PodsAlertProps } from '../../../components/alert/PodsAlert';
import { BillingSkeleton } from '../../BillingPage/utility/BillingSkeleton';
import { Design } from '../../../helpers/Design';
import { HeaderCardWrapper } from '../../BillingPage/components/HeaderCardWrapper';
import { LegacyPaymentHistorySection } from './components/LegacyPaymentHistorySection';
import { BillingFaqCard } from '../../BillingPage/components/BillingFaqCard';
import { LegacyStatementsSection } from './components/LegacyStatementsSection';

export type BillingPageLocationState = {
  alertProps?: PodsAlertProps;
};

// TODO: Add this type of mapping to our APITypes to DomainTypes mapper.
const convertBillingInvoiceToBillingCardData = (invoice: BillingInvoice): BillingCardData => ({
  amount: invoice.isPaid ? invoice.totalDue : invoice.balanceDue,
  totalAmount: invoice.totalDue,
  currencyType: invoice.currencyType!,
  date: invoice.dueDate!,
  id: invoice.invoiceNumber!,
  documentId: invoice.documentId,
  invoiceNumber: invoice.invoiceNumber!,
  type: invoice.isPaid ? 'paid-invoice' : 'unpaid-invoice',
  isPoet: invoice.isPoet
});

export const LegacyBillingPage = () => {
  const { t: translate } = useTranslation();
  const { billingInformation, isBillingInfoPending, billingError } = useBillingContext();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const location = useLocation();
  const locationState: BillingPageLocationState | null = location.state;
  const navigate = useNavigate();

  // On page refresh, clear location state
  useBeforeUnload(
    React.useCallback(() => {
      if (locationState?.alertProps) {
        navigate(ROUTES.BILLING);
      }
    }, [])
  );

  if (billingError) return <pre>{JSON.stringify(billingError, null, 2)}</pre>;
  if (isBillingInfoPending || !billingInformation) return <BillingSkeleton />;

  // TODO: Convert to dates when we get them from the backend? -eh
  const invoicesByOldestDate = (a: BillingInvoice, b: BillingInvoice) =>
    Date.parse(a.dueDate!) - Date.parse(b.dueDate!);

  const invoicesByLatestDate = (a: BillingInvoice, b: BillingInvoice) =>
    Date.parse(b.dueDate!) - Date.parse(a.dueDate!);

  const statementsByLatestDate = (a: MonthlyBillingStatement, b: MonthlyBillingStatement) =>
    Date.parse(b.createdDateTime!) - Date.parse(a.createdDateTime!);

  const upcomingPayments = billingInformation.invoices
    ?.filter((invoice) => !invoice.isPaid)
    .sort(invoicesByOldestDate);
  const paymentHistory = billingInformation.invoices
    ?.filter((invoice) => invoice.isPaid)
    .sort(invoicesByLatestDate);
  const sortedStatements = billingInformation.monthlyStatements?.sort(statementsByLatestDate);
  const paymentHistoryData = { paymentHistory };
  const statementData = { sortedStatements };

  return (
    <PageLayout columnsLg={6}>
      <Grid data-testid="billing-page" {...styles.page}>
        <Typography variant="h1">{translate(TranslationKeys.BillingPage.HEADER)}</Typography>
        {locationState?.alertProps && <PodsAlert {...locationState.alertProps} />}
        <Grid
          container
          spacing={2}
          alignItems="stretch"
          flexDirection={isMobile ? 'column' : 'row'}>
          <Grid item sm={7} flexDirection="column" display="flex">
            <LegacyAccountBalanceCard />
          </Grid>
          <Grid item sm={5} flexDirection="column" display="flex">
            <HeaderCardWrapper>
              <PrimaryPaymentMethodCard />
            </HeaderCardWrapper>
          </Grid>
        </Grid>
        <LegacyAcornFinancing />
        <Grid data-testid="upcoming-payments-section">
          <Accordion disableGutters defaultExpanded {...styles.accordion}>
            <AccordionSummary
              {...styles.accordionSummary}
              expandIcon={<RightChevronIcon {...styles.rightChevronIcon} />}>
              <Typography variant="h4" {...styles.accordionTitle}>
                {translate(TranslationKeys.BillingPage.UpcomingPayments.HEADER)}
              </Typography>
            </AccordionSummary>
            <AccordionDetails {...styles.accordionDetails}>
              <Grid container data-testid="upcoming-payments-content" gap="8px">
                {upcomingPayments?.map((payment) => (
                  <LegacyBillingCard
                    key={payment.invoiceNumber}
                    {...convertBillingInvoiceToBillingCardData(payment)}
                  />
                ))}
              </Grid>
            </AccordionDetails>
          </Accordion>
        </Grid>
        <LegacyPaymentHistorySection {...paymentHistoryData} styles={styles} />
        <LegacyStatementsSection {...statementData} styles={styles} />
        <BillingFaqCard showCustomStatementWarning={false} />
      </Grid>
    </PageLayout>
  );
};

const styles = {
  page: {
    sx: {
      gap: '32px',
      display: 'flex',
      flexDirection: 'column'
    }
  },
  accordion: {
    sx: { boxShadow: 'none' }
  },
  accordionSummary: {
    sx: {
      minHeight: 'fit-content',
      flexDirection: 'row-reverse',
      '& .MuiAccordionSummary-expandIconWrapper.Mui-expanded': {
        transform: 'rotate(90deg)'
      },
      '& .MuiAccordionSummary-content': {
        margin: 0
      },
      gap: Design.Primitives.Spacing.xxs
    }
  },
  accordionTitle: {
    sx: {
      color: Design.Alias.Color.accent900,
      flex: 1
    }
  },
  accordionDetails: {
    sx: {
      paddingLeft: 0,
      paddingRight: 0
    }
  },
  accordionHeader: {
    sx: {
      display: 'flex',
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center'
    }
  },
  rightChevronIcon: {
    sx: {
      width: '16px',
      height: '16px'
    },
    style: {
      color: Design.Alias.Color.accent900
    }
  }
};
