import React, { useContext, useEffect, useState } from 'react';
import { Grid, Typography, useMediaQuery } from '@mui/material';
import { DesktopDatePicker } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { useTranslation } from 'react-i18next';
import { SaveButton } from '../../../../../components/buttons/SaveButton';
import {
  ContainerPlacement,
  MoveLeg,
  MoveLegTypeEnum,
  SameServiceAreaRequest,
  ServiceAddress,
  UpdateMoveLegRequest
} from '../../../../../domain/OrderEntities';
import { CancelButton } from '../../../../../components/buttons/CancelButton';
import { TranslationKeys } from '../../../../../locales/TranslationKeys';
import {
  getDatePicker<PERSON><PERSON><PERSON><PERSON><PERSON>,
  PriceDifferenceContext
} from '../../../../../locales/TranslationConstants';
import { NotificationContext } from '../../../../../components/notifications/NotificationContext';
import { PodsAlert, PodsAlertIcon, PodsAlertType } from '../../../../../components/alert/PodsAlert';
import { Design } from '../../../../../helpers/Design';
import { UpdateMoveLegResponse } from '../../../../../networkRequests/responseEntities/OrderAPIEntities';
import { formatDate } from '../../../../../helpers/dateHelpers';
import { StretchableLoadingButton } from '../../../../../components/buttons/StretchableLoadingButton';
import { theme } from '../../../../../PodsTheme';
import useContainerContext from '../../../../../context/ContainerContext';
import { CheckmarkIcon } from '../../../../../components/icons/CheckmarkIcon';
import { useGtmEvents } from '../../../../../config/google/useGtmEvents';
import useSingleOrderContext from '../../../../../context/SingleOrderContext';
import { GtmScheduleType } from '../../../../../config/google/GoogleEntities';
import {
  createGtmErrorRequest,
  GA_GENERIC_BACKEND_MESSAGE
} from '../../../../../config/google/googleAnalyticsUtils';
import { useLegacyIsSameServiceArea } from '../../../../../networkRequests/legacy/mutations/useLegacyIsSameServiceArea';
import { useLegacyUpdateMoveLeg } from '../../../../../networkRequests/legacy/mutations/useLegacyUpdateMoveLeg';
import { DatepickerTextField } from '../../../../HomePage/container/scheduling/DatepickerTextField';
import { DatePickerDay } from '../../../../HomePage/container/scheduling/DatePickerDay';
import { MoveLegAddressForm } from '../../../../HomePage/container/scheduling/MoveLegAddressForm';
import { toServiceAddress } from '../../../../HomePage/container/scheduling/useMoveLegAddressState';
import { isStaleDataError } from '../../../../HomePage/utils';
import useLegacyOrdersContext from '../../../../../context/legacy/LegacyOrdersContext';
import { LegacyContainerPlacementProvider } from './containerplacement/context/LegacyContainerPlacementProvider';
import { LegacyContainerPlacementModal } from './containerplacement/LegacyContainerPlacementModal';
import useMoveLegContext from '../../../../HomePage/container/moveleg/MoveLegContext';

export interface ScheduleMoveLegProps {
  onStopScheduling: () => void;
  isEditingGtmEvent?: boolean;
}

const Tx = TranslationKeys.HomePage.MoveLegs;
export const getSuccessMessage = (moveLeg: MoveLeg) => {
  const { moveLegType } = moveLeg;
  if (moveLegType === MoveLegTypeEnum.VISIT_CONTAINER && moveLeg.containerVisitDate == null)
    return Tx.Scheduling.SCHEDULE_VISIT_CONTAINER_SUCCESSFUL;
  if (moveLegType === MoveLegTypeEnum.VISIT_CONTAINER && moveLeg.containerVisitDate != null)
    return Tx.Scheduling.RESCHEDULE_VISIT_CONTAINER_SUCCESSFUL;
  return Tx.Scheduling.SCHEDULE_UPDATE_SUCCESSFUL;
};

export const LegacyScheduleMoveLeg = ({
  onStopScheduling,
  isEditingGtmEvent
}: ScheduleMoveLegProps) => {
  const {
    moveLeg,
    scheduling: { dateState, addressState }
  } = useMoveLegContext();
  const { order, container } = useContainerContext();
  const { moveLegScheduling } = useSingleOrderContext();
  const { setIsSaving } = moveLegScheduling;
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = scheduleStyles(isMobile);
  const { t: translate } = useTranslation();
  const updateMoveLeg = useLegacyUpdateMoveLeg();
  const isSameServiceAreaCall = useLegacyIsSameServiceArea();
  const { refetch, refetchOnFailure } = useLegacyOrdersContext();
  const { setNotification } = useContext(NotificationContext);
  const [serviceabilityError, setServiceabilityError] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState(false);
  const [saveButtonIsLoading, setSaveButtonIsLoading] = useState<boolean>(false);
  const [priceDifferenceResponse, setPriceDifferenceResponse] =
    useState<UpdateMoveLegResponse | null>();
  const [completedContainerPlacement, setCompletedContainerPlacement] =
    useState<ContainerPlacement | null>(null);
  const [containerPlacementOpen, setContainerPlacementOpen] = useState<boolean>(false);
  const gtmEvents = useGtmEvents();

  useEffect(() => {
    dateState.addContainerAvailabilitiesFor3Months(
      moveLeg.scheduledDate ?? dateState.getCalendarStartDate()
    );
  }, []);

  const handleSelectDate = (pickedDate: Date | null | undefined) => {
    if (pickedDate != null) {
      dateState.setSelectedDate(pickedDate);
    }

    setIsOpen(false);
  };

  const showCalendar = () => {
    if (!serviceabilityError) {
      setIsOpen(true);
    }
  };

  const onLoadingComplete = () => {
    setSaveButtonIsLoading(false);
    setIsSaving(false);
  };

  const createUpdateMoveLegRequest = (): UpdateMoveLegRequest => {
    const request: UpdateMoveLegRequest = {
      orderId: order.orderId,
      containerOrderId: container.containerOrderId,
      moveLegId: moveLeg.moveLegId,
      moveLegType: moveLeg.moveLegType,
      requestedDate: formatDate(dateState.selectedDate!, 'yyyy-MM-dd'),
      transitDays: moveLeg.transitDays,
      // Likely used to cancel a visit to a container warehouse
      isCancelLeg: false,
      locationFields: {
        zip: moveLeg.displayAddress.postalCode,
        moveLegType: moveLeg.moveLegType,
        orderType: order.orderType,
        siteIdentity: moveLeg.siteIdentity,
        isIfOpenCalendar: false
      },
      containerPlacement: completedContainerPlacement ?? undefined
    };
    if (isMoveOrRedelivery()) {
      request.serviceAddress = addressState.value;
      request.containerPlacement = completedContainerPlacement!!;
    }
    if (priceDifferenceResponse) request.quoteId = priceDifferenceResponse.quoteId;
    return request;
  };

  const handleCancel = () => {
    setPriceDifferenceResponse(null);
    setServiceabilityError(false);
    addressState.setValue(toServiceAddress(moveLeg.displayAddress));
    onStopScheduling();
  };

  const handleSave = () => {
    setIsSaving(true);
    addressState.displayErrorForAllFields();
    // don't save is selected date is not changed, unless its move or redelivery
    if (isMoveOrRedelivery()) {
      if (
        !addressState.isValid() ||
        !dateState.selectedDateIsValid() ||
        completedContainerPlacement == null
      )
        return;
    } else if (!dateState.selectedDateIsValid() || !dateState.selectedDateIsDifferent()) return;
    setSaveButtonIsLoading(true);

    const request = createUpdateMoveLegRequest();

    const hasAddressChanged = () => {
      if (request.serviceAddress) {
        if (moveLeg.displayAddress.address1 == null) return true;
        return !(
          moveLeg.displayAddress.address1.toLowerCase() ===
          request.serviceAddress?.address1.toLowerCase()
        );
      }
      return false;
    };

    const gtmRequest: GtmScheduleType = {
      transactionId: order.orderId,
      containerId: container.containerId,
      moveLegId: moveLeg.moveLegId,
      moveLegType: moveLeg.moveLegType,
      addressChanged: hasAddressChanged(),
      deliverySiteType: request.containerPlacement?.siteType.toString(),
      containerPlacement: request.containerPlacement?.placement.toString(),
      pavedSurface: request.containerPlacement?.isPavedSurface,
      gated: false, // TODO: This will need to be updated once we introduce feature
      optionalNotes: !!(
        request.containerPlacement && request.containerPlacement.driverNotes.length > 0
      )
    };

    function triggerGtmForSuccess(isSuccess: boolean) {
      if (isSuccess) {
        const eventType = isEditingGtmEvent ? 'success_edit_schedule' : 'success_schedule';
        gtmEvents.pushMoveLegScheduleEvent(eventType, gtmRequest);
      } else {
        const eventType = isEditingGtmEvent ? 'submit_edit_schedule' : 'submit_schedule';
        gtmEvents.pushMoveLegScheduleEvent(eventType, gtmRequest);
      }
    }

    triggerGtmForSuccess(false);
    updateMoveLeg.mutate(request, {
      onSuccess: (response) => {
        triggerGtmForSuccess(true);
        onLoadingComplete();
        if (response.quoteId !== '0') {
          setPriceDifferenceResponse(response);
          return;
        }
        setPriceDifferenceResponse(null);
        setNotification({
          isError: false,
          message: translate(getSuccessMessage(moveLeg))
        });
        refetch();
        onStopScheduling();
      },
      onError: (error: unknown) => {
        const eventType = isEditingGtmEvent ? 'submit_edit_schedule' : 'submit_schedule';
        gtmEvents.errorEvent(
          createGtmErrorRequest(GA_GENERIC_BACKEND_MESSAGE, eventType, 'backend')
        );
        onLoadingComplete();
        if (isStaleDataError(error)) {
          refetchOnFailure();
        } else {
          setNotification({
            message: translate(TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE),
            isError: true
          });
        }
      }
    });
  };

  const openContainerPlacementModal = () => {
    gtmEvents.startPlacementFlow({
      orderId: order.orderId,
      containerId: container.containerId,
      moveLegId: moveLeg.moveLegId,
      moveLegType: moveLeg.moveLegType
    });
    setContainerPlacementOpen(true);
  };

  const handleContainerPlacementFinished = (containerPlacement: ContainerPlacement) => {
    gtmEvents.successPlacementFlow({
      orderId: order.orderId,
      containerId: container.containerId,
      moveLegId: moveLeg.moveLegId,
      moveLegType: moveLeg.moveLegType
    });
    setCompletedContainerPlacement(containerPlacement);
    setContainerPlacementOpen(false);
  };

  const getPriceChangeDescription = () => {
    if (priceDifferenceResponse?.priceDifference == null) return '';
    const priceDifference = Number(priceDifferenceResponse!.priceDifference);

    const context =
      priceDifference > 0 ? PriceDifferenceContext.INCREASE : PriceDifferenceContext.DECREASE;
    const amount = Math.abs(priceDifference).toFixed(2);

    return translate(Tx.Scheduling.PRICE_CHANGE_DESCRIPTION, { context, amount });
  };

  const isMoveOrRedelivery = () =>
    moveLeg.moveLegType === 'REDELIVERY' || moveLeg.moveLegType === 'MOVE';

  const isDatePickerDisabled = () => {
    if (saveButtonIsLoading) return true;
    if (isMoveOrRedelivery()) {
      return !addressState.isValid();
    }
    return priceDifferenceResponse != null;
  };

  const isContainerPlacementEnabled = () =>
    addressState.isValid() && dateState.selectedDateIsValid();

  const isSaveEnabled = () => {
    if (isMoveOrRedelivery()) {
      return (
        addressState.isValid() &&
        dateState.selectedDateIsValid() &&
        completedContainerPlacement != null
      );
    }
    return dateState.selectedDateIsValid() && dateState.selectedDateIsDifferent();
  };

  const checkServiceability = (address: ServiceAddress) => {
    setServiceabilityError(false);
    if (!address.postalCode) return;
    if (addressState.isInSameServiceArea(address.postalCode)) return;
    const request: SameServiceAreaRequest = {
      originalAddress: moveLeg.displayAddress as ServiceAddress,
      updatedAddress: address as ServiceAddress
    };
    isSameServiceAreaCall.mutate(request, {
      onSuccess: (isSameServiceArea) => {
        if (isSameServiceArea) {
          addressState.addInServiceArea(address.postalCode!);
        } else {
          addressState.removeInServiceArea(address.postalCode!);
          setServiceabilityError(true);
        }
      },
      onError: () => {
        setServiceabilityError(true);
      }
    });
  };

  const renderContainerPlacementSection = () => {
    if (completedContainerPlacement == null) {
      return (
        <Grid container {...styles.containerPlacementContainer}>
          <Typography {...styles.containerPlacementTitle}>
            {translate(Tx.Scheduling.CONTAINER_PLACEMENT_TITLE)}
          </Typography>
          <Typography {...styles.containerPlacementSubtitle}>
            {translate(Tx.Scheduling.CONTAINER_PLACEMENT_SUBTITLE)}
          </Typography>
          <StretchableLoadingButton
            isMobile={isMobile}
            disabled={!isContainerPlacementEnabled()}
            onClick={openContainerPlacementModal}
            label={Tx.Scheduling.CONTAINER_PLACEMENT_BUTTON}
          />
        </Grid>
      );
    }
    return (
      <Grid container item {...styles.containerPlacementCompletedContainer}>
        <Grid item>
          <CheckmarkIcon {...styles.containerPlacementCheckmark} />
        </Grid>
        <Grid item>
          <Typography {...styles.containerPlacementCompleted}>
            {translate(Tx.Scheduling.CONTAINER_PLACEMENT_COMPLETED)}
          </Typography>
        </Grid>
      </Grid>
    );
  };

  return (
    <Grid container {...styles.scheduleMoveLeg}>
      {isMoveOrRedelivery() && (
        <>
          {serviceabilityError && (
            <Grid {...styles.addressAlert} data-testid="same-area-alert">
              <PodsAlert
                title={translate(Tx.Scheduling.SERVICE_NOT_SAME_AREA_TITLE)}
                description={translate(Tx.Scheduling.SERVICE_NOT_SAME_AREA_DESCRIPTION)}
                icon={PodsAlertIcon.INFO}
                alertType={PodsAlertType.ERROR}
              />
            </Grid>
          )}
          <MoveLegAddressForm state={addressState} checkServicability={checkServiceability} />
        </>
      )}
      <LocalizationProvider dateAdapter={AdapterDateFns}>
        <DesktopDatePicker
          label={translate(getDatePickerLabelKey(moveLeg))}
          disabled={isDatePickerDisabled()}
          loading={dateState.containerAvailabilityPending}
          renderLoading={() => <div>Loading</div>}
          renderInput={(params) => <DatepickerTextField onClick={showCalendar} params={params} />}
          onClose={() => setIsOpen(false)}
          onChange={(e) => handleSelectDate(e)}
          value={dateState.selectedDate}
          disablePast
          disableOpenPicker={saveButtonIsLoading}
          defaultCalendarMonth={dateState.getCalendarStartDateWithAvailability()}
          open={isOpen}
          views={['day']}
          onMonthChange={(day) => dateState.addContainerAvailabilitiesFor3Months(day!)}
          // Note: later when the next available day is not today's date, it could be next month, so we need to toggle the datepicker forward
          renderDay={(day, selectedDays, pickersDayProps) => (
            <DatePickerDay
              key={day.toISOString()}
              date={day}
              selectedDay={selectedDays[0]}
              pickersDayProps={pickersDayProps}
              isAvailable={dateState.isAvailable}
              onClick={handleSelectDate}
            />
          )}
        />
      </LocalizationProvider>
      {isMoveOrRedelivery() && renderContainerPlacementSection()}
      {priceDifferenceResponse && (
        <Grid data-testid="price-difference-alert" {...styles.priceDifferenceAlert}>
          <PodsAlert
            title={translate(Tx.Scheduling.PRICE_CHANGE_TITLE)}
            description={getPriceChangeDescription()}
            icon={PodsAlertIcon.INFO}
          />
        </Grid>
      )}
      <Grid container {...styles.submitButtons}>
        <CancelButton onClick={handleCancel} disabled={updateMoveLeg.isPending} />
        <SaveButton
          onClick={handleSave}
          disabled={!isSaveEnabled()}
          isLoading={saveButtonIsLoading}
          label={
            priceDifferenceResponse ? TranslationKeys.CommonComponents.CONFIRM_BUTTON : undefined
          }
        />
      </Grid>
      <LegacyContainerPlacementProvider handleFinish={handleContainerPlacementFinished}>
        <LegacyContainerPlacementModal
          open={containerPlacementOpen}
          handleOnClose={() => {
            setContainerPlacementOpen(false);
          }}
        />
      </LegacyContainerPlacementProvider>
    </Grid>
  );
};

const scheduleStyles = (isMobile: boolean) => ({
  scheduleMoveLeg: {
    sx: {
      paddingTop: Design.Primitives.Spacing.md
    }
  },
  addressAlert: {
    sx: {
      paddingBottom: Design.Primitives.Spacing.md
    }
  },
  submitButtons: {
    sx: {
      justifyContent: 'right',
      paddingTop: Design.Primitives.Spacing.md,
      gap: Design.Primitives.Spacing.xxs
    }
  },
  priceDifferenceAlert: {
    sx: { paddingTop: Design.Primitives.Spacing.xxs }
  },
  infoIcon: {
    sx: { height: Design.Primitives.Spacing.sm, width: Design.Primitives.Spacing.sm }
  },
  containerPlacementContainer: {
    sx: {
      paddingTop: Design.Primitives.Spacing.md,
      gap: Design.Primitives.Spacing.xxs
    }
  },
  containerPlacementTitle: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.LgBold
    }
  },
  containerPlacementSubtitle: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.Sm
    }
  },
  containerPlacementCompletedContainer: {
    sx: {
      alignItems: 'center',
      paddingTop: Design.Primitives.Spacing.md
    },
    spacing: '0.5rem'
  },
  containerPlacementCompleted: {
    sx: {
      ...(isMobile
        ? Design.Alias.Text.BodyUniversal.MdBold
        : Design.Alias.Text.BodyUniversal.LgBold),
      color: Design.Primitives.Color.Semantic.success
    }
  },
  containerPlacementCheckmark: {
    sx: {
      ...(isMobile ? { height: '14px', width: '14px' } : { height: '16px', width: '16px' }),
      path: { fill: 'green' }
    }
  }
});
