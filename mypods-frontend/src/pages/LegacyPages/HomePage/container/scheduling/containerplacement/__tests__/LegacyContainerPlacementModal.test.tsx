import { fireEvent, render, screen } from '@testing-library/react';
import { vi } from 'vitest';
import userEvent from '@testing-library/user-event';
import { TranslationKeys } from '../../../../../../../locales/TranslationKeys';
import { act } from 'react';
import {
  createMoveLeg,
  createOrder,
  createContainer
} from '../../../../../../../testUtils/MyPodsFactories';
import { testQueryClient } from '../../../../../../../testUtils/RenderHelpers';
import { LegacyContainerPlacementProvider } from '../context/LegacyContainerPlacementProvider';
import { QueryClientProvider } from '@tanstack/react-query';
import { ContainerProvider } from '../../../../../../../context/ContainerContext';
import { formatAddress } from '../../../../../../../networkRequests/responseEntities/CustomerEntities';
import {
  ContainerPlacement,
  ContainerPlacementSiteType,
  FinalContainerPlacement
} from '../../../../../../../domain/OrderEntities';
import {
  ContainerPlacementScreenName,
  SiteTypeChildScreens
} from '../../../../../../HomePage/container/scheduling/containerplacement/screenmanager/ContainerPlacementModalTypes';
import { defaultInitialDeliveryPlacement } from '../../../../../../HomePage/container/scheduling/containerplacement/screenmanager/useContainerPlacementScreenManager';
import { LegacyContainerPlacementModal } from '../LegacyContainerPlacementModal';
import { MoveLegProvider } from '../../../../../../HomePage/container/moveleg/MoveLegContext';

const Tx = TranslationKeys.HomePage.ContainerPlacement;

const flows: ContainerPlacementScreenName[] = [
  'PAVED_SURFACE',
  'PLACEMENT_TIPS',
  'SITE_TYPE',
  'DRIVER_NOTES'
];

const actions = {
  enterPavedSurface: async (key: string = TranslationKeys.CommonComponents.YES_BUTTON) => {
    const pavedSurfaceButton = screen.getByRole('button', {
      name: key
    });
    await act(async () => userEvent.click(pavedSurfaceButton));
  },
  enterPlacementTips: async (key: string = TranslationKeys.CommonComponents.NEXT_BUTTON) => {
    const placementTipsNext = screen.getByRole('button', {
      name: key
    });
    await act(async () => userEvent.click(placementTipsNext));
  },
  enterSiteType: async (key: string = Tx.SiteTypeScreen.Buttons.DRIVEWAY) => {
    const siteTypeButton = screen.getByRole('button', {
      name: key
    });
    await act(async () => userEvent.click(siteTypeButton));
  },
  enterDriverNotes: async (driverNotes: string) => {
    const driverNotesArea = screen.getByLabelText(Tx.DriverNotes.LABEL);
    await act(async () => fireEvent.change(driverNotesArea, { target: { value: driverNotes } }));
    const driverNotesNext = screen.getByRole('button', {
      name: TranslationKeys.CommonComponents.NEXT_BUTTON
    });
    await act(async () => userEvent.click(driverNotesNext));
  },
  enterSiteTypeChildScreen: async (altText: SiteTypeChildScreens) => {
    const image = screen.getByAltText(altText);
    await act(async () => userEvent.click(image));
  },
  enterFinalSelectionScreen: async (altText: FinalContainerPlacement) => {
    const drivewayTypeButton = screen.getByAltText(altText);
    await act(async () => userEvent.click(drivewayTypeButton));
  },
  completeFormStartingAt: async (
    startingFromScreen: ContainerPlacementScreenName = 'PAVED_SURFACE'
  ) => {
    let indexToStartAt = flows.findIndex((it) => it === startingFromScreen);
    const flowsToFill = new Set(flows.slice(indexToStartAt));

    await actions.completeFlows(flowsToFill);
  },
  completeFormAfter: async (afterScreen: ContainerPlacementScreenName = 'PAVED_SURFACE') => {
    let index = flows.findIndex((it) => it === afterScreen);
    let flowsToFill = new Set(flows.slice(index + 1));

    await actions.completeFlows(flowsToFill);
  },
  completeUpTo: async (upToExcludingScreen: ContainerPlacementScreenName = 'PAVED_SURFACE') => {
    let indexUpTo = flows.findIndex((it) => it === upToExcludingScreen);
    const flowsToFill = new Set(flows.slice(0, indexUpTo));

    await actions.completeFlows(flowsToFill);
  },
  completeFlows: async (flowsToFill: Set<ContainerPlacementScreenName>) => {
    if (flowsToFill.has('PAVED_SURFACE')) await actions.enterPavedSurface();
    if (flowsToFill.has('PLACEMENT_TIPS')) await actions.enterPlacementTips();
    if (flowsToFill.has('SITE_TYPE')) {
      await actions.enterSiteType(Tx.SiteTypeScreen.Buttons.DRIVEWAY);
      await actions.enterSiteTypeChildScreen('DRIVEWAY_STRAIGHT');
      await actions.enterSiteTypeChildScreen('DRIVEWAY_STRAIGHT_CLOSE');
      await actions.enterFinalSelectionScreen('DRIVEWAY_STRAIGHT_CLOSE_REAR');
    }
    if (flowsToFill.has('DRIVER_NOTES')) {
      await actions.enterDriverNotes('Some driver notes');
    }
  },
  clickFinish: async () => {
    const finishButton = screen.getByRole('button', { name: Tx.ReviewScreen.FINISH_BUTTON });
    await act(async () => userEvent.click(finishButton));
  }
};

describe('Container Placement Modal', () => {
  const moveLeg = createMoveLeg();
  const container = createContainer({ moveLegs: [moveLeg] });
  const order = createOrder({ containers: [container] });

  const mockHandleFinish = vi.fn();
  const mockHandleOnClose = vi.fn();

  const renderContainerPlacement = (existingPlacement?: ContainerPlacement) => {
    return render(
      <QueryClientProvider client={testQueryClient()}>
        <ContainerProvider state={{ order, container }}>
          <MoveLegProvider
            moveLeg={moveLeg}
            lastMoveLegId={moveLeg.moveLegId}
            isLastRenderedMoveLeg={true}>
            <LegacyContainerPlacementProvider
              handleFinish={mockHandleFinish}
              existingPlacement={existingPlacement}>
              <LegacyContainerPlacementModal open={true} handleOnClose={mockHandleOnClose} />
            </LegacyContainerPlacementProvider>
          </MoveLegProvider>
        </ContainerProvider>
      </QueryClientProvider>
    );
  };

  it('when close is clicked, close the modal', async () => {
    renderContainerPlacement();

    const closeButton = screen.getByLabelText('close');
    await act(async () => userEvent.click(closeButton));

    expect(mockHandleOnClose).toHaveBeenCalled();
  });

  it('displays the title, subtitle and default placement details', async () => {
    renderContainerPlacement();

    await actions.completeFormStartingAt();

    expect(screen.getByText(Tx.ReviewScreen.TITLE)).toBeInTheDocument();
    expect(screen.getByText(Tx.ReviewScreen.SUBTITLE)).toBeInTheDocument();
    expect(screen.getByText(Tx.ReviewScreen.Labels.ADDRESS)).toBeInTheDocument();
    expect(screen.getByText(formatAddress(moveLeg.displayAddress))).toBeInTheDocument();
    expect(screen.getByText(Tx.ReviewScreen.Labels.PLACEMENT_SITE)).toBeInTheDocument();
    expect(screen.getByText(Tx.ReviewScreen.Labels.CONTAINER_PLACEMENT)).toBeInTheDocument();
    expect(screen.getByAltText('final-selection-image'));
    expect(screen.getByText(Tx.ReviewScreen.Labels.DRIVER_INSTRUCTIONS)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: Tx.ReviewScreen.FINISH_BUTTON })).toBeEnabled();
  });

  describe('Edit buttons', () => {
    it('should navigate to the placement site when clicked', async () => {
      renderContainerPlacement();

      await actions.completeFormStartingAt();

      let editButton = screen.getByTestId('modal-placement-site-edit-button');
      await act(async () => userEvent.click(editButton));

      expect(screen.getByText(Tx.SiteTypeScreen.TITLE)).toBeInTheDocument();
    });
    it('should navigate to the first set of images for the selected site type when clicked', async () => {
      renderContainerPlacement();
      await actions.completeFormStartingAt();

      let editButton = screen.getByTestId('modal-container-placement-edit-button');
      await act(async () => userEvent.click(editButton));

      expect(screen.getByText(Tx.SiteTypeScreen.Prompts.Driveway.SITE_TYPE)).toBeInTheDocument();
    });
    it('should navigate to the driver notes when clicked', async () => {
      renderContainerPlacement();
      await actions.completeFormStartingAt();

      let editButton = screen.getByTestId('modal-driver-notes-edit-button');
      await act(async () => userEvent.click(editButton));

      expect(screen.getByText(Tx.DriverNotes.TITLE)).toBeInTheDocument();
    });
  });

  describe('PavedSurfaceScreen', () => {
    it('sets placement data with isPavedSurface true when modal choosing "yes"', async () => {
      renderContainerPlacement();

      await actions.enterPavedSurface(TranslationKeys.CommonComponents.YES_BUTTON);
      await actions.completeFormAfter('PAVED_SURFACE');
      await actions.clickFinish();

      expect(mockHandleFinish).toHaveBeenCalledWith(
        expect.objectContaining({ isPavedSurface: true })
      );
    });

    it('sets placement data with isPavedSurface false when modal choosing "no"', async () => {
      renderContainerPlacement();

      await actions.enterPavedSurface(TranslationKeys.CommonComponents.NO_BUTTON);
      await actions.completeFormAfter('PAVED_SURFACE');
      await actions.clickFinish();

      expect(mockHandleFinish).toHaveBeenCalledWith(
        expect.objectContaining({ isPavedSurface: false })
      );
    });
  });

  describe('Placement Tips', () => {
    it('placement tips is a simple page to read through in the flow', async () => {
      renderContainerPlacement();

      await actions.completeUpTo('PLACEMENT_TIPS');
      await actions.enterPlacementTips();
      await actions.completeFormAfter('PLACEMENT_TIPS');

      expect(screen.getByText(Tx.ReviewScreen.TITLE)).toBeInTheDocument();
    });
  });

  describe('SiteTypeScreen', () => {
    beforeEach(async () => {
      renderContainerPlacement();

      await actions.completeUpTo('SITE_TYPE');
    });

    type DrivewayTestCase = {
      initialDrivewayType: SiteTypeChildScreens;
      containerLocationInDriveway: SiteTypeChildScreens;
      placement: FinalContainerPlacement;
      finalSelectionImage: string;
    };
    it.each<DrivewayTestCase>([
      {
        initialDrivewayType: 'DRIVEWAY_STRAIGHT',
        containerLocationInDriveway: 'DRIVEWAY_STRAIGHT_CLOSE',
        placement: 'DRIVEWAY_STRAIGHT_CLOSE_REAR',
        finalSelectionImage: '/driveway/driveway-straight-close-rear.jpg'
      },
      {
        initialDrivewayType: 'DRIVEWAY_STRAIGHT',
        containerLocationInDriveway: 'DRIVEWAY_STRAIGHT_CLOSE',
        placement: 'DRIVEWAY_STRAIGHT_CLOSE_CAB',
        finalSelectionImage: '/driveway/driveway-straight-close-cab.jpg'
      },
      {
        initialDrivewayType: 'DRIVEWAY_STRAIGHT',
        containerLocationInDriveway: 'DRIVEWAY_STRAIGHT_MIDDLE',
        placement: 'DRIVEWAY_STRAIGHT_MIDDLE_REAR',
        finalSelectionImage: '/driveway/driveway-straight-middle-rear.jpg'
      },
      {
        initialDrivewayType: 'DRIVEWAY_STRAIGHT',
        containerLocationInDriveway: 'DRIVEWAY_STRAIGHT_MIDDLE',
        placement: 'DRIVEWAY_STRAIGHT_MIDDLE_CAB',
        finalSelectionImage: '/driveway/driveway-straight-middle-cab.jpg'
      },
      {
        initialDrivewayType: 'DRIVEWAY_STRAIGHT',
        containerLocationInDriveway: 'DRIVEWAY_STRAIGHT_FAR',
        placement: 'DRIVEWAY_STRAIGHT_FAR_CAB',
        finalSelectionImage: '/driveway/driveway-straight-far-cab.jpg'
      },
      {
        initialDrivewayType: 'DRIVEWAY_STRAIGHT',
        containerLocationInDriveway: 'DRIVEWAY_STRAIGHT_FAR',
        placement: 'DRIVEWAY_STRAIGHT_FAR_REAR',
        finalSelectionImage: '/driveway/driveway-straight-far-rear.jpg'
      }
    ])(
      'sets driveway site type data with placement: $placement',
      async ({
        initialDrivewayType,
        containerLocationInDriveway,
        placement,
        finalSelectionImage
      }) => {
        await actions.enterSiteType(Tx.SiteTypeScreen.Buttons.DRIVEWAY);
        await actions.enterSiteTypeChildScreen(initialDrivewayType);
        await actions.enterSiteTypeChildScreen(containerLocationInDriveway);
        await actions.enterFinalSelectionScreen(placement);
        await actions.completeFormStartingAt('DRIVER_NOTES');

        const reviewImage = screen.getByAltText('final-selection-image') as HTMLImageElement;
        expect(reviewImage.src).toContain(finalSelectionImage);

        await actions.clickFinish();

        const siteType: ContainerPlacementSiteType = 'DRIVEWAY';
        expect(mockHandleFinish).toHaveBeenCalledWith(
          expect.objectContaining({ siteType, placement })
        );
      }
    );

    type StreetTestCase = Pick<DrivewayTestCase, 'placement' | 'finalSelectionImage'>;
    it.each<StreetTestCase>([
      { placement: 'STREET_FRONT_CAB', finalSelectionImage: '/street/street-front-cab.jpg' },
      { placement: 'STREET_LEFT_CAB', finalSelectionImage: '/street/street-left-cab.jpg' },
      { placement: 'STREET_BACK_CAB', finalSelectionImage: '/street/street-back-cab.jpg' },
      { placement: 'STREET_RIGHT_CAB', finalSelectionImage: '/street/street-right-cab.jpg' }
    ])(
      'sets street site type data with placement: $placement',
      async ({ placement, finalSelectionImage }) => {
        await actions.enterSiteType(Tx.SiteTypeScreen.Buttons.STREET);
        await actions.enterFinalSelectionScreen(placement);
        await actions.completeFormStartingAt('DRIVER_NOTES');

        const reviewImage = screen.getByAltText('final-selection-image') as HTMLImageElement;
        expect(reviewImage.src).toContain(finalSelectionImage);

        await actions.clickFinish();
        const siteType: ContainerPlacementSiteType = 'STREET';
        expect(mockHandleFinish).toHaveBeenCalledWith(
          expect.objectContaining({ siteType, placement })
        );
      }
    );

    type ParkingLotCase = Pick<
      DrivewayTestCase,
      'containerLocationInDriveway' | 'placement' | 'finalSelectionImage'
    >;
    it.each<ParkingLotCase>([
      {
        containerLocationInDriveway: 'PARKING_LOT_FRONT',
        placement: 'PARKING_LOT_FRONT_01',
        finalSelectionImage: '/parking-lot/parking-lot-front-01-cab.jpg'
      },
      {
        containerLocationInDriveway: 'PARKING_LOT_FRONT',
        placement: 'PARKING_LOT_FRONT_02',
        finalSelectionImage: '/parking-lot/parking-lot-front-02-cab.jpg'
      },
      {
        containerLocationInDriveway: 'PARKING_LOT_FRONT',
        placement: 'PARKING_LOT_FRONT_03',
        finalSelectionImage: '/parking-lot/parking-lot-front-03-rear.jpg'
      },
      {
        containerLocationInDriveway: 'PARKING_LOT_FRONT',
        placement: 'PARKING_LOT_FRONT_04',
        finalSelectionImage: '/parking-lot/parking-lot-front-04-cab.jpg'
      },
      {
        containerLocationInDriveway: 'PARKING_LOT_BACK',
        placement: 'PARKING_LOT_BACK_01',
        finalSelectionImage: '/parking-lot/parking-lot-back-01-cab.jpg'
      },
      {
        containerLocationInDriveway: 'PARKING_LOT_BACK',
        placement: 'PARKING_LOT_BACK_02',
        finalSelectionImage: '/parking-lot/parking-lot-back-02-cab.jpg'
      },
      {
        containerLocationInDriveway: 'PARKING_LOT_BACK',
        placement: 'PARKING_LOT_BACK_03',
        finalSelectionImage: '/parking-lot/parking-lot-back-03-cab.jpg'
      },
      {
        containerLocationInDriveway: 'PARKING_LOT_BACK',
        placement: 'PARKING_LOT_BACK_04',
        finalSelectionImage: '/parking-lot/parking-lot-back-04-cab.jpg'
      },
      {
        containerLocationInDriveway: 'PARKING_LOT_LEFT',
        placement: 'PARKING_LOT_LEFT_01',
        finalSelectionImage: '/parking-lot/parking-lot-left-01-rear.jpg'
      },
      {
        containerLocationInDriveway: 'PARKING_LOT_LEFT',
        placement: 'PARKING_LOT_LEFT_02',
        finalSelectionImage: '/parking-lot/parking-lot-left-02-cab.jpg'
      },
      {
        containerLocationInDriveway: 'PARKING_LOT_LEFT',
        placement: 'PARKING_LOT_LEFT_03',
        finalSelectionImage: '/parking-lot/parking-lot-left-03-cab.jpg'
      },
      {
        containerLocationInDriveway: 'PARKING_LOT_LEFT',
        placement: 'PARKING_LOT_LEFT_04',
        finalSelectionImage: '/parking-lot/parking-lot-left-04-cab.jpg'
      },
      {
        containerLocationInDriveway: 'PARKING_LOT_RIGHT',
        placement: 'PARKING_LOT_RIGHT_01',
        finalSelectionImage: '/parking-lot/parking-lot-right-01-cab.jpg'
      },
      {
        containerLocationInDriveway: 'PARKING_LOT_RIGHT',
        placement: 'PARKING_LOT_RIGHT_02',
        finalSelectionImage: '/parking-lot/parking-lot-right-02-rear.jpg'
      },
      {
        containerLocationInDriveway: 'PARKING_LOT_RIGHT',
        placement: 'PARKING_LOT_RIGHT_03',
        finalSelectionImage: '/parking-lot/parking-lot-right-03-cab.jpg'
      },
      {
        containerLocationInDriveway: 'PARKING_LOT_RIGHT',
        placement: 'PARKING_LOT_RIGHT_04',
        finalSelectionImage: '/parking-lot/parking-lot-right-04-cab.jpg'
      }
    ])(
      'sets parking lot site type data with placement: $placement',
      async ({ containerLocationInDriveway, placement, finalSelectionImage }) => {
        await actions.enterSiteType(Tx.SiteTypeScreen.Buttons.PARKING_LOT);
        await actions.enterSiteTypeChildScreen(containerLocationInDriveway);
        await actions.enterFinalSelectionScreen(placement);
        await actions.completeFormStartingAt('DRIVER_NOTES');

        const reviewImage = screen.getByAltText('final-selection-image') as HTMLImageElement;
        expect(reviewImage.src).toContain(finalSelectionImage);

        await actions.clickFinish();
        const siteType: ContainerPlacementSiteType = 'PARKING_LOT';
        expect(mockHandleFinish).toHaveBeenCalledWith(
          expect.objectContaining({ siteType, placement })
        );
      }
    );
  });

  describe('Driver Notes', () => {
    it('sets driver notes data on the review screen', async () => {
      const driverNotes = 'My Driver Notes';
      renderContainerPlacement();

      await actions.completeUpTo('DRIVER_NOTES');
      expect(screen.getByText(Tx.DriverNotes.TITLE)).toBeInTheDocument();
      await actions.enterDriverNotes(driverNotes);

      expect(screen.getByText(driverNotes)).toBeInTheDocument();

      await actions.clickFinish();
      expect(mockHandleFinish).toHaveBeenCalledWith(
        expect.objectContaining({ driverNotes: driverNotes })
      );
    });
  });

  describe('Existing Container Placement ', () => {
    it('Allows existing container placement to set, and should take you to the review screen on open', async () => {
      const existingContainerPlacement = defaultInitialDeliveryPlacement;
      renderContainerPlacement(existingContainerPlacement);

      expect(screen.getByText(Tx.ReviewScreen.TITLE)).toBeInTheDocument();
      await actions.clickFinish();
      expect(mockHandleFinish).toHaveBeenCalledWith(existingContainerPlacement);
    });
  });

  describe('Forward and Back navigation buttons', () => {
    it('should allow forward and back movement', async () => {
      renderContainerPlacement();

      expect(screen.queryAllByTestId('ChevronLeftIcon')).toHaveLength(0);
      expect(screen.queryAllByTestId('ChevronRightIcon')).toHaveLength(0);

      await actions.enterPavedSurface();

      expect(screen.getByTestId('ChevronLeftIcon')).toBeInTheDocument();
      expect(screen.queryAllByTestId('ChevronRightIcon')).toHaveLength(0);

      await act(async () => userEvent.click(screen.getByTestId('ChevronLeftIcon')));

      expect(screen.queryAllByTestId('ChevronLeftIcon')).toHaveLength(0);
      expect(screen.getByTestId('ChevronRightIcon')).toBeInTheDocument();

      await act(async () => userEvent.click(screen.getByTestId('ChevronRightIcon')));

      expect(screen.queryAllByTestId('ChevronRightIcon')).toHaveLength(0);
      expect(screen.getByTestId('ChevronLeftIcon')).toBeInTheDocument();
    });
  });
});
