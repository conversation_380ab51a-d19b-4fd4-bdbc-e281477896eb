import { Grid, styled, TextField, Typography } from '@mui/material';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Design } from '../../../../../../../helpers/Design';
import { ContainerPlacementActionButton } from '../../../../../../HomePage/container/scheduling/containerplacement/ContainerPlacementActionButton';
import { ContainerPlacementContent } from '../../../../../../HomePage/container/scheduling/containerplacement/ContainerPlacementContent';
import { ContainerPlacementHeader } from '../../../../../../HomePage/container/scheduling/containerplacement/ContainerPlacementHeader';
import { useLegacyContainerPlacementContext } from '../context/LegacyContainerPlacementContext';
import { TranslationKeys } from '../../../../../../../locales/TranslationKeys';

const maxCharacterLimit = 150;
const Tx = TranslationKeys.HomePage.ContainerPlacement.DriverNotes;

export const LegacyDrivewayNotesScreen = () => {
  const { t: translate } = useTranslation();
  const { containerPlacement, setContainerPlacement, manager } =
    useLegacyContainerPlacementContext();

  const handleOnChange = (value: string) => {
    setContainerPlacement((prevState) => ({
      ...prevState,
      driverNotes: value
    }));
  };

  const handleNextClicked = () => {
    manager.selectChoice(containerPlacement.driverNotes);
  };

  const helperText = () =>
    translate(Tx.HELPER_TEXT, {
      count: containerPlacement.driverNotes.length,
      total: maxCharacterLimit
    });

  return (
    <ContainerPlacementContent>
      <ContainerPlacementHeader titleKey={Tx.TITLE} subTitleKey={Tx.SUBTITLE} />
      <Grid item container>
        <Typography {...styles.label}>{translate(Tx.LABEL)}</Typography>
        <BlueTextField
          value={containerPlacement.driverNotes}
          InputProps={{
            inputProps: {
              maxLength: maxCharacterLimit
            }
          }}
          multiline
          fullWidth
          rows={3}
          label={translate(Tx.LABEL)}
          helperText={helperText()}
          onChange={(event) => {
            handleOnChange(event.target.value);
          }}
        />
      </Grid>
      <ContainerPlacementActionButton
        label={translate(TranslationKeys.CommonComponents.NEXT_BUTTON)}
        onClick={handleNextClicked}
      />
    </ContainerPlacementContent>
  );
};

const styles = {
  label: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.Md,
      marginBottom: '8px'
    }
  }
};

const BlueTextField = styled(TextField)({
  size: 'medium',
  backgroundColor: 'white',
  '& .MuiFormLabel-root': {
    color: Design.Alias.Color.accent900
  },
  '& label.Mui-focused': {
    color: Design.Alias.Color.secondary500
  },
  '& .MuiOutlinedInput-root': {
    '&:hover fieldset': {
      borderColor: Design.Alias.Color.secondary500,
      color: Design.Alias.Color.secondary500
    },
    '&.Mui-focused fieldset': {
      borderColor: Design.Alias.Color.secondary500
    }
  },
  '&.Mui-focused fieldset': {
    borderColor: Design.Alias.Color.neutral300
  },
  '& .MuiFormHelperText-root': {
    ...Design.Alias.Text.BodyUniversal.Xs,
    textAlign: 'end',
    color: '#000'
  },
  '& .MuiFormHelperText-root.Mui-error': {
    backgroundColor: Design.Alias.Color.neutral100,
    color: Design.Alias.Color.primary500,
    margin: 0,
    paddingLeft: 10
  }
});
