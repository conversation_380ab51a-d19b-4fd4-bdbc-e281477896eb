import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  ReviewContent,
  ReviewContentImage,
  ReviewContentText
} from '../../../../../../HomePage/container/scheduling/containerplacement/screens/ReviewContent';
import { formatAddress } from '../../../../../../../networkRequests/responseEntities/CustomerEntities';
import { getContainerPlacementSiteTypeButton } from '../../../../../../../locales/TranslationConstants';
import { getImageForFinalPlacement } from '../../../../../../HomePage/container/scheduling/containerplacement/screenmanager/getImages';
import { TranslationKeys } from '../../../../../../../locales/TranslationKeys';
import { useLegacyContainerPlacementContext } from '../context/LegacyContainerPlacementContext';
import useMoveLegContext from '../../../../../../HomePage/container/moveleg/MoveLegContext';

const Tx = TranslationKeys.HomePage.ContainerPlacement;

// -- impls --
export const LegacyReviewContainerPlacement: React.FC = () => {
  const { t: translate } = useTranslation();
  const {
    scheduling: { addressState }
  } = useMoveLegContext();
  const { containerPlacement, manager } = useLegacyContainerPlacementContext();

  return (
    <>
      <ReviewContent
        label={translate(Tx.ReviewScreen.Labels.ADDRESS)}
        testId="modal-address"
        reviewBody={<ReviewContentText text={formatAddress(addressState.value)} />}
      />
      <ReviewContent
        label={translate(Tx.ReviewScreen.Labels.PLACEMENT_SITE)}
        testId="modal-placement-site"
        reviewBody={
          <ReviewContentText
            text={translate(getContainerPlacementSiteTypeButton(containerPlacement.siteType))}
          />
        }
        handleEditClick={() => {
          manager.goto('SITE_TYPE');
        }}
      />
      <ReviewContent
        label={translate(Tx.ReviewScreen.Labels.CONTAINER_PLACEMENT)}
        testId="modal-container-placement"
        reviewBody={
          <ReviewContentImage
            altText="final-selection-image"
            src={getImageForFinalPlacement(containerPlacement.placement)}
          />
        }
        handleEditClick={() => {
          manager.gotoSiteTypeSelectedChild();
        }}
      />
      <ReviewContent
        label={translate(Tx.ReviewScreen.Labels.DRIVER_INSTRUCTIONS)}
        testId="modal-driver-notes"
        reviewBody={<ReviewContentText text={containerPlacement.driverNotes} />}
        handleEditClick={() => {
          manager.goto('DRIVER_NOTES');
        }}
      />
    </>
  );
};
