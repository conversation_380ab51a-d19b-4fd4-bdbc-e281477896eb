import { Grid, Typography, useMediaQuery } from '@mui/material';
import React, { useState } from 'react';
import Divider from '@mui/material/Divider';
import { addDays } from 'date-fns';
import useContainerContext from '../../../../../../context/ContainerContext';
import useSingleOrderContext from '../../../../../../context/SingleOrderContext';
import { theme } from '../../../../../../PodsTheme';
import { useGtmEvents } from '../../../../../../config/google/useGtmEvents';
import {
  ContainerPlacement,
  ScheduledStatus,
  serviceCountdownTypeForMoveLegType,
  UpdateMoveLegRequest
} from '../../../../../../domain/OrderEntities';
import {
  ContainerProgressLine,
  ProgressLineVariant
} from '../../../../../HomePage/container/ContainerProgressLine';
import { formatDate, isWithin24Hours } from '../../../../../../helpers/dateHelpers';
import { getDateLabels } from '../../../../../../locales/TranslationConstants';
import { InTransitMoveLeg } from '../../../../../HomePage/container/moveleg/current/InTransitMoveLeg';
import { CallForSpecialAssistance } from '../../../../../HomePage/container/moveleg/CallForSpecialAssistance';
import { EditButton } from '../../../../../../components/buttons/EditButton';
import { ServiceCountdownAlert } from '../../../../../HomePage/container/serviceCountdown/ServiceCountdownAlert';
import { DateComponent } from '../../../../../HomePage/container/moveleg/DateComponent';
import { AddressComponent } from '../../../../../HomePage/container/moveleg/AddressComponent';
import { formatAddress } from '../../../../../../networkRequests/responseEntities/CustomerEntities';
import { PickupWindow } from '../../../../../HomePage/container/moveleg/PickupWindow';
import { ScheduleMoveLegButton } from '../../../../../HomePage/container/moveleg/current/ScheduleMoveLegButton';
import { CallToScheduleComponent } from '../../../../../HomePage/container/moveleg/CallToScheduleComponent';
import { sharedMoveLegStyles } from '../../../../../HomePage/container/moveleg/sharedMoveLegStyles';
import { LegacyVisitContainerSection } from '../LegacyVisitContainerSection';
import { LegacyReviewInitialDeliveryAlert } from '../../../../../../components/legacyComponents/alert/LegacyReviewInitialDeliveryAlert';
import { LegacyScheduleMoveLeg } from '../../scheduling/LegacyScheduleMoveLeg';
import { REDESIGNED_SCHEDULING, useFeatureFlags } from '../../../../../../helpers/useFeatureFlags';
import { ManagePickupPanel } from '../../../../../HomePage/Scheduling/ManagePickupPanel';
import { ManageVisitContainer } from '../../../../../HomePage/Scheduling/ManageVisitContainer';
import { ManageDropOffContainer } from '../../../../../HomePage/Scheduling/ManageDropOffContainer';
import useMoveLegContext from '../../../../../HomePage/container/moveleg/MoveLegContext';
import { GtmScheduleType } from '../../../../../../config/google/GoogleEntities';
import {
  createGtmErrorRequest,
  GA_GENERIC_BACKEND_MESSAGE
} from '../../../../../../config/google/googleAnalyticsUtils';
import { isStaleDataError } from '../../../../../HomePage/utils';
import { UpdateMoveLegResponse } from '../../../../../../networkRequests/responseEntities/OrderAPIEntities';
import useLegacyOrdersContext from '../../../../../../context/legacy/LegacyOrdersContext';
import { useLegacyUpdateMoveLeg } from '../../../../../../networkRequests/legacy/mutations/useLegacyUpdateMoveLeg';

export const LegacyMoveLegSection = () => {
  const {
    moveLegScheduling: {
      currentlySelectedMoveLeg,
      editMoveLegScheduling,
      stopMoveLegScheduling,
      isSaving,
      setIsSaving,
      isCancelling
    }
  } = useSingleOrderContext();
  const {
    scheduling: { addressState, dateState }
  } = useMoveLegContext();
  const { refetch, refetchOnFailure } = useLegacyOrdersContext();
  const updateMoveLeg = useLegacyUpdateMoveLeg();
  const gtmEvents = useGtmEvents();
  const { isRedesignedSchedulingEnabled } = useFeatureFlags([REDESIGNED_SCHEDULING]);
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = moveLegStyles(isMobile);
  const { moveLeg, isLastMoveLeg, isLastRenderedMoveLeg, title } = useMoveLegContext();
  const { order, container } = useContainerContext();
  const [priceDifferenceResponse, setPriceDifferenceResponse] =
    useState<UpdateMoveLegResponse | null>();
  const [isEditingGtmEvent, setIsEditingGtmEvent] = useState<boolean>(false);
  const [isPickupPanelOpen, setIsPickupPanelOpen] = useState(false);
  const [isVisitDrawerOpen, setIsVisitDrawerOpen] = useState(false);
  const [isDropOffDrawerOpen, setIsDropOffDrawerOpen] = useState(false);
  const [saveButtonIsLoading, setSaveButtonIsLoading] = useState<boolean>(false);
  const [showInitialDeliveryAlert, setShowInitialDeliveryAlert] = useState<boolean>(
    moveLeg.moveLegType === 'INITIAL_DELIVERY' && !order.initialDeliveryPlacementIsReviewed
  );

  const gtmSchedulePayLoad = {
    transactionId: order.orderId,
    moveLegId: moveLeg.moveLegId,
    moveLegType: moveLeg.moveLegType.toString(),
    containerId: container.containerId
  };
  // -- move leg helpers --
  function isCallToScheduleLeg(): boolean {
    return (
      moveLeg.moveLegType === 'WAREHOUSE_TO_WAREHOUSE' &&
      moveLeg.scheduledStatus === 'UNSCHEDULED' &&
      !shouldCallForAssistance()
    );
  }

  const isUnscheduledContainerVisit =
    moveLeg.moveLegType === 'VISIT_CONTAINER' && moveLeg.containerVisitDate == null;
  const isScheduledContainerVisit =
    moveLeg.moveLegType === 'VISIT_CONTAINER' && moveLeg.containerVisitDate != null;
  const isSchedulingMoveLeg = currentlySelectedMoveLeg === moveLeg && !isCancelling;
  const { startSchedule, startEditSchedule } = useGtmEvents();

  const hideInitialDeliveryAlert = () => {
    setShowInitialDeliveryAlert(false);
  };

  const isMoveOrRedelivery = () =>
    moveLeg.moveLegType === 'REDELIVERY' || moveLeg.moveLegType === 'MOVE';

  const scheduledStatusToProgressLineVariant = (
    scheduledStatus: ScheduledStatus
  ): ProgressLineVariant => {
    if (scheduledStatus === 'UNSCHEDULED') {
      return 'FADED_DASHED';
    }
    return 'DASHED';
  };

  const shouldRenderScheduleButton = () => {
    if (!moveLeg.isSchedulableOnline) return false;
    if (moveLeg.isCityService) return false;
    if (moveLeg.isCrossBorder) return false;
    if (moveLeg.isHawaii) return false;
    if (isUnscheduledContainerVisit) return true;
    if (moveLeg.scheduledStatus !== 'UNSCHEDULED') return false;
    return true;
  };

  const shouldRenderEditButton = () => {
    if (isSchedulingMoveLeg) return false;
    if (!moveLeg.isSchedulableOnline) return false;
    if (moveLeg.moveLegType === 'VISIT_CONTAINER') return false;
    if (moveLeg.scheduledStatus !== 'FUTURE') return false;
    if (isWithin24Hours(moveLeg.scheduledDate)) return false;
    if (moveLeg.isCrossBorder) return false;
    if (moveLeg.isHawaii) return false;
    if (moveLeg.isCityService) return false;
    return true;
  };

  const shouldCallForAssistance = () => {
    if (moveLeg.isTransitLeg) return false;
    if (moveLeg.isCrossBorder) return true;
    if (moveLeg.isHawaii) return true;
    if (moveLeg.isCityService) return true;
    return false;
  };

  // -- schedule & edit behavior --
  const toggleEditMoveLegOn = () => {
    editMoveLegScheduling(moveLeg);
  };

  const toggleMoveLegDrawer = () => {
    if (serviceCountdownTypeForMoveLegType(moveLeg.moveLegType) === 'VISIT') {
      setIsVisitDrawerOpen(true);
    } else if (serviceCountdownTypeForMoveLegType(moveLeg.moveLegType) === 'DROPOFF') {
      setIsDropOffDrawerOpen(true);
    } else if (serviceCountdownTypeForMoveLegType(moveLeg.moveLegType) === 'PICKUP') {
      setIsPickupPanelOpen(true);
    } else {
      toggleEditMoveLegOn();
    }
  };

  const toggleEdit = () => {
    setIsEditingGtmEvent(true);
    if (isRedesignedSchedulingEnabled()) {
      toggleMoveLegDrawer();
    } else {
      toggleEditMoveLegOn();
    }
  };

  const onPickupPanelClose = () => {
    setIsPickupPanelOpen(false);
    // TODO: clear the date out, but date state can't be null.
  };

  // -- dates --
  const dateLabels = getDateLabels(moveLeg.moveLegType);

  function getFirstDateValue() {
    if (moveLeg.moveLegType === 'VISIT_CONTAINER') {
      return moveLeg.arrivalDate;
    }
    return moveLeg.scheduledDate;
  }

  const getSecondDateValue = () => {
    if (moveLeg.moveLegType === 'VISIT_CONTAINER') {
      return moveLeg.moveDate;
    }
    if (moveLeg.scheduledDate) {
      return addDays(moveLeg.scheduledDate, moveLeg.transitDays);
    }
    return undefined;
  };

  if (moveLeg.isTransitLeg) return <InTransitMoveLeg />;

  // -- save order modification --
  const onLoadingComplete = () => {
    setSaveButtonIsLoading(false);
    setIsSaving(false);
  };

  const createUpdateMoveLegRequest = (
    completedContainerPlacement?: ContainerPlacement
  ): UpdateMoveLegRequest => {
    const request: UpdateMoveLegRequest = {
      orderId: order.orderId,
      containerOrderId: container.containerOrderId,
      moveLegId: moveLeg.moveLegId,
      moveLegType: moveLeg.moveLegType,
      requestedDate: formatDate(dateState.selectedDate!, 'yyyy-MM-dd'),
      transitDays: moveLeg.transitDays,
      // Likely used to cancel a visit to a container warehouse
      isCancelLeg: false,
      locationFields: {
        zip: moveLeg.displayAddress.postalCode,
        moveLegType: moveLeg.moveLegType,
        orderType: order.orderType,
        siteIdentity: moveLeg.siteIdentity,
        isIfOpenCalendar: false
      },
      containerPlacement: completedContainerPlacement ?? undefined
    };
    if (isMoveOrRedelivery()) {
      request.serviceAddress = addressState.value;
      request.containerPlacement = completedContainerPlacement!!;
    }
    if (priceDifferenceResponse) request.quoteId = priceDifferenceResponse.quoteId;
    return request;
  };

  const hasAddressChanged = (request: UpdateMoveLegRequest) => {
    if (request.serviceAddress) {
      if (moveLeg.displayAddress.address1 == null) return true;
      return !(
        moveLeg.displayAddress.address1.toLowerCase() ===
        request.serviceAddress?.address1.toLowerCase()
      );
    }
    return false;
  };
  const createGtmScheduleType = (request: UpdateMoveLegRequest): GtmScheduleType => {
    const gtmRequest: GtmScheduleType = {
      transactionId: order.orderId,
      containerId: container.containerId,
      moveLegId: moveLeg.moveLegId,
      moveLegType: moveLeg.moveLegType,
      addressChanged: hasAddressChanged(request),
      deliverySiteType: request.containerPlacement?.siteType.toString(),
      containerPlacement: request.containerPlacement?.placement.toString(),
      pavedSurface: request.containerPlacement?.isPavedSurface,
      gated: false, // TODO: This will need to be updated once we introduce feature
      optionalNotes: !!(
        request.containerPlacement && request.containerPlacement.driverNotes.length > 0
      )
    };
    return gtmRequest;
  };

  function triggerGtmForSuccess(gtmRequest: GtmScheduleType, isSuccess: boolean) {
    if (isSuccess) {
      const eventType = isEditingGtmEvent ? 'success_edit_schedule' : 'success_schedule';
      gtmEvents.pushMoveLegScheduleEvent(eventType, gtmRequest);
    } else {
      const eventType = isEditingGtmEvent ? 'submit_edit_schedule' : 'submit_schedule';
      gtmEvents.pushMoveLegScheduleEvent(eventType, gtmRequest);
    }
  }
  const onScheduleSave = async (
    containerPlacement: ContainerPlacement | undefined,
    onSuccess: () => void,
    onError: () => void
  ) => {
    setIsSaving(true);
    addressState.displayErrorForAllFields();
    // don't save is selected date is not changed, unless its move or redelivery
    if (isMoveOrRedelivery()) {
      if (!addressState.isValid() || !dateState.selectedDateIsValid() || containerPlacement == null)
        return;
    } else if (!dateState.selectedDateIsValid() || !dateState.selectedDateIsDifferent()) return;

    // setSaveButtonIsLoading(true);

    const request = createUpdateMoveLegRequest(containerPlacement);
    const gtmSchedule = createGtmScheduleType(request);

    triggerGtmForSuccess(gtmSchedule, false);
    updateMoveLeg.mutate(request, {
      onSuccess: (response) => {
        triggerGtmForSuccess(gtmSchedule, true);
        onLoadingComplete();
        if (response.quoteId !== '0') {
          setPriceDifferenceResponse(response);
          return;
        }
        setPriceDifferenceResponse(null);

        // the other takes a success block, but this one doesn't
        // confirm behavior
        refetch();
        onSuccess();
      },
      onError: (error: unknown) => {
        const eventType = isEditingGtmEvent ? 'submit_edit_schedule' : 'submit_schedule';
        gtmEvents.errorEvent(
          createGtmErrorRequest(GA_GENERIC_BACKEND_MESSAGE, eventType, 'backend')
        );
        onLoadingComplete();
        if (isStaleDataError(error)) {
          refetchOnFailure();
        } else {
          onError();
        }
      }
    });
  };

  return (
    <Grid container {...styles.moveLegSection} data-testid={`move-leg-${moveLeg.moveLegId}`}>
      <Grid item>
        <ContainerProgressLine
          variant={scheduledStatusToProgressLineVariant(moveLeg.scheduledStatus)}
          isUpNext={moveLeg.isUpNext}
          isFinal={isLastMoveLeg}
          dataTestId={`progress-line-${moveLeg.moveLegId}`}
        />
      </Grid>
      <Grid item xs {...styles.mainBody}>
        <Grid container {...styles.titleContainer} data-testid="move-leg-title-container">
          <Grid container item xs {...styles.titleWithEdit}>
            <Typography
              color="inherit"
              variant="h4"
              {...styles.titleText}
              data-testid="move-leg-title">
              {title}
            </Typography>
          </Grid>
          {shouldRenderEditButton() && (
            <EditButton
              dataTestId="move-leg"
              onClick={() => {
                setIsEditingGtmEvent(true);
                startEditSchedule(gtmSchedulePayLoad);
                toggleEdit();
              }}
              disabled={isSaving}
            />
          )}
        </Grid>
        {shouldCallForAssistance() && <CallForSpecialAssistance />}
        <ServiceCountdownAlert moveLeg={moveLeg} />
        {showInitialDeliveryAlert && (
          <LegacyReviewInitialDeliveryAlert hideAlertCallback={hideInitialDeliveryAlert} />
        )}
        <Grid container {...styles.detailsContainer}>
          <Grid container {...styles.detailsContainer}>
            <DateComponent
              firstDateLabel={dateLabels.firstDateLabel}
              firstDateValue={getFirstDateValue()}
              secondDateLabel={dateLabels.secondDateLabel}
              secondDateValue={getSecondDateValue()}
            />
            <AddressComponent moveLeg={moveLeg} address={formatAddress(moveLeg.displayAddress)} />
          </Grid>
          {moveLeg.moveLegType === 'SELF_FINAL_PICKUP' && moveLeg.scheduledDate && (
            <PickupWindow etaWindow={moveLeg.eta} />
          )}
          {isSchedulingMoveLeg ? (
            <LegacyScheduleMoveLeg
              onStopScheduling={stopMoveLegScheduling}
              isEditingGtmEvent={isEditingGtmEvent}
            />
          ) : (
            shouldRenderScheduleButton() && (
              <ScheduleMoveLegButton
                moveLeg={moveLeg}
                onClick={() => {
                  startSchedule(gtmSchedulePayLoad);
                  toggleEdit();
                }}
                disabled={isSaving}
              />
            )
          )}
          {isCallToScheduleLeg() && (
            <CallToScheduleComponent
              isScheduled={moveLeg.scheduledStatus === 'FUTURE'}
              onlineScheduleEnabled
            />
          )}
        </Grid>
        {/* // TODO Test that it should be schedulable online */}
        {isScheduledContainerVisit && moveLeg.isSchedulableOnline && !isSchedulingMoveLeg && (
          <LegacyVisitContainerSection
            moveLeg={moveLeg}
            onEdit={() => {
              startEditSchedule(gtmSchedulePayLoad);
              toggleEdit();
            }}
          />
        )}

        {isLastRenderedMoveLeg ? (
          <Grid container {...styles.dividerContainer}>
            <Divider />
          </Grid>
        ) : (
          <Grid container style={{ height: '24px' }} />
        )}
        <ManagePickupPanel
          isOpen={isPickupPanelOpen}
          onClose={onPickupPanelClose}
          onSave={onScheduleSave}
        />
        <ManageVisitContainer
          isOpen={isVisitDrawerOpen}
          onClose={() => setIsVisitDrawerOpen(false)}
          moveLeg={moveLeg}
        />
        <ManageDropOffContainer
          isOpen={isDropOffDrawerOpen}
          onClose={() => setIsDropOffDrawerOpen(false)}
          moveLeg={moveLeg}
        />
      </Grid>
    </Grid>
  );
};

const moveLegStyles = (isMobile: boolean) => ({
  ...sharedMoveLegStyles(isMobile),
  titleWithEdit: {
    sx: {
      flexDirection: 'row',
      alignItems: 'center'
    }
  }
});
