import { Grid, Typography, useMediaQuery } from '@mui/material';
import React, { useState } from 'react';
import Divider from '@mui/material/Divider';
import { addDays } from 'date-fns';
import useContainerContext from '../../../../../../context/ContainerContext';
import useSingleOrderContext from '../../../../../../context/SingleOrderContext';
import { theme } from '../../../../../../PodsTheme';
import { useGtmEvents } from '../../../../../../config/google/useGtmEvents';
import {
  ScheduledStatus,
  serviceCountdownTypeForMoveLegType,
  UpdateMoveLegRequest
} from '../../../../../../domain/OrderEntities';
import {
  ContainerProgressLine,
  ProgressLineVariant
} from '../../../../../HomePage/container/ContainerProgressLine';
import { isWithin24Hours } from '../../../../../../helpers/dateHelpers';
import { getDateLabels } from '../../../../../../locales/TranslationConstants';
import { InTransitMoveLeg } from '../../../../../HomePage/container/moveleg/current/InTransitMoveLeg';
import { CallForSpecialAssistance } from '../../../../../HomePage/container/moveleg/CallForSpecialAssistance';
import { EditButton } from '../../../../../../components/buttons/EditButton';
import { ServiceCountdownAlert } from '../../../../../HomePage/container/serviceCountdown/ServiceCountdownAlert';
import { DateComponent } from '../../../../../HomePage/container/moveleg/DateComponent';
import { AddressComponent } from '../../../../../HomePage/container/moveleg/AddressComponent';
import { formatAddress } from '../../../../../../networkRequests/responseEntities/CustomerEntities';
import { PickupWindow } from '../../../../../HomePage/container/moveleg/PickupWindow';
import { ScheduleMoveLegButton } from '../../../../../HomePage/container/moveleg/current/ScheduleMoveLegButton';
import { CallToScheduleComponent } from '../../../../../HomePage/container/moveleg/CallToScheduleComponent';
import { sharedMoveLegStyles } from '../../../../../HomePage/container/moveleg/sharedMoveLegStyles';
import { LegacyVisitContainerSection } from '../LegacyVisitContainerSection';
import { LegacyReviewInitialDeliveryAlert } from '../../../../../../components/legacyComponents/alert/LegacyReviewInitialDeliveryAlert';
import { LegacyScheduleMoveLeg } from '../../scheduling/LegacyScheduleMoveLeg';
import {
  REDESIGNED_SCHEDULING,
  REDESIGNED_SCHEDULING_PICKUP,
  useFeatureFlags
} from '../../../../../../helpers/useFeatureFlags';
import { ManagePickupPanel } from '../../../../../HomePage/Scheduling/ManagePickupPanel';
import {
  ManageVisitPanel,
  OnMoveLegUpdateSaveProps
} from '../../../../../HomePage/Scheduling/ManageVisitPanel';
import { ManageDropOffContainer } from '../../../../../HomePage/Scheduling/ManageDropOffContainer';
import useMoveLegContext from '../../../../../HomePage/container/moveleg/MoveLegContext';
import { GtmScheduleType } from '../../../../../../config/google/GoogleEntities';
import {
  createGtmErrorRequest,
  GA_GENERIC_BACKEND_MESSAGE
} from '../../../../../../config/google/googleAnalyticsUtils';
import { isStaleDataError } from '../../../../../HomePage/utils';
import { UpdateMoveLegResponse } from '../../../../../../networkRequests/responseEntities/OrderAPIEntities';
import useLegacyOrdersContext from '../../../../../../context/legacy/LegacyOrdersContext';
import { useLegacyUpdateMoveLeg } from '../../../../../../networkRequests/legacy/mutations/useLegacyUpdateMoveLeg';
import {
  createLegacyUpdateMoveLegRequest,
  hasAddressChanged,
  isMoveOrRedelivery
} from '../../../../../HomePage/container/scheduling/moveLegHelpers';

export const LegacyMoveLegSection = () => {
  const {
    moveLegScheduling: {
      currentlySelectedMoveLeg,
      editMoveLegScheduling,
      stopMoveLegScheduling,
      isSaving,
      setIsSaving,
      isCancelling
    }
  } = useSingleOrderContext();
  const {
    scheduling: { addressState, dateState }
  } = useMoveLegContext();
  const { refetch, refetchOnFailure } = useLegacyOrdersContext();
  const updateMoveLeg = useLegacyUpdateMoveLeg();
  const gtmEvents = useGtmEvents();
  const { isRedesignedSchedulingEnabled, isRedesignedSchedulingPickupEnabled } = useFeatureFlags([
    REDESIGNED_SCHEDULING,
    REDESIGNED_SCHEDULING_PICKUP
  ]);
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = moveLegStyles(isMobile);
  const { moveLeg, isLastMoveLeg, isLastRenderedMoveLeg, title } = useMoveLegContext();
  const { order, container } = useContainerContext();
  const [priceDifferenceResponse, setPriceDifferenceResponse] =
    useState<UpdateMoveLegResponse | null>();
  const [isEditingGtmEvent, setIsEditingGtmEvent] = useState<boolean>(false);
  const [isPickupPanelOpen, setIsPickupPanelOpen] = useState(false);
  const [isVisitPanelOpen, setIsVisitPanelOpen] = useState(false);
  const [isDropOffDrawerOpen, setIsDropOffDrawerOpen] = useState(false);
  const [showInitialDeliveryAlert, setShowInitialDeliveryAlert] = useState<boolean>(
    moveLeg.moveLegType === 'INITIAL_DELIVERY' && !order.initialDeliveryPlacementIsReviewed
  );

  const gtmSchedulePayLoad = {
    transactionId: order.orderId,
    moveLegId: moveLeg.moveLegId,
    moveLegType: moveLeg.moveLegType.toString(),
    containerId: container.containerId
  };

  function isCallToScheduleLeg(): boolean {
    return (
      moveLeg.moveLegType === 'WAREHOUSE_TO_WAREHOUSE' &&
      moveLeg.scheduledStatus === 'UNSCHEDULED' &&
      !shouldCallForAssistance()
    );
  }

  const isUnscheduledContainerVisit =
    moveLeg.moveLegType === 'VISIT_CONTAINER' && moveLeg.containerVisitDate == null;
  const isScheduledContainerVisit =
    moveLeg.moveLegType === 'VISIT_CONTAINER' && moveLeg.containerVisitDate != null;
  const isSchedulingMoveLeg = currentlySelectedMoveLeg === moveLeg && !isCancelling;
  const { startSchedule, startEditSchedule } = useGtmEvents();

  const hideInitialDeliveryAlert = () => {
    setShowInitialDeliveryAlert(false);
  };

  const scheduledStatusToProgressLineVariant = (
    scheduledStatus: ScheduledStatus
  ): ProgressLineVariant => {
    if (scheduledStatus === 'UNSCHEDULED') {
      return 'FADED_DASHED';
    }
    return 'DASHED';
  };

  const shouldRenderScheduleButton = () => {
    if (!moveLeg.isSchedulableOnline) return false;
    if (moveLeg.isCityService) return false;
    if (moveLeg.isCrossBorder) return false;
    if (moveLeg.isHawaii) return false;
    if (isUnscheduledContainerVisit) return true;
    return moveLeg.scheduledStatus === 'UNSCHEDULED';
  };

  const shouldRenderEditButton = () => {
    if (isSchedulingMoveLeg) return false;
    if (!moveLeg.isSchedulableOnline) return false;
    if (moveLeg.moveLegType === 'VISIT_CONTAINER') return false;
    if (moveLeg.scheduledStatus !== 'FUTURE') return false;
    if (isWithin24Hours(moveLeg.scheduledDate)) return false;
    if (moveLeg.isCrossBorder) return false;
    if (moveLeg.isHawaii) return false;
    return !moveLeg.isCityService;
  };

  const shouldCallForAssistance = () =>
    !moveLeg.isTransitLeg && (moveLeg.isCrossBorder || moveLeg.isHawaii || moveLeg.isCityService);

  const toggleEditMoveLegOn = () => {
    editMoveLegScheduling(moveLeg);
  };

  const toggleEdit = () => {
    setIsEditingGtmEvent(true);
    if (
      serviceCountdownTypeForMoveLegType(moveLeg.moveLegType) === 'PICKUP' &&
      isRedesignedSchedulingPickupEnabled()
    ) {
      setIsPickupPanelOpen(true);
    } else if (
      isRedesignedSchedulingEnabled() &&
      serviceCountdownTypeForMoveLegType(moveLeg.moveLegType) === 'VISIT'
    ) {
      setIsVisitPanelOpen(true);
    } else if (
      isRedesignedSchedulingEnabled() &&
      serviceCountdownTypeForMoveLegType(moveLeg.moveLegType) === 'DROPOFF'
    ) {
      setIsDropOffDrawerOpen(true);
    } else {
      toggleEditMoveLegOn();
    }
  };

  const dateLabels = getDateLabels(moveLeg.moveLegType);

  function getFirstDateValue() {
    if (moveLeg.moveLegType === 'VISIT_CONTAINER') {
      return moveLeg.arrivalDate;
    }
    return moveLeg.scheduledDate;
  }

  const getSecondDateValue = () => {
    if (moveLeg.moveLegType === 'VISIT_CONTAINER') {
      return moveLeg.moveDate;
    }
    if (moveLeg.scheduledDate) {
      return addDays(moveLeg.scheduledDate, moveLeg.transitDays);
    }
    return undefined;
  };

  if (moveLeg.isTransitLeg) return <InTransitMoveLeg />;

  const onLoadingComplete = () => {
    setIsSaving(false);
  };

  const createGtmScheduleType = (request: UpdateMoveLegRequest): GtmScheduleType => ({
    transactionId: order.orderId,
    containerId: container.containerId,
    moveLegId: moveLeg.moveLegId,
    moveLegType: moveLeg.moveLegType,
    addressChanged: hasAddressChanged(moveLeg, request),
    deliverySiteType: request.containerPlacement?.siteType.toString(),
    containerPlacement: request.containerPlacement?.placement.toString(),
    pavedSurface: request.containerPlacement?.isPavedSurface,
    gated: false, // TODO: This will need to be updated once we introduce feature
    optionalNotes: !!(
      request.containerPlacement && request.containerPlacement.driverNotes.length > 0
    )
  });

  function triggerGtmForSuccess(gtmRequest: GtmScheduleType, isSuccess: boolean) {
    if (isSuccess) {
      const eventType = isEditingGtmEvent ? 'success_edit_schedule' : 'success_schedule';
      gtmEvents.pushMoveLegScheduleEvent(eventType, gtmRequest);
    } else {
      const eventType = isEditingGtmEvent ? 'submit_edit_schedule' : 'submit_schedule';
      gtmEvents.pushMoveLegScheduleEvent(eventType, gtmRequest);
    }
  }

  const onScheduleSave = async ({
    containerPlacement,
    onSuccess,
    onError
  }: OnMoveLegUpdateSaveProps) => {
    setIsSaving(true);
    addressState.displayErrorForAllFields();
    // don't save if selected date is not changed, unless its move or redelivery
    if (isMoveOrRedelivery(moveLeg)) {
      if (!addressState.isValid() || !dateState.selectedDateIsValid() || containerPlacement == null)
        return;
    } else if (!dateState.selectedDateIsValid() || !dateState.selectedDateIsDifferent()) return;

    const request = createLegacyUpdateMoveLegRequest({
      order,
      container,
      moveLeg,
      selectedDate: dateState.selectedDate!,
      serviceAddress: addressState.value,
      completedContainerPlacement: containerPlacement,
      priceDifferenceResponse: priceDifferenceResponse,
      isCancelLeg: false,
      isUpdating: true,
      isIfOpenCalendar: false
    });
    const gtmSchedule = createGtmScheduleType(request);

    triggerGtmForSuccess(gtmSchedule, false);
    updateMoveLeg.mutate(request, {
      onSuccess: (response) => {
        triggerGtmForSuccess(gtmSchedule, true);
        onLoadingComplete();
        if (response.quoteId !== '0') {
          setPriceDifferenceResponse(response);
          return;
        }
        setPriceDifferenceResponse(null);

        // the other takes a success block, but this one doesn't
        // confirm behavior
        refetch();
        onSuccess();
      },
      onError: (error: unknown) => {
        const eventType = isEditingGtmEvent ? 'submit_edit_schedule' : 'submit_schedule';
        gtmEvents.errorEvent(
          createGtmErrorRequest(GA_GENERIC_BACKEND_MESSAGE, eventType, 'backend')
        );
        onLoadingComplete();
        if (isStaleDataError(error)) {
          refetchOnFailure();
        } else {
          onError(error);
        }
      }
    });
  };

  return (
    <Grid container {...styles.moveLegSection} data-testid={`move-leg-${moveLeg.moveLegId}`}>
      <Grid item>
        <ContainerProgressLine
          variant={scheduledStatusToProgressLineVariant(moveLeg.scheduledStatus)}
          isUpNext={moveLeg.isUpNext}
          isFinal={isLastMoveLeg}
          dataTestId={`progress-line-${moveLeg.moveLegId}`}
        />
      </Grid>
      <Grid item xs {...styles.mainBody}>
        <Grid container {...styles.titleContainer} data-testid="move-leg-title-container">
          <Grid container item xs {...styles.titleWithEdit}>
            <Typography
              color="inherit"
              variant="h4"
              {...styles.titleText}
              data-testid="move-leg-title">
              {title}
            </Typography>
          </Grid>
          {shouldRenderEditButton() && (
            <EditButton
              dataTestId="move-leg"
              onClick={() => {
                setIsEditingGtmEvent(true);
                startEditSchedule(gtmSchedulePayLoad);
                toggleEdit();
              }}
              disabled={isSaving}
            />
          )}
        </Grid>
        {shouldCallForAssistance() && <CallForSpecialAssistance />}
        <ServiceCountdownAlert moveLeg={moveLeg} />
        {showInitialDeliveryAlert && (
          <LegacyReviewInitialDeliveryAlert hideAlertCallback={hideInitialDeliveryAlert} />
        )}
        <Grid container {...styles.detailsContainer}>
          <Grid container {...styles.detailsContainer}>
            <DateComponent
              firstDateLabel={dateLabels.firstDateLabel}
              firstDateValue={getFirstDateValue()}
              secondDateLabel={dateLabels.secondDateLabel}
              secondDateValue={getSecondDateValue()}
            />
            <AddressComponent moveLeg={moveLeg} address={formatAddress(moveLeg.displayAddress)} />
          </Grid>
          {moveLeg.moveLegType === 'SELF_FINAL_PICKUP' && moveLeg.scheduledDate && (
            <PickupWindow etaWindow={moveLeg.eta} />
          )}
          {isSchedulingMoveLeg ? (
            <LegacyScheduleMoveLeg
              onStopScheduling={stopMoveLegScheduling}
              isEditingGtmEvent={isEditingGtmEvent}
            />
          ) : (
            shouldRenderScheduleButton() && (
              <ScheduleMoveLegButton
                moveLeg={moveLeg}
                onClick={() => {
                  startSchedule(gtmSchedulePayLoad);
                  toggleEdit();
                }}
                disabled={isSaving}
              />
            )
          )}
          {isCallToScheduleLeg() && (
            <CallToScheduleComponent
              isScheduled={moveLeg.scheduledStatus === 'FUTURE'}
              onlineScheduleEnabled
            />
          )}
        </Grid>
        {/* // TODO Test that it should be schedulable online */}
        {isScheduledContainerVisit && moveLeg.isSchedulableOnline && !isSchedulingMoveLeg && (
          <LegacyVisitContainerSection
            moveLeg={moveLeg}
            onEdit={() => {
              startEditSchedule(gtmSchedulePayLoad);
              toggleEdit();
            }}
          />
        )}

        {isLastRenderedMoveLeg ? (
          <Grid container {...styles.dividerContainer}>
            <Divider />
          </Grid>
        ) : (
          <Grid container style={{ height: '24px' }} />
        )}
        <ManagePickupPanel
          isOpen={isPickupPanelOpen}
          onClose={() => {
            setIsPickupPanelOpen(false);
          }}
          onSave={onScheduleSave}
        />
        <ManageVisitPanel
          isOpen={isVisitPanelOpen}
          onClose={() => {
            setIsVisitPanelOpen(false);
          }}
          onSave={onScheduleSave}
        />
        <ManageDropOffContainer
          isOpen={isDropOffDrawerOpen}
          onClose={() => {
            setIsDropOffDrawerOpen(false);
          }}
        />
      </Grid>
    </Grid>
  );
};

const moveLegStyles = (isMobile: boolean) => ({
  ...sharedMoveLegStyles(isMobile),
  titleWithEdit: {
    sx: {
      flexDirection: 'row',
      alignItems: 'center'
    }
  }
});
