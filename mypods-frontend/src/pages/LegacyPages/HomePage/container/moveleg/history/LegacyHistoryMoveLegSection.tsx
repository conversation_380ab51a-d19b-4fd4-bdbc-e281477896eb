import React, { Grid, Typography, useMediaQuery } from '@mui/material';
import Divider from '@mui/material/Divider';
import { addDays } from 'date-fns';
import { useTranslation } from 'react-i18next';
import useMoveLegContext from '../../../../../HomePage/container/moveleg/MoveLegContext';
import { ContainerProgressLine } from '../../../../../HomePage/container/ContainerProgressLine';
import { getDateLabels } from '../../../../../../locales/TranslationConstants';
import { theme } from '../../../../../../PodsTheme';
import { TranslationKeys } from '../../../../../../locales/TranslationKeys';
import { TransitLegDescription } from '../../../../../HomePage/container/moveleg/TransitLegDescription';
import { DateComponent } from '../../../../../HomePage/container/moveleg/DateComponent';
import { AddressComponent } from '../../../../../HomePage/container/moveleg/AddressComponent';
import { formatAddress } from '../../../../../../networkRequests/responseEntities/CustomerEntities';
import { Design } from '../../../../../../helpers/Design';

export const LegacyHistoryMoveLegSection = () => {
  const { moveLeg, isLastMoveLeg, title } = useMoveLegContext();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { t: translate } = useTranslation();
  const styles = moveLegStyles(isMobile);

  const dateLabels = getDateLabels(moveLeg.moveLegType);
  const secondDateValue = moveLeg.scheduledDate
    ? addDays(moveLeg.scheduledDate, moveLeg.transitDays)
    : undefined;

  const getTitle = () =>
    moveLeg.moveLegType === 'VISIT_CONTAINER'
      ? translate(TranslationKeys.HomePage.MoveLegs.Title.CONTAINER_AT_WAREHOUSE_HISTORY)
      : title;

  return (
    <Grid container {...styles.moveLegSection} data-testid={`move-leg-${moveLeg.moveLegId}`}>
      <Grid item>
        <ContainerProgressLine
          variant="FADED_SOLID"
          isUpNext={moveLeg.isUpNext}
          isFinal={isLastMoveLeg}
          dataTestId={`progress-line-${moveLeg.moveLegId}`}
        />
      </Grid>
      <Grid item xs {...styles.mainBody}>
        <Grid container {...styles.titleContainer} data-testid="move-leg-title-container">
          <Typography
            color="inherit"
            variant="h4"
            {...styles.titleText}
            data-testid="move-leg-title">
            {getTitle()}
          </Typography>
        </Grid>
        {moveLeg.isTransitLeg ? (
          <TransitLegDescription />
        ) : (
          <Grid container {...styles.detailsContainer}>
            <DateComponent
              firstDateLabel={dateLabels.firstDateLabel}
              firstDateValue={moveLeg.scheduledDate}
              secondDateLabel={dateLabels.secondDateLabel}
              secondDateValue={secondDateValue}
            />
            <AddressComponent moveLeg={moveLeg} address={formatAddress(moveLeg.displayAddress)} />
          </Grid>
        )}
        <Grid container {...styles.dividerContainer}>
          <Divider />
        </Grid>
      </Grid>
    </Grid>
  );
};

const moveLegStyles = (isMobile: boolean) => ({
  moveLegSection: {
    sx: {
      flexDirection: 'row',
      columnGap: Design.Primitives.Spacing.xxs
    }
  },
  titleContainer: {
    sx: {
      flexDirection: 'row',
      alignItems: 'center',
      columnGap: Design.Primitives.Spacing.xxs
    }
  },
  titleText: {
    sx: {
      paddingBottom: 0,
      color: Design.Alias.Color.neutral600 // TODO: change color or add to design theme
    }
  },
  mainBody: {
    sx: {
      flexGrow: 1,
      display: 'flex',
      flexDirection: 'column',
      gap: Design.Primitives.Spacing.xxs,
      marginTop: '-5px'
    }
  },
  detailsContainer: {
    sx: {
      color: Design.Alias.Color.neutral600,
      flexDirection: isMobile ? 'column' : 'row',
      columnGap: Design.Primitives.Spacing.md,
      rowGap: Design.Primitives.Spacing.xxs
    }
  },
  dividerContainer: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      paddingRight: '8px',
      height: isMobile ? '64px' : '72px'
    }
  }
});
