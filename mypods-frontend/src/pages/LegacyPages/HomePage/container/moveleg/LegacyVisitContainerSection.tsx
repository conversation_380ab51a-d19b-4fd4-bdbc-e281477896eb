import { CircularProgress, Grid, Typography, useMediaQuery } from '@mui/material';
import React, { useContext } from 'react';
import { useTranslation } from 'react-i18next';
import { LoadingButton } from '@mui/lab';
import Button from '@mui/material/Button';
import { MoveLeg, UpdateMoveLegRequest } from '../../../../../domain/OrderEntities';
import { theme } from '../../../../../PodsTheme';
import { TranslationKeys } from '../../../../../locales/TranslationKeys';
import { ContainerContext } from '../../../../../context/ContainerContext';
import useSingleOrderContext from '../../../../../context/SingleOrderContext';
import { NotificationContext } from '../../../../../components/notifications/NotificationContext';
import { CancelVisitButton } from '../../../../../components/buttons/CancelVisitButton';
import { Design } from '../../../../../helpers/Design';
import { EditButton } from '../../../../../components/buttons/EditButton';
import { DateComponent } from '../../../../HomePage/container/moveleg/DateComponent';
import { formatETA } from '../../../../../helpers/dateHelpers';
import { CallToScheduleComponent } from '../../../../HomePage/container/moveleg/CallToScheduleComponent';
import { isStaleDataError } from '../../../../HomePage/utils';
import { useLegacyUpdateMoveLeg } from '../../../../../networkRequests/legacy/mutations/useLegacyUpdateMoveLeg';
import useLegacyOrdersContext from '../../../../../context/legacy/LegacyOrdersContext';

export const LegacyVisitContainerSection = ({
  moveLeg,
  onEdit
}: {
  moveLeg: MoveLeg;
  onEdit: () => void;
}) => {
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = visitContainerStyles(isMobile);
  const { t: translate } = useTranslation();
  const Tx = TranslationKeys.HomePage.MoveLegs;
  const { mutate: updateMoveLeg, isPending } = useLegacyUpdateMoveLeg();
  const { order, container } = useContext(ContainerContext);
  const { refetch, refetchOnFailure } = useLegacyOrdersContext();
  const { setNotification } = useContext(NotificationContext);
  const { moveLegScheduling } = useSingleOrderContext();
  const {
    editMoveLegScheduling,
    stopMoveLegScheduling,
    isSaving,
    setIsSaving,
    isCancelling,
    setIsCancelling
  } = moveLegScheduling;

  const handleCancel = () => {
    setIsSaving(true);

    const request: UpdateMoveLegRequest = {
      orderId: order.orderId,
      containerOrderId: container.containerOrderId,
      moveLegId: moveLeg.moveLegId,
      moveLegType: moveLeg.moveLegType,
      requestedDate: null,
      transitDays: moveLeg.transitDays,
      isCancelLeg: true,
      isUpdating: false,
      locationFields: {
        zip: moveLeg.displayAddress.postalCode,
        moveLegType: moveLeg.moveLegType,
        orderType: order.orderType,
        siteIdentity: moveLeg.siteIdentity,
        isIfOpenCalendar: false
      }
    };
    updateMoveLeg(request, {
      onSuccess: (_) => {
        stopCancellingMoveLeg();
        refetch();
        setNotification({
          isError: false,
          message: translate(TranslationKeys.CommonComponents.Notification.CONTAINER_VISIT_CANCELED)
        });
      },
      onError: (error: unknown) => {
        setIsSaving(false);
        if (isStaleDataError(error)) {
          refetchOnFailure();
        } else {
          setNotification({
            message: translate(TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE),
            isError: true
          });
        }
      }
    });
  };

  const startCancellingMoveLeg = () => {
    editMoveLegScheduling(moveLeg);
    setIsCancelling(true);
  };

  const stopCancellingMoveLeg = () => {
    stopMoveLegScheduling();
    setIsCancelling(false);
    setIsSaving(false);
  };

  const isItRescheduleLeg = () => moveLeg.scheduledStatus === 'UNSCHEDULED';

  return (
    <Grid container {...styles.visitContainer}>
      <Grid container item xs {...styles.titleWithEdit}>
        <Typography color="inherit" style={{ ...Design.Alias.Text.BodyUniversal.MdBold }}>
          {translate(Tx.Title.VISIT_CONTAINER)}
        </Typography>
        {!isCancelling && (
          <Grid item {...styles.buttons}>
            <CancelVisitButton onClick={startCancellingMoveLeg} disabled={isSaving} />
            <EditButton onClick={onEdit} dataTestId="container-visit" disabled={isSaving} />
          </Grid>
        )}
      </Grid>
      <Grid container item {...styles.detailsContainer}>
        <DateComponent
          firstDateLabel={translate(Tx.Scheduling.DateLabels.LABEL_VISIT)}
          firstDateValue={moveLeg.scheduledDate}
        />
        <Grid item>
          <Typography
            color={moveLeg.eta == null ? Design.Alias.Color.neutral600 : 'inherit'}
            variant="subtitle2"
            sx={{ fontWeight: Design.Alias.Text.BodyUniversal.XsBold }}>
            {translate(Tx.Scheduling.DateLabels.LABEL_VISIT_WINDOW)}
          </Typography>
          <Typography
            color={moveLeg.eta == null ? Design.Alias.Color.neutral600 : 'inherit'}
            variant="subtitle1">
            {formatETA(moveLeg.eta) || translate(Tx.Scheduling.NO_WAREHOUSE_HOURS_FOUND)}
          </Typography>
        </Grid>
      </Grid>
      {isCancelling && (
        <Grid container item {...styles.actionButtonContainer}>
          <Button
            {...styles.nevermindButton}
            disabled={isSaving}
            color="secondary"
            variant="outlined"
            aria-label="Cancel"
            onClick={stopCancellingMoveLeg}>
            {translate(TranslationKeys.CommonComponents.NEVERMIND_BUTTON)}
          </Button>
          <LoadingButton
            {...styles.confirmCancelButton}
            loadingPosition={isPending ? 'start' : undefined}
            startIcon={isPending && <CircularProgress size={20} color="inherit" />}
            centerRipple
            disableElevation
            color="secondary"
            variant="contained"
            disabled={isSaving}
            onClick={handleCancel}>
            {translate(TranslationKeys.CommonComponents.CONFIRM_CANCEL_BUTTON)}
          </LoadingButton>
        </Grid>
      )}
      {!isCancelling && (
        <CallToScheduleComponent isScheduled={isItRescheduleLeg()} onlineScheduleEnabled />
      )}
    </Grid>
  );
};

const visitContainerStyles = (isMobile: boolean) => ({
  visitContainer: {
    marginTop: Design.Primitives.Spacing.md,
    gap: Design.Primitives.Spacing.xxs,
    color: Design.Alias.Color.accent900
  },
  titleWithEdit: {
    sx: {
      display: 'flex',
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between'
    }
  },
  detailsContainer: {
    sx: {
      flexDirection: isMobile ? 'column' : 'row',
      columnGap: Design.Primitives.Spacing.md,
      rowGap: Design.Primitives.Spacing.xxs
    }
  },
  buttons: {
    display: 'flex',
    gap: Design.Primitives.Spacing.md
  },
  actionButtonContainer: {
    sx: {
      gap: Design.Primitives.Spacing.xxs,
      justifyContent: 'flex-end',
      marginTop: Design.Primitives.Spacing.sm
    }
  },
  confirmCancelButton: {
    sx: {
      minWidth: 'fit-content',
      height: '36px'
    }
  },
  nevermindButton: {
    sx: {
      textTransform: 'none',
      height: '36px',
      width: '112px'
    }
  }
});
