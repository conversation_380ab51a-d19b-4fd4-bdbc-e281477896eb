import React from 'react';
import { Grid, useMediaQuery } from '@mui/material';
import { PageLayout } from '../../../components/PageLayout';
import { theme } from '../../../PodsTheme';
import { Design } from '../../../helpers/Design';
import { LegacyContainerTileWrapper } from './container/LegacyContainerTileWrapper';
import { LegacyHomePageSidebar } from './LegacyHomePageSidebar';
import {
  HOME_PAGE_ALERT_ENABLED,
  QUICK_LINKS,
  useFeatureFlags
} from '../../../helpers/useFeatureFlags';
import { DynamicHomeAlert } from '../../HomePage/DynamicHomeAlert';
import { MyPodsSplashScreenModal } from '../../HomePage/container/MyPodsSplashScreenModal';
import { QuickLinks } from '../../HomePage/components/QuickLinks';

export const LegacyHomePage = () => {
  const { isHomePageAlertEnabled, isQuickLinksEnabled } = useFeatureFlags([
    HOME_PAGE_ALERT_ENABLED,
    QUICK_LINKS
  ]);
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = homePageStyles(isMobile);

  return (
    <PageLayout columnsLg={8}>
      {isHomePageAlertEnabled() && <DynamicHomeAlert />}
      <Grid {...styles.homePage}>
        <Grid {...styles.mainBody}>
          <Grid container item xs={12} sm={8} {...styles.leftBody}>
            <LegacyContainerTileWrapper />
          </Grid>
          {isQuickLinksEnabled() ? (
            <Grid container item xs={12} sm={4}>
              <QuickLinks />
            </Grid>
          ) : (
            <LegacyHomePageSidebar />
          )}
        </Grid>
      </Grid>
      <MyPodsSplashScreenModal />
    </PageLayout>
  );
};

const homePageStyles = (isMobile: boolean) => ({
  homePage: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      gap: Design.Primitives.Spacing.md
    }
  },
  header: {
    sx: {
      display: 'flex',
      paddingBottom: Design.Primitives.Spacing.lgPlus,
      columnGap: Design.Primitives.Spacing.sm,
      rowGap: 0,
      flexDirection: isMobile ? 'column' : 'row'
    }
  },
  mainBody: {
    sx: {
      display: 'flex',
      flexDirection: isMobile ? 'column' : 'row',
      columnGap: Design.Primitives.Spacing.sm,
      rowGap: Design.Primitives.Spacing.lg
    }
  },
  leftBody: {
    sx: {
      flexDirection: 'column',
      gap: '8px'
    }
  }
});
