import React from 'react';
import { Grid, Typography, useMediaQuery } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { PageLayout } from '../../../components/PageLayout';
import { theme } from '../../../PodsTheme';
import { Design } from '../../../helpers/Design';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { formatFullName } from '../../../networkRequests/responseEntities/CustomerEntities';
import { LegacyContainerTileWrapper } from './container/LegacyContainerTileWrapper';
import { LegacyHomePageSidebar } from './LegacyHomePageSidebar';
import {
  HOME_PAGE_ALERT_ENABLED,
  QUICK_LINKS,
  useFeatureFlags
} from '../../../helpers/useFeatureFlags';
import { DynamicHomeAlert } from '../../HomePage/DynamicHomeAlert';
import { useLegacyGetCustomer } from '../../../networkRequests/legacy/queries/useLegacyGetCustomer';
import { MyPodsSplashScreenModal } from '../../HomePage/container/MyPodsSplashScreenModal';
import { QuickLinks } from '../../HomePage/components/QuickLinks';

export const LegacyHomePage = () => {
  const { t: translate } = useTranslation();
  const { customer } = useLegacyGetCustomer();
  const { isHomePageAlertEnabled, isQuickLinksEnabled } = useFeatureFlags([
    HOME_PAGE_ALERT_ENABLED,
    QUICK_LINKS
  ]);
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = homePageStyles(isMobile);

  return (
    <PageLayout columnsLg={8}>
      {isHomePageAlertEnabled() && <DynamicHomeAlert />}
      <Grid {...styles.homePage}>
        <Grid {...styles.header}>
          <Grid container item xs={12} sm={8} {...styles.leftBody}>
            <Typography variant="h1">
              {translate(TranslationKeys.HomePage.HEADER, { fullName: formatFullName(customer) })}
            </Typography>
          </Grid>
          <Grid container item xs={12} sm={4} />
        </Grid>
        <Grid {...styles.mainBody}>
          <Grid container item xs={12} sm={8} {...styles.leftBody}>
            <LegacyContainerTileWrapper />
          </Grid>
          {isQuickLinksEnabled() ? <QuickLinks /> : <LegacyHomePageSidebar />}
        </Grid>
      </Grid>
      <MyPodsSplashScreenModal />
    </PageLayout>
  );
};

const homePageStyles = (isMobile: boolean) => ({
  homePage: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      gap: Design.Primitives.Spacing.md
    }
  },
  header: {
    sx: {
      display: 'flex',
      paddingBottom: Design.Primitives.Spacing.lgPlus,
      columnGap: Design.Primitives.Spacing.sm,
      rowGap: 0,
      flexDirection: isMobile ? 'column' : 'row'
    }
  },
  mainBody: {
    sx: {
      display: 'flex',
      flexDirection: isMobile ? 'column' : 'row',
      columnGap: Design.Primitives.Spacing.sm,
      rowGap: Design.Primitives.Spacing.lg
    }
  },
  leftBody: {
    sx: {
      flexDirection: 'column',
      gap: '8px'
    }
  }
});
