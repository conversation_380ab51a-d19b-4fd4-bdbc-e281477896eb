import { vi } from 'vitest';
import { HostedFieldsEvent, HostedFieldsTokenizePayload } from 'braintree-web';
import React, { act } from 'react';
import {
  createCustomer,
  createCustomerAddress,
  createHostedFieldsTokenizePayload,
  createRefreshSessionClaims
} from '../../../../testUtils/MyPodsFactories';
import { renderWithPoetProvidersAndState } from '../../../../testUtils/RenderHelpers';
import {
  mockGetCustomer,
  mockLegacyAddPaymentMethod,
  mockLegacyGetPaymentMethods,
  mockNavigate,
  mockRefreshSession,
  mockUpdateBillingAddress
} from '../../../../../setupTests';
import { LegacyManagePaymentMethodPage } from '../LegacyManagePaymentMethodPage';
import { screen, waitFor, waitForElementToBeRemoved } from '@testing-library/react';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import userEvent from '@testing-library/user-event';
import {
  addPaymentMethodRequest,
  AddPaymentMethodRequest
} from '../../../../networkRequests/responseEntities/PaymentEntities';
import { ROUTES } from '../../../../Routes';
import { expectNotificationAlertContainsTitle } from '../../../../testUtils/assertions';
import {
  Customer,
  UpdateBillingAddressRequest
} from '../../../../networkRequests/responseEntities/CustomerEntities';
import { paymentMethodPayload } from '../../../ManagePaymentMethodPage/Braintree/BraintreeForm';

// -- mocks --
const mockClientCreate = vi.hoisted(() => vi.fn());
const mockHostedFieldsCreate = vi.hoisted(() => vi.fn());
const mockHostedFieldsTokenize = vi.hoisted(() => vi.fn());
const mockHostedFieldsOn = vi.hoisted(() => vi.fn());
const mockPaypalCheckoutCreate = vi.hoisted(() => vi.fn());

vi.mock('braintree-web', async () => ({
  ...(await vi.importActual('braintree-web')),
  client: {
    create: mockClientCreate
  },
  hostedFields: {
    create: mockHostedFieldsCreate
  },
  paypalCheckout: {
    create: mockPaypalCheckoutCreate
  }
}));

// -- braintree event handlers --
let triggerAllCreditCardFieldsComplete = (_: HostedFieldsEvent): void => {};

// Setup braintree mocks that don't change (coupled to implementation)
beforeEach(() => {
  mockClientCreate.mockResolvedValue({});
  mockHostedFieldsCreate.mockResolvedValue({
    tokenize: mockHostedFieldsTokenize,
    on: mockHostedFieldsOn
  });
  mockPaypalCheckoutCreate.mockResolvedValue({
    loadPayPalSDK: vi.fn().mockResolvedValue({
      createPayment: vi.fn(),
      tokenizePayment: vi.fn()
    })
  });
  window.paypal = {
    FUNDING: {
      // @ts-expect-error : needed for script
      PAYPAL: 'PAYPAL'
    },
    // @ts-expect-error : needed for script
    Buttons: (_) => {
      return {
        render: vi.fn()
      };
    }
  };
});

const Tx = TranslationKeys.ManagePaymentMethodsPage;
const views = {
  saveButton: () =>
    screen.getByRole('button', {
      name: Tx.SAVE_BUTTON
    }),
  cancelButton: () =>
    screen.getByRole('button', {
      name: TranslationKeys.CommonComponents.CANCEL_BUTTON
    }),
  billingCheckbox: () => screen.getByLabelText(Tx.BILLING_ADDRESS_CHECKBOX_LABEL),
  postalCodeField: () =>
    screen.getByLabelText(TranslationKeys.AccountPage.AddressInfo.InputFields.POSTAL_CODE),
  navigateToAccountPageLink: () => screen.getByLabelText(Tx.GO_TO_ACCOUNT_PAGE_LABEL)
};

const actions = {
  clickSave: async () => act(() => userEvent.click(views.saveButton())),
  clickCancel: async () => act(() => userEvent.click(views.cancelButton())),
  completeCreditCardForm: () => {
    const allFieldsCompleteEvent: HostedFieldsEvent = {
      fields: {
        cardholderName: {
          isEmpty: false
        },
        number: {
          isEmpty: false
        },
        expirationDate: {
          isEmpty: false
        },
        cvv: {
          isEmpty: false
        }
      }
    } as HostedFieldsEvent;

    act(() => {
      triggerAllCreditCardFieldsComplete(allFieldsCompleteEvent);
    });
  },
  clickRedirectToAccountPage: async () => {
    await act(() => userEvent.click(views.navigateToAccountPageLink()));
  }
};

// Unskip while making changes to manage payment, but these tests are flaky right now and cause the pipeline to fail
describe.skip('ManagePaymentMethodPage', async () => {
  const jwtCustomer = createRefreshSessionClaims();
  const customer = createCustomer({ ...jwtCustomer });
  const successfulTokenizedPayload: HostedFieldsTokenizePayload =
    createHostedFieldsTokenizePayload();

  beforeEach(() => {
    mockRefreshSession.mockResolvedValue(jwtCustomer);
    mockGetCustomer.mockResolvedValue(customer);
    mockHostedFieldsTokenize.mockResolvedValue(successfulTokenizedPayload);
    mockHostedFieldsOn.mockImplementation((eventType, callbackFn) => {
      if (eventType === 'notEmpty') {
        triggerAllCreditCardFieldsComplete = callbackFn;
      }
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  const renderPage = async () => {
    mockLegacyGetPaymentMethods.mockResolvedValue([]);
    const result = renderWithPoetProvidersAndState(<LegacyManagePaymentMethodPage />);
    await waitForElementToBeRemoved(() =>
      result.queryByText('Loading (Test Render Promise Awaiting)...')
    );
    return result;
  };

  it('when add payment method is successful, the user is redirected to view payment methods page', async () => {
    await renderPage();

    actions.completeCreditCardForm();
    expect(views.saveButton()).toBeEnabled();
    await actions.clickSave();

    const expectedRequest: AddPaymentMethodRequest = addPaymentMethodRequest(
      paymentMethodPayload(successfulTokenizedPayload),
      customer,
      customer.billingAddress!!
    );

    expect(mockLegacyAddPaymentMethod).toHaveBeenCalledWith(expectedRequest);
    expect(mockNavigate).toHaveBeenCalledWith(ROUTES.VIEW_PAYMENT_METHODS);
    await waitFor(() =>
      expectNotificationAlertContainsTitle(
        TranslationKeys.CommonComponents.Notification.PAYMENT_METHOD_ADDED_SUCCEEDED
      )
    );
  });

  it('save button is disabled until the form is completed', async () => {
    await renderPage();

    expect(views.saveButton()).toBeDisabled();
    actions.completeCreditCardForm();
    expect(views.saveButton()).toBeEnabled();
  });

  it('should display generic error notification if api fails on submit', async () => {
    mockLegacyAddPaymentMethod.mockRejectedValue(new Error('Any api failure'));

    await renderPage();

    actions.completeCreditCardForm();
    expect(views.saveButton()).toBeEnabled();
    await actions.clickSave();

    expectNotificationAlertContainsTitle(
      TranslationKeys.CommonComponents.Notification.PAYMENT_METHOD_ADD_FAILED
    );
  });

  it('when back is clicked, should navigate back to the payment methods', async () => {
    await renderPage();

    const link = screen.getByTestId('back-link');
    await act(async () => userEvent.click(link));

    expect(mockNavigate).toHaveBeenCalledWith(ROUTES.VIEW_PAYMENT_METHODS);
  });

  it('when cancel is clicked, should navigate back to the payment methods page', async () => {
    await renderPage();

    await actions.clickCancel();

    expect(mockNavigate).toHaveBeenCalledWith(ROUTES.VIEW_PAYMENT_METHODS);
  });

  describe('Billing Address', () => {
    let customerWithBillingAddress: Customer = {
      ...customer,
      billingAddress: createCustomerAddress({
        address1: '300 Marine Ct',
        address2: 'Unit 1',
        postalCode: '07666',
        city: 'Teaneck',
        state: 'NJ'
      })
    };

    beforeEach(() => {
      mockGetCustomer.mockResolvedValue(customerWithBillingAddress);
    });

    it('should display the customers billing address on page load', async () => {
      await renderPage();

      expect(screen.getByText(Tx.BILLING_ADDRESS_TITLE)).toBeInTheDocument();
      expect(views.billingCheckbox()).toBeChecked();
      expect(screen.getByText('300 Marine Ct, Unit 1')).toBeInTheDocument();
      expect(screen.getByText('Teaneck, NJ 07666')).toBeInTheDocument();
    });

    it('should allow user to update billing address and then add payment method', async () => {
      const updatedPostalCode = '11111';
      let updateBillingAddress = {
        ...customerWithBillingAddress.billingAddress!!,
        postalCode: updatedPostalCode
      };
      await renderPage();

      actions.completeCreditCardForm();
      await act(() => userEvent.click(views.billingCheckbox()));
      await act(() => userEvent.clear(views.postalCodeField()));
      await act(() => userEvent.type(views.postalCodeField(), updatedPostalCode));
      await waitFor(() => expect(views.saveButton()).toBeEnabled());
      await actions.clickSave();
      await waitFor(() => expect(views.postalCodeField()).toHaveValue(updatedPostalCode));

      let expectedBillingRequest: UpdateBillingAddressRequest = {
        address: updateBillingAddress
      };
      expect(mockUpdateBillingAddress).toHaveBeenCalledWith(expectedBillingRequest);

      const expectedRequest: AddPaymentMethodRequest = addPaymentMethodRequest(
        paymentMethodPayload(successfulTokenizedPayload),
        customerWithBillingAddress,
        updateBillingAddress
      );
      expect(mockLegacyAddPaymentMethod).toHaveBeenCalledWith(expectedRequest);
    });

    it('alert update billing address failure', async () => {
      mockUpdateBillingAddress.mockRejectedValue(new Error('error'));
      await renderPage();

      actions.completeCreditCardForm();
      await act(() => userEvent.click(views.billingCheckbox()));
      await act(() => userEvent.clear(views.postalCodeField()));
      await act(() => userEvent.type(views.postalCodeField(), '11111'));
      await actions.clickSave();

      await waitFor(() =>
        expectNotificationAlertContainsTitle(
          TranslationKeys.CommonComponents.Notification.BILLING_ADDRESS_SAVE_FAILED
        )
      );
    });
    it('shows redirect text if billing address is null', async () => {
      const customerNoBillingAddress = createCustomer({ billingAddress: undefined });
      mockGetCustomer.mockResolvedValue(customerNoBillingAddress);

      await renderPage();
      expect(views.navigateToAccountPageLink()).toBeInTheDocument();
      await actions.clickRedirectToAccountPage();
      expect(mockNavigate).toHaveBeenCalledWith(ROUTES.ACCOUNT);
    });
  });
});
