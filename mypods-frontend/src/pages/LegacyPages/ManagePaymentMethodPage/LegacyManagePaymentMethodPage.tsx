import React, { useContext, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Checkbox, FormControlLabel, Grid, Typography, useMediaQuery } from '@mui/material';
import { useNavigate } from 'react-router';
import Divider from '@mui/material/Divider';
import { AxiosError } from 'axios';
import { Design } from '../../../helpers/Design';
import { PageLayout } from '../../../components/PageLayout';
import { NotificationContext } from '../../../components/notifications/NotificationContext';
import { ROUTES } from '../../../Routes';
import { BackLink } from '../../../components/buttons/BackLink';
import { SaveButton } from '../../../components/buttons/SaveButton';
import { CancelButton } from '../../../components/buttons/CancelButton';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { <PERSON>ds<PERSON>lert, PodsAlertType } from '../../../components/alert/PodsAlert';
import { theme } from '../../../PodsTheme';
import {
  areAddressEqual,
  formatAddressLine1,
  formatAddressLine2,
  UpdateBillingAddressRequest
} from '../../../networkRequests/responseEntities/CustomerEntities';
import { LegacyCustomerAddressForm } from '../AccountPage/AccountFields/AddressField/LegacyCustomerAddressForm';
import { useLegacyAddressFieldState } from '../AccountPage/AccountFields/AddressField/useLegacyAddressFieldState';
import { useGtmEvents } from '../../../config/google/useGtmEvents';
import { gtmCardType } from '../../../config/google/GoogleEntities';
import { useLegacyAddPaymentMethod } from '../../../networkRequests/legacy/mutations/useLegacyAddPaymentMethod';
import { useLegacyGetPaymentMethods } from '../../../networkRequests/legacy/queries/useLegacyGetPaymentMethods';
import {
  BraintreeForm,
  ErrorReason,
  PaymentErrorResponse
} from '../../ManagePaymentMethodPage/Braintree/BraintreeForm';
import { PaymentMethodPayload } from '../../ManagePaymentMethodPage/Braintree/PaymentMethodPayload';
import {
  addPaymentMethodRequest,
  AddPaymentMethodRequest
} from '../../../networkRequests/responseEntities/PaymentEntities';
import { NoBillingAddressFound } from '../../ManagePaymentMethodPage/NoBillingAddressFound';
import { PaypalCheckout } from '../../ManagePaymentMethodPage/Braintree/PaypalCheckout';
import { ManagePaymentDivider } from '../../ManagePaymentMethodPage/ManagePaymentDivider';
import { useLegacyUpdateBillingAddress } from '../../../networkRequests/legacy/mutations/useLegacyUpdateBillingAddress';
import { useLegacyGetCustomer } from '../../../networkRequests/legacy/queries/useLegacyGetCustomer';

const Tx = TranslationKeys.ManagePaymentMethodsPage;

// -- impls --
export const LegacyManagePaymentMethodPage: React.FC = () => {
  const { t: translate } = useTranslation();
  const { setNotification } = useContext(NotificationContext);
  const navigate = useNavigate();
  const { refetch: refetchPayments } = useLegacyGetPaymentMethods();
  const { customer, updateCustomer } = useLegacyGetCustomer();
  const addressFieldState = useLegacyAddressFieldState(customer.billingAddress);
  const updateBillingAddress = useLegacyUpdateBillingAddress();
  const styles = managePaymentStyles();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const addPaymentMethod = useLegacyAddPaymentMethod();
  const braintreeFormRef = useRef<HTMLFormElement>(null);
  const [isPaypalSelected, setIsPaypalSelected] = useState(false);
  const [isBraintreeFormComplete, setIsBraintreeFormComplete] = useState(false);
  const [isTokenizationPending, setIsTokenizationPending] = useState(false);
  const [isBillingAddressFormShown, setIsBillingAddressFormShown] = useState<boolean>(false);
  const gtmEvents = useGtmEvents();
  const topOfForm = React.createRef<HTMLDivElement>();

  useEffect(() => {
    if (customer.billingAddress) {
      addressFieldState.setValue(customer.billingAddress);
    }
  }, [isBillingAddressFormShown]);

  const [paymentErrors, setPaymentErrors] = useState<ErrorReason[]>([]);

  useEffect(() => {
    if (paymentErrors.length > 0) {
      topOfForm?.current?.scrollIntoView();
    }
  }, [paymentErrors]);

  const isLoading = isTokenizationPending || addPaymentMethod.isPending;
  const isSubmitDisabled =
    isLoading ||
    !isBraintreeFormComplete ||
    (isBillingAddressFormShown && !addressFieldState.isValid()) ||
    paymentErrors.length > 0;

  const clearAllErrors = () => {
    setPaymentErrors([]);
  };

  const submitForm = () => {
    setIsTokenizationPending(true);
    clearAllErrors();

    if (braintreeFormRef !== undefined && braintreeFormRef.current instanceof HTMLFormElement) {
      braintreeFormRef.current.requestSubmit();
    }
    if (isBillingAddressFormShown) {
      addressFieldState.displayErrorForAllFields();
    }
  };

  const callAddPaymentMethod = (payload: PaymentMethodPayload) => {
    const request: AddPaymentMethodRequest = addPaymentMethodRequest(
      payload,
      customer,
      addressFieldState.value!!
    );
    gtmEvents.submitAddPayment(gtmCardType(payload.cardType));
    addPaymentMethod.mutate(request, {
      onSuccess: () => {
        refetchPayments();
        setNotification({
          message: translate(
            TranslationKeys.CommonComponents.Notification.PAYMENT_METHOD_ADDED_SUCCEEDED
          ),
          isError: false
        });
        gtmEvents.successAddPayment(gtmCardType(payload.cardType));
        navigate(ROUTES.VIEW_PAYMENT_METHODS);
      },
      onError: (error: unknown) => {
        setIsPaypalSelected(false);
        if (
          error instanceof AxiosError &&
          error.response?.status === 400 &&
          error.response?.data?.message.includes('Card Not Added: Expiration Date')
        ) {
          setNotification({
            message: translate(
              TranslationKeys.CommonComponents.Notification.PAYMENT_METHOD_ADD_VALIDATION_ERROR
            ),
            isError: true
          });
          return;
        }
        setNotification({
          message: translate(
            TranslationKeys.CommonComponents.Notification.PAYMENT_METHOD_ADD_FAILED
          ),
          isError: true
        });
      }
    });
  };

  const onTokenizationSuccess = (payload: PaymentMethodPayload) => {
    setIsTokenizationPending(false);

    if (
      !isBillingAddressFormShown ||
      areAddressEqual(addressFieldState.value, customer.billingAddress!!)
    ) {
      callAddPaymentMethod(payload);
      return;
    }
    if (!addressFieldState.isValid()) return;

    const request: UpdateBillingAddressRequest = { address: addressFieldState.value };
    updateBillingAddress.mutate(request, {
      onSuccess: () => {
        const updatedCustomer = { ...customer };
        updatedCustomer.billingAddress = request.address;
        updateCustomer(updatedCustomer);
        callAddPaymentMethod(payload);
      },
      onError: () => {
        setNotification({
          message: translate(
            TranslationKeys.CommonComponents.Notification.BILLING_ADDRESS_SAVE_FAILED
          ),
          isError: true
        });
      }
    });
  };

  const onTokenizationFailure = (response: PaymentErrorResponse) => {
    setIsTokenizationPending(false);
    setPaymentErrors(response.reasons);
  };

  // -- paypal handlers --
  const onPaypalSelected = () => {
    gtmEvents.startAddPayment('paypal');
    setIsPaypalSelected(true);
    setPaymentErrors([]);
  };

  const onPaypalSuccess = (payload: PaymentMethodPayload) => {
    clearAllErrors();
    onTokenizationSuccess(payload);
  };

  const onPaypalFailure = (error: string) => {
    setIsTokenizationPending(false);
    setPaymentErrors([{ code: 'paypal', message: error }]);
    setIsPaypalSelected(false);
  };

  const onPaypalCancel = () => {
    clearAllErrors();
    setIsBraintreeFormComplete(false);
    setIsPaypalSelected(false);
  };

  const renderAlertIfError = () => {
    if (paymentErrors.length > 0)
      return (
        <Grid>
          <PodsAlert
            alertType={PodsAlertType.ERROR}
            title="Error"
            description={
              paymentErrors.length > 0 ? paymentErrors.map((it) => it.message).join('<br/>') : ''
            }
          />
        </Grid>
      );
  };

  // If billing address is empty, notify user to go to account page and setup billing address
  if (customer.billingAddress == null) {
    return (
      <PageLayout columnsLg={6}>
        <Grid {...styles.page}>
          <Grid {...styles.headerSection}>
            <BackLink route={ROUTES.VIEW_PAYMENT_METHODS} />
            <NoBillingAddressFound />
          </Grid>
        </Grid>
      </PageLayout>
    );
  }

  return (
    <PageLayout columnsLg={6}>
      <Grid {...styles.page}>
        <Grid {...styles.headerSection}>
          <BackLink route={ROUTES.VIEW_PAYMENT_METHODS} />
          <Typography variant="h1" {...styles.title}>
            {translate(Tx.TITLE)}
          </Typography>
        </Grid>
      </Grid>
      <Grid container>
        <Grid item padding="1rem 0">
          <Typography>{translate(Tx.OTHER_WAYS_TO_PAY)}</Typography>
        </Grid>
        <Grid item container>
          <Grid item xs={isMobile ? 12 : 6}>
            <div id="paypal-disable-wrapper" ref={topOfForm} data-testid="paypal-disable-wrapper">
              <PaypalCheckout
                onSelect={onPaypalSelected}
                onSuccess={onPaypalSuccess}
                onFailure={onPaypalFailure}
                onCancel={onPaypalCancel}
              />
            </div>
          </Grid>
          <Grid item xs={isMobile ? 12 : 6}>
            {/* Financing */}
          </Grid>
        </Grid>
      </Grid>
      {!isPaypalSelected && (
        <>
          <ManagePaymentDivider />
          <Grid container flexDirection="column" {...styles.creditCardInformation}>
            <Grid item>
              <Typography variant="h4" {...styles.title}>
                {translate(Tx.CARD_FORM_TITLE)}
              </Typography>
            </Grid>
            {renderAlertIfError()}
            <BraintreeForm
              postalCode={addressFieldState.value.postalCode}
              formRef={braintreeFormRef}
              onSuccess={onTokenizationSuccess}
              onFailure={onTokenizationFailure}
              setIsBraintreeFormComplete={setIsBraintreeFormComplete}
              onFocus={clearAllErrors}
            />
          </Grid>
          <Grid>
            <Grid item>
              <Typography variant="h4" {...styles.title}>
                {translate(Tx.BILLING_ADDRESS_TITLE)}
              </Typography>
            </Grid>
            <Grid item>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={!isBillingAddressFormShown}
                    onChange={() => setIsBillingAddressFormShown(!isBillingAddressFormShown)}
                    color="secondary"
                  />
                }
                label={
                  <Typography variant="subtitle1">
                    {translate(Tx.BILLING_ADDRESS_CHECKBOX_LABEL)}
                  </Typography>
                }
                labelPlacement="end"
              />
            </Grid>
            {isBillingAddressFormShown ? (
              <Grid>
                <Grid sx={{ padding: '1rem 0' }}>
                  <Typography variant="body1">{translate(Tx.BILLING_ADDRESS_TITLE)}</Typography>
                  <Typography variant="subtitle2">
                    {translate(Tx.BILLING_ADDRESS_SUBTITLE)}
                  </Typography>
                </Grid>
                <LegacyCustomerAddressForm state={addressFieldState} />
                <Grid sx={{ paddingTop: '1.5rem' }}>
                  <Divider />
                </Grid>
              </Grid>
            ) : (
              <>
                <Grid item>
                  <Typography variant="body2">
                    {formatAddressLine1(customer.billingAddress)}
                  </Typography>
                </Grid>
                <Grid item>
                  <Typography variant="body2">
                    {formatAddressLine2(customer.billingAddress)}
                  </Typography>
                </Grid>
              </>
            )}
          </Grid>
        </>
      )}
      <Grid container {...styles.submitButtons}>
        <CancelButton
          onClick={() => {
            navigate(ROUTES.VIEW_PAYMENT_METHODS);
          }}
        />
        <SaveButton
          onClick={submitForm}
          disabled={isSubmitDisabled}
          isLoading={isLoading}
          label={translate(Tx.SAVE_BUTTON)}
          styles={{ sx: { width: '230px' } }}
        />
      </Grid>
    </PageLayout>
  );
};

// -- styles --
const managePaymentStyles = () => ({
  page: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      gap: Design.Primitives.Spacing.lgPlus
    }
  },
  headerSection: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      gap: Design.Primitives.Spacing.sm,
      marginBottom: '1.5rem'
    }
  },
  title: {
    sx: { color: Design.Alias.Color.accent900 }
  },
  creditCardInformation: {
    gap: Design.Primitives.Spacing.sm,
    sx: {
      padding: '1.5rem 0'
    }
  },
  submitButtons: {
    sx: {
      justifyContent: 'right',
      paddingTop: Design.Primitives.Spacing.md,
      gap: '0.5rem'
    }
  }
});
