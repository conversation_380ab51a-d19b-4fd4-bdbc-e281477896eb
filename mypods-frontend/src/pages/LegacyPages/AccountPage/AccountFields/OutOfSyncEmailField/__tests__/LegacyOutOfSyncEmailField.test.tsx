import React from 'react';
import { AxiosError, AxiosResponse } from 'axios';
import { screen, waitFor, within } from '@testing-library/react';
import { TranslationKeys } from '../../../../../../locales/TranslationKeys';
import {
  createCustomer,
  createEmail,
  createRefreshSessionClaims
} from '../../../../../../testUtils/MyPodsFactories';
import {
  renderWithQueryProvider,
  runPendingPromises
} from '../../../../../../testUtils/RenderHelpers';
import {
  mockLegacyGetCustomer,
  mockLegacyUpdateEmail,
  mockRefreshSession
} from '../../../../../../../setupTests';
import {
  accountPageActions as actions,
  legacyAccountPageViews as views
} from '../../../__tests__/LegacyAccountPageViews';
import {
  UpdateEmailErrorStatus,
  UpdateEmailRequest
} from '../../../../../../networkRequests/responseEntities/CustomerEntities';
import { expectNotificationAlertContainsTitle } from '../../../../../../testUtils/assertions';
import { LegacyOutOfSyncEmailField } from '../LegacyOutOfSyncEmailField';

const Tx = TranslationKeys.AccountPage.AccountInfo.Email;

describe('LegacyOutOfSyncEmailField', () => {
  const customerId = '*********';
  const customer = createCustomer({
    id: customerId,
    email: createEmail({ id: 42, address: '<EMAIL>' }),
    username: '<EMAIL>'
  });
  let newEmail = '<EMAIL>';

  beforeEach(() => {
    mockRefreshSession.mockResolvedValue(createRefreshSessionClaims());
    mockLegacyGetCustomer.mockResolvedValue(customer);
  });

  const renderEmailField = async () => {
    const result = renderWithQueryProvider(<LegacyOutOfSyncEmailField />);
    await runPendingPromises();
    return result;
  };

  const renderAndUpdateEmail = async () => {
    await renderEmailField();
    await actions.editOutOfSyncEmail();
    await actions.saveOutOfSyncEmail();
  };

  it('should show radio buttons with the okta username selected by default', async () => {
    await renderEmailField();
    await actions.editOutOfSyncEmail();

    expect(screen.getByText(Tx.OutOfSync.LABEL));
    expect(screen.getByLabelText(Tx.OutOfSync.OTHER));
    expect(screen.getByLabelText(customer.username!!));
    expect(views.outOfSyncEmail.saveButton()).toBeEnabled();
  });

  it('should save the okta username', async () => {
    await renderEmailField();
    await actions.editOutOfSyncEmail();
    await actions.saveOutOfSyncEmail();

    const expectedRequest: UpdateEmailRequest = {
      email: { id: customer.email!.id, address: customer.username }
    };
    expect(views.outOfSyncEmail.accountField()).toBeInTheDocument(); // Restore the display view
    expect(mockLegacyUpdateEmail).toHaveBeenCalledWith(expectedRequest);
    expect(screen.getByText(customer.username!)).toBeInTheDocument();
    expectNotificationAlertContainsTitle(
      TranslationKeys.CommonComponents.Notification.EMAIL_SAVE_SUCCEEDED
    );
  });

  it('should allow the user to select other and change the email to anything else', async () => {
    await renderEmailField();
    await actions.editOutOfSyncEmail();
    await actions.selectRadioChoice(Tx.OutOfSync.OTHER);
    await actions.enterEmail(newEmail);
    await actions.saveOutOfSyncEmail();

    const expectedRequest: UpdateEmailRequest = {
      email: { id: customer.email!.id, address: newEmail }
    };
    expect(mockLegacyUpdateEmail).toHaveBeenCalledWith(expectedRequest);
    expect(screen.getByText(newEmail!)).toBeInTheDocument();
  });

  describe('given the user receives a server error for the email, they will see helper text upon save', async () => {
    test.each([
      [UpdateEmailErrorStatus.EMAIL_ALREADY_IN_USE, Tx.HelperText.EMAIL_ALREADY_IN_USE],
      [UpdateEmailErrorStatus.INVALID_EMAIL, Tx.HelperText.INVALID_EMAIL]
    ])('when %s response code should show %s message', async (responseCode, displayText) => {
      const response: AxiosResponse = {
        data: { status: responseCode },
        status: 500
      } as AxiosResponse;

      mockLegacyUpdateEmail.mockRejectedValue(
        new AxiosError('message', '500', undefined, undefined, response)
      );

      await renderAndUpdateEmail();

      expect(within(views.outOfSyncEmail.baseField()).getByText(displayText)).toBeInTheDocument();
    });
  });

  describe('displays alert notifications when the updateEmail call fails with an error', () => {
    test.each([
      [
        UpdateEmailErrorStatus.ACCOUNT_UNDER_MAINTENANCE,
        Tx.Notifications.Title.ACCOUNT_UNDER_MAINTENANCE,
        Tx.Notifications.Message.ACCOUNT_UNDER_MAINTENANCE
      ],
      [UpdateEmailErrorStatus.ERROR, Tx.Notifications.Title.ERROR, Tx.Notifications.Message.ERROR],
      [
        UpdateEmailErrorStatus.NO_ACCOUNT_FOUND,
        Tx.Notifications.Title.NO_ACCOUNT_FOUND,
        Tx.Notifications.Message.NO_ACCOUNT_FOUND
      ],
      [
        UpdateEmailErrorStatus.TOKEN_EXPIRED,
        Tx.Notifications.Title.TOKEN_EXPIRED,
        Tx.Notifications.Message.TOKEN_EXPIRED
      ],
      [
        UpdateEmailErrorStatus.TOKEN_INVALID,
        Tx.Notifications.Title.TOKEN_INVALID,
        Tx.Notifications.Message.TOKEN_INVALID
      ]
    ])('when %s occurs display %s and %s', async (responseCode, title, message) => {
      const response: AxiosResponse = {
        data: { status: responseCode },
        status: 500
      } as AxiosResponse;

      mockLegacyUpdateEmail.mockRejectedValue(
        new AxiosError('message', '500', undefined, undefined, response)
      );

      await renderAndUpdateEmail();

      await waitFor(() => {
        expect(screen.getByRole('alert')).toHaveTextContent(title);
        expect(screen.getByRole('alert')).toHaveTextContent(message);
      });
    });
  });

  it('given an error occurs, displays generic failure', async () => {
    mockLegacyUpdateEmail.mockRejectedValue('Something went wrong.');

    await renderAndUpdateEmail();

    expectNotificationAlertContainsTitle(
      TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE
    );
  });
});
