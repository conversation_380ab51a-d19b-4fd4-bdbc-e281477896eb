import React, { useContext, useState } from 'react';
import { Grid, Switch } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { Design } from '../../../../../helpers/Design';
import { Txt } from '../../../../../components/Txt';
import { TranslationKeys } from '../../../../../locales/TranslationKeys';
import { UpdateSmsOptInRequest } from '../../../../../networkRequests/responseEntities/CustomerEntities';
import { NotificationContext } from '../../../../../components/notifications/NotificationContext';
import { GtmAccountDetailType } from '../../../../../config/google/GoogleEntities';
import { createGtmErrorRequest } from '../../../../../config/google/googleAnalyticsUtils';
import { useLegacyUpdateSmsOptIn } from '../../../../../networkRequests/legacy/mutations/useLegacyUpdateSmsOptIn';
import { useLegacyGetCustomer } from '../../../../../networkRequests/legacy/queries/useLegacyGetCustomer';
import { useAccountUpdateEvents } from '../../../../../config/useAccountUpdateEvents';

// -- impls --
export const LegacySmsPreferencesField = () => {
  const { setNotification } = useContext(NotificationContext);
  const { t: translate } = useTranslation();
  const { sendSubmitAccountDetailsEvent, sendEditAccountDetailsSuccessEvent, sendErrorEvent } =
    useAccountUpdateEvents();
  const detailType: GtmAccountDetailType = 'sms_preference';
  const { customer, updateCustomer } = useLegacyGetCustomer();
  const [isOptedIn, setIsOptedIn] = useState<boolean>(customer.smsOptIn ?? false);
  const updateSmsOptIn = useLegacyUpdateSmsOptIn();
  const styles = communicationFieldStyles();

  const handleToggle = (event: React.ChangeEvent<HTMLInputElement>) => {
    const isChecked = event.target.checked;
    setIsOptedIn(isChecked);

    const request: UpdateSmsOptInRequest = {
      lastName: customer.lastName,
      primaryPhone: customer.primaryPhone,
      secondaryPhone: customer.secondaryPhone,
      newSmsOptIn: isChecked
    };

    sendSubmitAccountDetailsEvent(detailType);
    updateSmsOptIn.mutate(request, {
      onSuccess: () => {
        sendEditAccountDetailsSuccessEvent(detailType);
        setIsOptedIn(isChecked);
        updateCustomer({ ...customer, smsOptIn: isChecked });
        setNotification({
          message: translate(
            TranslationKeys.CommonComponents.Notification.SMS_PREFERENCES_SAVE_SUCCEEDED
          )
        });
      },
      onError: () => {
        setIsOptedIn(!isChecked);
        setNotification({
          message: translate(TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE),
          isError: true
        });
        sendErrorEvent(
          createGtmErrorRequest('Failed to update SMS preferences', 'account', 'backend')
        );
      }
    });
  };

  return (
    <Grid container data-testid="account-field" {...styles.accountField}>
      <Grid item container {...styles.fieldLabel}>
        <Grid
          container
          item
          flexDirection="row"
          alignContent="space-between"
          justifyContent="space-between">
          <Grid item>
            <Txt {...styles.fieldLabelText}>
              {translate(TranslationKeys.AccountPage.Communication.SmsPreferences.LABEL)}
            </Txt>
          </Grid>
          <Grid item position="relative">
            <Grid item position="absolute" top={0} right={0}>
              <Switch
                onChange={handleToggle}
                disabled={updateSmsOptIn.isPending}
                color="secondary"
                checked={isOptedIn}
                data-testid="sms-opt-in-switch"
              />
            </Grid>
          </Grid>
        </Grid>
      </Grid>
      <Grid item {...styles.fieldValue}>
        <Txt {...styles.fieldValueText}>
          {translate(TranslationKeys.AccountPage.Communication.SmsPreferences.DESCRIPTION)}
        </Txt>
      </Grid>
    </Grid>
  );
};

const communicationFieldStyles = () => ({
  accountField: {
    sx: {
      paddingY: Design.Primitives.Spacing.sm,
      borderBottom: `1px solid ${Design.Alias.Color.neutral300}`,
      gap: Design.Primitives.Spacing.xxs,
      flexDirection: 'column'
    }
  },
  fieldLabel: { sx: { flexDirection: 'row' } },
  fieldLabelText: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.Md,
      color: Design.Alias.Color.neutral700
    }
  },
  fieldValue: {},
  fieldValueText: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.Md,
      color: Design.Alias.Color.accent900
    }
  }
});
