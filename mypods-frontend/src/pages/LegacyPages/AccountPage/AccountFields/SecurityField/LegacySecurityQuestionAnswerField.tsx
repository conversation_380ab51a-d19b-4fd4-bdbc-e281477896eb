import React from 'react';
import { Grid } from '@mui/material';
import { Design } from '../../../../../helpers/Design';
import { ShowHideButton } from '../../../../../components/buttons/ShowHideButton';
import { LegacyOccludedSecurityField } from '../PinField/LegacyOccludedSecurityField';
import { LegacySecurityFieldLabel } from './LegacySecurityFieldLabel';

export const LegacySecurityQuestionAnswerField = (props: {
  questionLabel: string;
  answerLabel: string;
  questionValue: string;
  answerValue: string;
}) => {
  const [showQuestionAnswer, setShowQuestionAnswer] = React.useState(false);
  const styles = securityQAStyles();

  return (
    <Grid container data-testid="account-field" {...styles.wrapper}>
      <Grid item container flexDirection="row">
        <LegacySecurityFieldLabel label={props.questionLabel} />
        <Grid item flexGrow={0}>
          <ShowHideButton
            onClick={() => {
              setShowQuestionAnswer(!showQuestionAnswer);
            }}
            dataVisible={showQuestionAnswer}
          />
        </Grid>
      </Grid>
      <LegacyOccludedSecurityField value={props.questionValue} isVisible={showQuestionAnswer} />

      <LegacySecurityFieldLabel label={props.answerLabel} />
      <LegacyOccludedSecurityField value={props.answerValue} isVisible={showQuestionAnswer} />
    </Grid>
  );
};

const securityQAStyles = () => ({
  wrapper: {
    sx: {
      flexDirection: 'column',
      gap: Design.Primitives.Spacing.xxs,
      paddingY: Design.Primitives.Spacing.sm,
      borderBottom: `1px solid ${Design.Alias.Color.neutral300}`
    }
  }
});
