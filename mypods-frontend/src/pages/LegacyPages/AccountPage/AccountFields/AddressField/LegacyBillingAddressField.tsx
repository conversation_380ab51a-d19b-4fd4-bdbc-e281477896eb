import React from 'react';
import { Grid } from '@mui/material';
import {
  CustomerAddress,
  UpdateBillingAddressRequest
} from '../../../../../networkRequests/responseEntities/CustomerEntities';
import { TranslationKeys } from '../../../../../locales/TranslationKeys';
import {
  EditableAddressFieldProps,
  LegacyEditableAddressField
} from './LegacyEditableAddressField';
import {
  BILLING_ADDRESS_TYPE,
  NEW_IDENTITY,
  US_REGION_CODE
} from '../../../../../networkRequests/MyPodsConstants';
import { GtmAccountDetailType } from '../../../../../config/google/GoogleEntities';
import { useLegacyGetCustomer } from '../../../../../networkRequests/legacy/queries/useLegacyGetCustomer';
import { useLegacyUpdateBillingAddress } from '../../../../../networkRequests/legacy/mutations/useLegacyUpdateBillingAddress';
import { useAccountUpdateEvents } from '../../../../../config/useAccountUpdateEvents';

export const LegacyBillingAddressField = () => {
  const { customer, updateCustomer } = useLegacyGetCustomer();
  const { sendSubmitAccountDetailsEvent, sendEditAccountDetailsSuccessEvent } =
    useAccountUpdateEvents();
  const detailType: GtmAccountDetailType = 'address_billing';
  const updateBillingAddress = useLegacyUpdateBillingAddress();

  const initializeAddress = (address: CustomerAddress | undefined): CustomerAddress => {
    if (address == null)
      return {
        id: NEW_IDENTITY,
        addressType: BILLING_ADDRESS_TYPE,
        regionCode: US_REGION_CODE, // never canada for now
        address1: '',
        postalCode: '',
        city: '',
        state: ''
      };
    return { ...address };
  };

  const getRequest = (updatedAddress: CustomerAddress): UpdateBillingAddressRequest => ({
    address: updatedAddress
  });

  function onSuccessfulUpdate(request: UpdateBillingAddressRequest) {
    const updatedCustomer = { ...customer };
    if (request.address != null) {
      updatedCustomer.billingAddress = request.address;
    }
    updateCustomer(updatedCustomer);
  }

  const update = (value: CustomerAddress, onSuccess: () => void, onError: () => void) => {
    const updateRequest = getRequest(value);
    sendSubmitAccountDetailsEvent(detailType);
    updateBillingAddress.mutate(updateRequest, {
      onSuccess: (_, request) => {
        sendEditAccountDetailsSuccessEvent(detailType);
        onSuccessfulUpdate(request);
        onSuccess();
      },
      onError: () => {
        onError();
      }
    });
  };

  const getEditableFieldProps = (): EditableAddressFieldProps => ({
    currentAddress: customer.billingAddress,
    initializedAddress: initializeAddress(customer.billingAddress),
    update,
    isPendingUpdate: updateBillingAddress.isPending,
    labelKey: TranslationKeys.AccountPage.AddressInfo.Labels.BILLING_ADDRESS,
    gtmDetailType: detailType,
    successNotificationKey:
      TranslationKeys.CommonComponents.Notification.BILLING_ADDRESS_SAVE_SUCCEEDED
  });

  return (
    <Grid data-testid="billing-address-field">
      <LegacyEditableAddressField {...getEditableFieldProps()} />
    </Grid>
  );
};
