import React, { useEffect, useRef, useState } from 'react';
import { Grid, TextField } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { useMapsLibrary } from '@vis.gl/react-google-maps';
import { Design } from '../../../../../helpers/Design';
import { Txt } from '../../../../../components/Txt';
import { TranslationKeys } from '../../../../../locales/TranslationKeys';
import { IAddressFieldState } from './useLegacyAddressFieldState';
import { getAddressFromPlace } from '../../../../../helpers/googlePlace/getAddressFromPlace';

interface CustomerAddressFormProps {
  label?: string;
  state: IAddressFieldState;
}

export const LegacyCustomerAddressForm = ({ label, state }: CustomerAddressFormProps) => {
  const { t: translate } = useTranslation();
  const { value, error } = state;
  const [placeAutocomplete, setPlaceAutocomplete] =
    useState<google.maps.places.Autocomplete | null>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const places = useMapsLibrary('places');

  useEffect(() => {
    if (!places || !inputRef.current) return;

    const options = {
      componentRestrictions: { country: ['us', 'ca'] },
      fields: ['address_components'],
      types: ['address']
    };

    setPlaceAutocomplete(new places.Autocomplete(inputRef.current, options));
  }, [places]);

  useEffect(() => {
    if (!placeAutocomplete) return;
    placeAutocomplete.addListener('place_changed', handleGooglePlaceChanged);
  }, [placeAutocomplete]);

  const handleGooglePlaceChanged = () => {
    if (!placeAutocomplete) return;
    const placeAddress = getAddressFromPlace(placeAutocomplete.getPlace());
    const addressToSet = state.getValueFromPlaceAddress(placeAddress);

    state.setValue(addressToSet);
  };

  return (
    <Grid
      container
      flexDirection="column"
      gap={Design.Primitives.Spacing.sm}
      data-testid="address-form">
      {label && (
        <Grid container item flexDirection="row" justifyContent="space-between">
          <Txt
            style={Design.Alias.Text.BodyUniversal.Md}
            sx={{ color: Design.Alias.Color.neutral700 }}>
            {label}
          </Txt>
        </Grid>
      )}
      <Grid item>
        <TextField
          autoFocus
          fullWidth
          color="secondary"
          label={translate(TranslationKeys.AccountPage.AddressInfo.InputFields.ADDRESS1)}
          value={value.address1}
          error={!!error?.streetAddress}
          helperText={error?.streetAddress}
          onBlur={state.displayStreetAddressError}
          onChange={(event) => {
            state.handleAddress1Change(event.target.value);
          }}
          placeholder=""
          inputRef={inputRef}
        />
      </Grid>
      <Grid item>
        <TextField
          fullWidth
          color="secondary"
          label={translate(TranslationKeys.AccountPage.AddressInfo.InputFields.ADDRESS2)}
          value={value.address2}
          onChange={(event) => {
            state.handleAddress2Change(event.target.value);
          }}
        />
      </Grid>
      <Grid item>
        <TextField
          fullWidth
          color="secondary"
          label={translate(TranslationKeys.AccountPage.AddressInfo.InputFields.POSTAL_CODE)}
          value={value.postalCode}
          error={!!error?.postalCode}
          helperText={error?.postalCode}
          onBlur={state.displayPostalCodeError}
          onChange={(event) => {
            state.handlePostalCodeChange(event.target.value);
          }}
        />
      </Grid>
      <Grid container item spacing={1}>
        <Grid item xs={6}>
          <TextField
            fullWidth
            color="secondary"
            label={translate(TranslationKeys.AccountPage.AddressInfo.InputFields.CITY)}
            value={value.city}
            error={!!error?.city}
            helperText={error?.city}
            onBlur={state.displayCityError}
            onChange={(event) => {
              state.handleCityChange(event.target.value);
            }}
          />
        </Grid>
        <Grid item xs={6}>
          <TextField
            fullWidth
            color="secondary"
            label={translate(TranslationKeys.AccountPage.AddressInfo.InputFields.STATE)}
            value={value.state}
            error={!!error?.state}
            helperText={error?.state}
            onBlur={state.displayStateError}
            onChange={(event) => {
              state.handleStateChange(event.target.value);
            }}
          />
        </Grid>
      </Grid>
    </Grid>
  );
};
