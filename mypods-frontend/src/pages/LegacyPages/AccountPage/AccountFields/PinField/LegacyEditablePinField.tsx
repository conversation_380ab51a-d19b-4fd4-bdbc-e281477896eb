import React, { <PERSON>actNode, useContext, useEffect, useState } from 'react';
import { FormHelperText, Grid, TextField, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { Design } from '../../../../../helpers/Design';
import { Txt } from '../../../../../components/Txt';
import { CancelButton } from '../../../../../components/buttons/CancelButton';
import { SaveButton } from '../../../../../components/buttons/SaveButton';
import { LegacyAccountField } from '../LegacyAccountField';
import { TranslationKeys } from '../../../../../locales/TranslationKeys';
import { ShowHideButton } from '../../../../../components/buttons/ShowHideButton';
import {
  DashIcon,
  GreenCheckmarkIcon,
  RedXIcon
} from '../../../../../components/icons/GreenCheckmarkIcon';
import { UpdatePinRequest } from '../../../../../networkRequests/responseEntities/CustomerEntities';
import { PinMask } from '../../../../../helpers/validation/PhoneMask';
import { NotificationContext } from '../../../../../components/notifications/NotificationContext';
import { GtmAccountDetailType } from '../../../../../config/google/GoogleEntities';
import { useLegacyUpdatePin } from '../../../../../networkRequests/legacy/mutations/useLegacyUpdatePin';
import { useAccountUpdateEvents } from '../../../../../config/useAccountUpdateEvents';
import { callOnEnterPress } from '../../../../../helpers/eventHelpers';

export type EditablePinFieldProps = {
  displayValue: string;
  getUpdatePinRequest: (updatedValue: string) => UpdatePinRequest;
  isInputValid: (value: string) => boolean;
  labelKey: string;
  successNotificationKey: string;
};

export const LegacyEditablePinField: React.FC<EditablePinFieldProps> = ({
  displayValue,
  getUpdatePinRequest,
  isInputValid,
  labelKey,
  successNotificationKey
}) => {
  const { setNotification } = useContext(NotificationContext);
  const { t: translate } = useTranslation();
  const {
    sendStartEditingAccountDetailsEvent,
    sendSubmitAccountDetailsEvent,
    sendEditAccountDetailsSuccessEvent
  } = useAccountUpdateEvents();
  const detailType: GtmAccountDetailType = 'pin';
  const updatePin = useLegacyUpdatePin();
  const [value, setValue] = useState<string>(displayValue);
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [error, setError] = useState<ReactNode | undefined>();
  const [displayValidation, setDisplayValidation] = useState<boolean>(false);
  const label = translate(labelKey);
  const [dataVisible, setDataVisible] = useState<boolean>(false);

  useEffect(() => {
    setError(undefined);
  }, [isEditing]);

  const inputIsInvalidOrUnchanged = () => {
    if (value === displayValue) return true;
    return !isInputValid(value);
  };

  const setErrorIfInvalid = (newValue: string) => {
    if (!isInputValid(newValue)) {
      setError(
        <Grid flexDirection="column" color={Design.Alias.Color.primary300}>
          <Typography variant="subtitle2" color="inherit">
            {translate(TranslationKeys.CommonComponents.Input.Error.Validation.BASE)}
          </Typography>
          <Typography
            variant="subtitle2"
            color="inherit"
            sx={{ paddingLeft: Design.Primitives.Spacing.xxs }}>
            {translate(TranslationKeys.CommonComponents.Input.Error.Validation.PIN)}
          </Typography>
        </Grid>
      );
    } else {
      setError(undefined);
    }
  };

  const handleValueChange = (newValue: string) => {
    setValue(newValue);
    setErrorIfInvalid(newValue);
    setDisplayValidation(false);
  };

  const handleCancel = () => {
    setValue(displayValue);
    setIsEditing(false);
  };

  const handleEditClick = () => {
    sendStartEditingAccountDetailsEvent(detailType);
    setIsEditing(true);
    setValue('');
    setDataVisible(false);
  };

  const handleSave = () => {
    setErrorIfInvalid(value);
    setDisplayValidation(true);
    if (inputIsInvalidOrUnchanged()) return;

    const updatePinRequest = getUpdatePinRequest(value);
    sendSubmitAccountDetailsEvent(detailType);
    updatePin.mutate(updatePinRequest, {
      onSuccess: (_) => {
        sendEditAccountDetailsSuccessEvent(detailType);
        setNotification({
          message: translate(successNotificationKey)
        });
        setIsEditing(false);
      },
      onError: () => {
        setNotification({
          message: translate(TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE),
          isError: true
        });
      }
    });
  };

  const renderValidationMessage = () => {
    const iconStyle = { sx: { height: '16px', width: '16px' } };
    let icon = <GreenCheckmarkIcon {...iconStyle} />;
    if (value === '') icon = <DashIcon {...iconStyle} />;
    if (error) icon = <RedXIcon {...iconStyle} />;

    return (
      <Grid display="flex" flexDirection="row" gap={Design.Primitives.Spacing.xxs}>
        {icon}
        <Typography variant="subtitle2" color={error ? Design.Alias.Color.primary300 : 'initial'}>
          Contain 4 numbers
        </Typography>
      </Grid>
    );
  };

  return (
    <Grid data-testid="editable-pin-field">
      {!isEditing ? (
        <LegacyAccountField label={label} value={displayValue} onEditClick={handleEditClick} />
      ) : (
        <Grid
          container
          flexDirection="column"
          gap={Design.Primitives.Spacing.xxs}
          sx={{
            paddingY: Design.Primitives.Spacing.sm,
            borderBottom: `1px solid ${Design.Alias.Color.neutral300}`
          }}>
          <Grid container flexDirection="column" gap={Design.Primitives.Spacing.sm}>
            <Grid container flexDirection="row" justifyContent="space-between">
              <Txt
                style={Design.Alias.Text.BodyUniversal.Md}
                sx={{ color: Design.Alias.Color.neutral700 }}>
                {label}
              </Txt>
            </Grid>
            <Grid
              container
              flexDirection="column"
              sx={{
                marginBottom: Design.Primitives.Spacing.sm,
                marginTop: Design.Primitives.Spacing.xxs
              }}>
              <TextField
                autoFocus
                InputProps={{
                  endAdornment: (
                    <ShowHideButton
                      onClick={() => {
                        setDataVisible(!dataVisible);
                      }}
                      dataVisible={dataVisible}
                    />
                  ),
                  inputComponent: PinMask as any
                }}
                fullWidth
                color="secondary"
                inputRef={(input) => input && input.focus()}
                label={label}
                error={displayValidation && !!error}
                onChange={(event) => {
                  handleValueChange(event.target.value);
                }}
                onKeyDown={callOnEnterPress(handleSave)}
                type={dataVisible ? 'text' : 'password'}
              />
              {displayValidation && <FormHelperText component="div">{error}</FormHelperText>}
            </Grid>
          </Grid>
          {renderValidationMessage()}
          <Grid
            sx={{ marginTop: Design.Primitives.Spacing.xxs }}
            container
            columnGap={Design.Primitives.Spacing.xxs}
            justifyContent="flex-end">
            <CancelButton onClick={handleCancel} disabled={updatePin.isPending} />
            <SaveButton
              onClick={handleSave}
              isLoading={updatePin.isPending}
              disabled={value === displayValue}
            />
          </Grid>
        </Grid>
      )}
    </Grid>
  );
};
