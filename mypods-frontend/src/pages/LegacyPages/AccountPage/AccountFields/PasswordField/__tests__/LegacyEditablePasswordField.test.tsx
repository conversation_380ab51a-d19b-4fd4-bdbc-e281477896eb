import { screen, waitFor, within } from '@testing-library/react';
import {
  createCustomer,
  createEmail,
  createRefreshSessionClaims,
  createUpdatePasswordResponse
} from '../../../../../../testUtils/MyPodsFactories';
import {
  renderWithLegacyProvidersAndState,
  runPendingPromises
} from '../../../../../../testUtils/RenderHelpers';
import { legacyAccountPageViews as views } from '../../../__tests__/LegacyAccountPageViews';
import { LegacyEditablePasswordField } from '../LegacyEditablePasswordField';
import userEvent from '@testing-library/user-event';
import { TranslationKeys } from '../../../../../../locales/TranslationKeys';
import { UpdatePasswordResponseCode } from '../../../../../../networkRequests/responseEntities/CustomerEntities';
import {
  mockGetCustomer,
  mockLegacyUpdatePassword,
  mockRefreshSession
} from '../../../../../../../setupTests';
import { act } from 'react';
import { AxiosError, AxiosResponse } from 'axios';
import { expectNotificationAlertContainsTitle } from '../../../../../../testUtils/assertions';

const Tx = TranslationKeys.AccountPage.AccountInfo.Password;

describe('Editable password field', () => {
  const oldPassword = 'Current123!';
  const newPassword = 'New pass123!';
  const customerId = '*********';
  const customer = createCustomer({
    id: customerId,
    email: createEmail({ address: '<EMAIL>' })
  });

  const renderPage = async () => {
    const result = renderWithLegacyProvidersAndState(
      <LegacyEditablePasswordField
        displayValue="some test string"
        customerEmail={customer.email!}
      />
    );
    await runPendingPromises();
    return result;
  };

  const userChangesPasswordWith = async (
    oldPwd: string = oldPassword,
    newPwd: string = newPassword
  ) => {
    await act(async () => {
      await userEvent.click(views.password.editButton());
    });
    await act(async () => {
      await userEvent.type(views.password.currentInputField(), oldPwd);
      await userEvent.type(views.password.newInputField(), newPwd);
      await userEvent.click(views.password.saveButton());
    });
  };

  beforeEach(() => {
    mockRefreshSession.mockResolvedValue(createRefreshSessionClaims());
    mockGetCustomer.mockResolvedValue(customer);
  });

  it('should convert to edit mode on clicking edit button', async () => {
    await renderPage();

    expect(views.password.editView()).not.toBeInTheDocument();
    await act(async () => {
      await userEvent.click(views.password.editButton());
    });

    expect(views.password.currentInputField()).toBeInTheDocument();
    expect(views.password.newInputField()).toBeInTheDocument();
    expect(views.password.saveButton()).toBeInTheDocument();
    expect(views.password.cancelButton()).toBeInTheDocument();
  });

  it('should show us the read only view when cancel is clicked', async () => {
    await renderPage();

    expect(views.password.editView()).not.toBeInTheDocument();
    await act(async () => {
      await userEvent.click(views.password.editButton());
    });
    expect(views.password.editView()).toBeInTheDocument();

    await act(async () => {
      await userEvent.click(views.password.cancelButton());
    });
    expect(views.password.editView()).not.toBeInTheDocument();
  });

  test.each([
    [0, 0, 4, ''],
    [1, 3, 0, 'P'],
    [2, 2, 0, 'Pa'],
    [3, 1, 0, 'Password'],
    [4, 0, 0, 'Password!']
  ])(
    `should return %d success, %d error, and %d default icons when inputting %s`,
    async (successRuleCount, errorRuleCount, defaultIconCount, inputText) => {
      await renderPage();
      await act(async () => {
        await userEvent.click(views.password.editButton());
      });

      const passwordField = views.password.newInputField();

      if (inputText != '') {
        await act(async () => {
          await userEvent.type(passwordField, inputText);
        });
      }
      await waitFor(() => {
        expect(screen.queryAllByTestId('password-rule-success')).toHaveLength(successRuleCount);
        expect(screen.queryAllByTestId('password-rule-error')).toHaveLength(errorRuleCount);
        expect(screen.queryAllByTestId('password-rule-default')).toHaveLength(defaultIconCount);
      });
    }
  );
  it('should enable save button when current password and new password are entered', async () => {
    await renderPage();

    await act(async () => {
      await userEvent.click(views.password.editButton());
    });

    expect(views.password.saveButton()).toBeDisabled();

    await act(async () => {
      await userEvent.type(views.password.currentInputField(), 'currentPassword');
      await userEvent.type(views.password.newInputField(), 'newPassword');
    });
    await waitFor(() => {
      expect(views.password.saveButton()).not.toBeDisabled();
    });
  });

  it('should save new password and show success snack bar when save button is clicked', async () => {
    mockLegacyUpdatePassword.mockResolvedValue({});

    await renderPage();

    await act(async () => {
      await userEvent.click(views.password.editButton());
    });

    await act(async () => {
      await userEvent.type(views.password.currentInputField(), oldPassword);
      await userEvent.type(views.password.newInputField(), newPassword);
    });

    expect(views.password.saveButton()).not.toBeDisabled();
    await act(async () => {
      await userEvent.click(views.password.saveButton());
    });

    expect(mockLegacyUpdatePassword).toHaveBeenCalledWith(
      expect.objectContaining({
        oldPassword,
        newPassword,
        email: customer.email
      })
    );
  });

  it('should render the current password, when the view icon is clicked', async () => {
    await renderPage();

    await act(async () => {
      await userEvent.click(views.password.editButton());
    });
    expect(views.password.currentInputField()).toHaveAttribute('type', 'password');
    expect(views.password.newInputField()).toHaveAttribute('type', 'password');

    await act(async () => {
      await userEvent.click(views.password.currentShowHideButton());
    });
    expect(views.password.currentInputField()).toHaveAttribute('type', 'text');
    expect(views.password.newInputField()).toHaveAttribute('type', 'password');
  });

  it('should display the password requirements when edit is clicked', async () => {
    await renderPage();

    await act(async () => {
      await userEvent.click(views.password.editButton());
    });
    expect(
      screen.getByText(TranslationKeys.AccountPage.AccountInfo.Password.VALIDATION_HEADER)
    ).toBeInTheDocument();
    Object.values(TranslationKeys.AccountPage.AccountInfo.Password.Rules).forEach((key) =>
      expect(screen.getByText(key)).toBeInTheDocument()
    );
  });

  it('should disable the save button until both new and current passwords are entered', async () => {
    await renderPage();

    await act(async () => {
      await userEvent.click(views.password.editButton());
    });
    expect(views.password.saveButton()).toBeDisabled();

    await act(async () => {
      await userEvent.type(views.password.currentInputField(), 'My0ldPassword!');
    });
    expect(views.password.saveButton()).toBeDisabled();
    await act(async () => {
      await userEvent.clear(views.password.currentInputField());
    });

    await act(async () => {
      await userEvent.type(views.password.newInputField(), 'Pass123!');
    });
    expect(views.password.saveButton()).toBeDisabled();

    await act(async () => {
      await userEvent.type(views.password.currentInputField(), 'My0ldPassword!');
    });
    expect(views.password.saveButton()).toBeEnabled();
  });

  it('save the updated passwords when both are valid', async () => {
    mockLegacyUpdatePassword.mockResolvedValue(
      createUpdatePasswordResponse({ responseCode: UpdatePasswordResponseCode.SUCCESS })
    );

    await renderPage();
    await userChangesPasswordWith(oldPassword, newPassword);

    expectNotificationAlertContainsTitle(
      TranslationKeys.CommonComponents.Notification.NEW_PASSWORD_SAVE_SUCCEEDED
    );
  });

  it('given the user types in the incorrect old password, after they hit save, then they will see the helper text error', async () => {
    const response: AxiosResponse = {
      data: { status: UpdatePasswordResponseCode.INVALID_CREDENTIALS },
      status: 500
    } as AxiosResponse;

    mockLegacyUpdatePassword.mockRejectedValue(
      new AxiosError('message', '500', undefined, undefined, response)
    );

    await renderPage();
    await userChangesPasswordWith();

    expect(
      screen.getByText(
        TranslationKeys.AccountPage.AccountInfo.Password.HelperText.INVALID_CREDENTIALS
      )
    ).toBeInTheDocument();
  });

  describe('given the user receives a validation error for the new password, they will see an error upon save', async () => {
    test.each([
      [
        UpdatePasswordResponseCode.NEW_PASSWORD_IS_OLD_PASSWORD,
        Tx.HelperText.NEW_PASSWORD_IS_OLD_PASSWORD
      ],
      [UpdatePasswordResponseCode.NEW_PASSWORD_IS_COMMON, Tx.HelperText.NEW_PASSWORD_IS_COMMON],
      [UpdatePasswordResponseCode.NEW_PASSWORD_IS_REUSED, Tx.HelperText.NEW_PASSWORD_IS_REUSED],
      [
        UpdatePasswordResponseCode.PASSWORD_FAILED_REQUIREMENTS,
        Tx.HelperText.PASSWORD_FAILED_REQUIREMENTS
      ],
      [
        UpdatePasswordResponseCode.NEW_PASSWORD_FAILED_REQUIREMENTS,
        Tx.HelperText.NEW_PASSWORD_FAILED_REQUIREMENTS
      ]
    ])('when %s response code should show %s message', async (responseCode, displayText) => {
      const response: AxiosResponse = {
        data: { status: responseCode },
        status: 500
      } as AxiosResponse;

      mockLegacyUpdatePassword.mockRejectedValue(
        new AxiosError('message', '500', undefined, undefined, response)
      );

      await renderPage();
      await userChangesPasswordWith();

      expect(within(views.password.baseView()).getByText(displayText)).toBeInTheDocument();
      expect(
        screen.getByText(TranslationKeys.CommonComponents.Notification.PASSWORD_NOT_SAVED)
      ).toBeInTheDocument();
    });
  });

  it('given an error occurs, displays generic failure', async () => {
    mockLegacyUpdatePassword.mockRejectedValue('Something went wrong.');

    await renderPage();
    await userChangesPasswordWith();

    expectNotificationAlertContainsTitle(
      TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE
    );
  });

  describe('displays alert when account issue occurs', () => {
    test.each([
      [
        UpdatePasswordResponseCode.ACCOUNT_NOT_CONVERTED,
        Tx.Notifications.Title.ACCOUNT_NOT_CONVERTED,
        Tx.Notifications.Message.ACCOUNT_NOT_CONVERTED
      ],
      [
        UpdatePasswordResponseCode.NO_ACCOUNT_FOUND,
        Tx.Notifications.Title.NO_ACCOUNT_FOUND,
        Tx.Notifications.Message.NO_ACCOUNT_FOUND
      ]
    ])('when %s occurs display %s and %s', async (responseCode, title, message) => {
      const response: AxiosResponse = {
        data: { status: responseCode },
        status: 500
      } as AxiosResponse;

      mockLegacyUpdatePassword.mockRejectedValue(
        new AxiosError('message', '500', undefined, undefined, response)
      );

      await renderPage();
      await userChangesPasswordWith();

      await waitFor(() => {
        expect(screen.getByRole('alert')).toHaveTextContent(title);
        expect(screen.getByRole('alert')).toHaveTextContent(message);
      });
    });
  });

  describe('new password validation on submit', () => {
    test.each([
      [4, '4'],
      [3, 'P'],
      [2, 'Pa'],
      [1, 'Password'],
      [0, 'Password!']
    ])(
      `should return %d error messages when inputting %s`,
      async (errorMessageCount, inputText) => {
        await renderPage();

        await act(async () => {
          await userEvent.click(views.password.editButton());
        });

        await act(async () => {
          await userEvent.type(views.password.currentInputField(), oldPassword);
          if (inputText != '') {
            await userEvent.type(views.password.newInputField(), inputText);
          }
        });

        await act(async () => {
          await userEvent.click(views.password.saveButton());
        });
        expect(screen.queryAllByTestId('error-message')).toHaveLength(errorMessageCount);
        // number of error messages below the base message
      }
    );

    it('given a customer enters an invalid new password and hits save, then they will see the helper text', async () => {
      await renderPage();

      await act(async () => {
        await userEvent.click(views.password.editButton());
      });

      await act(async () => {
        await userEvent.type(views.password.currentInputField(), oldPassword);
        await userEvent.type(views.password.newInputField(), 'notvalid');
      });

      expect(
        screen.queryByText(TranslationKeys.CommonComponents.Input.Error.Validation.BASE)
      ).toBeNull();

      await act(async () => {
        await userEvent.click(views.password.saveButton());
      });

      expect(
        screen.getByText(TranslationKeys.CommonComponents.Input.Error.Validation.BASE)
      ).toBeInTheDocument();
    });
  });
});
