import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FormHelperText, Grid, TextField } from '@mui/material';
import { Txt } from '../../../../../components/Txt';
import { Design } from '../../../../../helpers/Design';
import { ShowHideButton } from '../../../../../components/buttons/ShowHideButton';
import { TranslationKeys } from '../../../../../locales/TranslationKeys';

export type PasswordTextFieldProps = {
  hasAutoFocus: boolean;
  onChange: (password: string) => void;
  label: string;
  helperText?: React.ReactNode;
};
const Tx = TranslationKeys.AccountPage.AccountInfo.Password;

export const LegacyPasswordTextField: React.FC<PasswordTextFieldProps> = ({
  hasAutoFocus,
  onChange,
  label,
  helperText
}) => {
  const [dataVisible, setDataVisible] = useState<boolean>(false);
  const { t: translate } = useTranslation();
  const styles = passwordTextFieldStyles();

  return (
    <Grid>
      <Grid {...styles.fieldLabel}>
        <Txt {...styles.fieldLabelText} i18nKey={label} />
      </Grid>
      <Grid container {...styles.fieldValue}>
        <TextField
          data-testid={label}
          autoFocus={hasAutoFocus}
          InputProps={{
            endAdornment: (
              <ShowHideButton
                onClick={() => {
                  setDataVisible(!dataVisible);
                }}
                dataVisible={dataVisible}
                color={Design.Alias.Color.neutral600}
              />
            )
          }}
          fullWidth
          color="secondary"
          label={translate(Tx.Labels.VIEW_PASSWORD)}
          error={helperText !== undefined}
          onChange={(event) => {
            onChange(event.target.value);
          }}
          type={dataVisible ? 'text' : 'password'}
        />
        {helperText && (
          <FormHelperText component="div" {...styles.formHelperText}>
            {helperText ?? undefined}
          </FormHelperText>
        )}
      </Grid>
    </Grid>
  );
};
const passwordTextFieldStyles = () => ({
  fieldLabel: { sx: { flexDirection: 'row', marginBottom: Design.Primitives.Spacing.sm } },
  fieldLabelText: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.Md,
      color: Design.Alias.Color.neutral700
    }
  },
  fieldValue: {
    sx: {
      flexDirection: 'column',
      marginBottom: Design.Primitives.Spacing.sm
    }
  },
  fieldValueText: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.Lg,
      color: Design.Alias.Color.accent900,
      overflowWrap: 'break-word'
    }
  },
  formHelperText: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.Xs,
      color: Design.Primitives.Color.Semantic.error
    }
  }
});
