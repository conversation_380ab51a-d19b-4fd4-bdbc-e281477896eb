import React from 'react';
import { Grid } from '@mui/material';
import { TranslationKeys } from '../../../../../locales/TranslationKeys';
import { NEW_IDENTITY } from '../../../../../networkRequests/MyPodsConstants';
import {
  EditableAccountField,
  EditableAccountFieldProps
} from '../../../../AccountPage/AccountFields/EditableAccountField/EditableAccountField';
import { isValidPhone } from '../../../../../helpers/validation/Validators';
import { PhoneMask } from '../../../../../helpers/validation/PhoneMask';
import { UpdatePrimaryPhoneRequest } from '../../../../../networkRequests/responseEntities/CustomerEntities';
import { GtmAccountDetailType } from '../../../../../config/google/GoogleEntities';
import { useLegacyGetCustomer } from '../../../../../networkRequests/legacy/queries/useLegacyGetCustomer';
import { useLegacyUpdatePrimaryPhone } from '../../../../../networkRequests/legacy/mutations/useLegacyUpdatePrimaryPhone';
import { useAccountUpdateEvents } from '../../../../../config/useAccountUpdateEvents';

export const LegacyPrimaryPhoneNumberField = () => {
  const { customer, updateCustomer } = useLegacyGetCustomer();
  const { sendSubmitAccountDetailsEvent, sendEditAccountDetailsSuccessEvent } =
    useAccountUpdateEvents();
  const detailType: GtmAccountDetailType = 'phone_primary';
  const updatePrimaryPhone = useLegacyUpdatePrimaryPhone();

  const getRequest = (updateValue: string): UpdatePrimaryPhoneRequest => {
    const updatePhone = {
      id: customer.primaryPhone?.id ?? NEW_IDENTITY,
      number: updateValue
    };
    return { phone: updatePhone };
  };

  const onSuccessfulUpdate = (request: UpdatePrimaryPhoneRequest): void => {
    const updatedCustomer = { ...customer };
    if (request.phone != null) updatedCustomer.primaryPhone = request.phone;
    updateCustomer(updatedCustomer);
  };

  const displayValue = () => customer.primaryPhone?.number ?? 'N/A';

  const update = (value: string, onSuccess: () => void, onError: () => void) => {
    const updateRequest = getRequest(value);
    sendSubmitAccountDetailsEvent(detailType);
    updatePrimaryPhone.mutate(updateRequest, {
      onSuccess: (_, request) => {
        sendEditAccountDetailsSuccessEvent(detailType);
        onSuccessfulUpdate(request);
        onSuccess();
      },
      onError: () => {
        onError();
      }
    });
  };

  const getEditableFieldProps = (): EditableAccountFieldProps => ({
    displayValue: displayValue(),
    isInputValid: isValidPhone,
    update,
    isPendingUpdate: updatePrimaryPhone.isPending,
    labelKey: TranslationKeys.AccountPage.ContactInfo.Labels.PRIMARY_PHONE,
    gtmDetailType: detailType,
    validationErrorKey: TranslationKeys.CommonComponents.Input.Error.INVALID_PHONE,
    successNotificationKey:
      TranslationKeys.CommonComponents.Notification.PRIMARY_PHONE_NUMBER_SAVE_SUCCEEDED,
    textFieldOverrides: {
      InputProps: {
        inputComponent: PhoneMask as any
      },
      type: 'tel'
    }
  });
  return (
    <Grid data-testid="primary-phone-field">
      <EditableAccountField {...getEditableFieldProps()} />
    </Grid>
  );
};
