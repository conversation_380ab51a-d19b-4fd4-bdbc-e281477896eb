import React from 'react';
import { Grid } from '@mui/material';
import { TranslationKeys } from '../../../../../locales/TranslationKeys';
import { NEW_IDENTITY } from '../../../../../networkRequests/MyPodsConstants';
import {
  EditableAccountField,
  EditableAccountFieldProps
} from '../../../../AccountPage/AccountFields/EditableAccountField/EditableAccountField';
import { isValidPhone } from '../../../../../helpers/validation/Validators';
import { PhoneMask } from '../../../../../helpers/validation/PhoneMask';
import { UpdateSecondaryPhoneRequest } from '../../../../../networkRequests/responseEntities/CustomerEntities';
import { GtmAccountDetailType } from '../../../../../config/google/GoogleEntities';
import { useLegacyGetCustomer } from '../../../../../networkRequests/legacy/queries/useLegacyGetCustomer';
import { useLegacyUpdateSecondaryPhone } from '../../../../../networkRequests/legacy/mutations/useLegacyUpdateSecondaryPhone';
import { useAccountUpdateEvents } from '../../../../../config/useAccountUpdateEvents';

export const LegacySecondaryPhoneNumberField = () => {
  const { customer, updateCustomer } = useLegacyGetCustomer();
  const { sendSubmitAccountDetailsEvent, sendEditAccountDetailsSuccessEvent } =
    useAccountUpdateEvents();
  const detailType: GtmAccountDetailType = 'phone_secondary';
  const updateSecondaryPhone = useLegacyUpdateSecondaryPhone();

  const getRequest = (updateValue: string): UpdateSecondaryPhoneRequest => {
    const updatePhone = {
      id: customer.secondaryPhone?.id ?? NEW_IDENTITY,
      number: updateValue
    };
    return { phone: updatePhone };
  };

  const displayValue = () => customer.secondaryPhone?.number ?? 'N/A';

  const onSuccessfulUpdate = (request: UpdateSecondaryPhoneRequest): void => {
    if (request.phone != null) updateCustomer({ secondaryPhone: request.phone });
  };

  const update = (value: string, onSuccess: () => void, onError: () => void) => {
    const updateRequest = getRequest(value);
    sendSubmitAccountDetailsEvent(detailType);
    updateSecondaryPhone.mutate(updateRequest, {
      onSuccess: (_, request) => {
        sendEditAccountDetailsSuccessEvent(detailType);
        onSuccessfulUpdate(request);
        onSuccess();
      },
      onError: () => {
        onError();
      }
    });
  };

  const getEditableFieldProps = (): EditableAccountFieldProps => ({
    displayValue: displayValue(),
    update,
    isPendingUpdate: updateSecondaryPhone.isPending,
    isInputValid: isValidPhone,
    gtmDetailType: detailType,
    labelKey: TranslationKeys.AccountPage.ContactInfo.Labels.SECONDARY_PHONE,
    validationErrorKey: TranslationKeys.CommonComponents.Input.Error.INVALID_PHONE,
    successNotificationKey:
      TranslationKeys.CommonComponents.Notification.SECONDARY_PHONE_NUMBER_SAVE_SUCCEEDED,
    textFieldOverrides: {
      InputProps: {
        inputComponent: PhoneMask as any
      },
      type: 'tel'
    }
  });

  return (
    <Grid data-testid="secondary-phone-field">
      <EditableAccountField {...getEditableFieldProps()} />
    </Grid>
  );
};
