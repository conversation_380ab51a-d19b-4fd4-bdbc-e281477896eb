import { LegacyAccountPage } from '../LegacyAccountPage';
import { screen, waitFor, within } from '@testing-library/react';
import React, { act } from 'react';
import {
  expectCustomerContextContains,
  LegacyCustomerContextViewer,
  renderWithLegacyProvidersAndState,
  runPendingPromises
} from '../../../../testUtils/RenderHelpers';
import {
  createCustomer,
  createCustomerAddress,
  createEmail,
  createEmptyCustomerAddress,
  createPhone,
  createRefreshSessionClaims,
  createUseFeatureFlagResult
} from '../../../../testUtils/MyPodsFactories';
import {
  Customer,
  CustomerAddress,
  formatAddress,
  UpdateBillingAddressRequest,
  UpdatePinRequest,
  UpdateSecondaryPhoneRequest,
  UpdateShippingAddressRequest,
  UpdateSmsOptInRequest
} from '../../../../networkRequests/responseEntities/CustomerEntities';
import {
  accountPageActions as actions,
  legacyAccountPageViews as views
} from './LegacyAccountPageViews';
import userEvent from '@testing-library/user-event';
import {
  BILLING_ADDRESS_TYPE,
  NEW_IDENTITY,
  US_REGION_CODE
} from '../../../../networkRequests/MyPodsConstants';
import {
  mockedUseFeatureFlags,
  mockLegacyGetCustomer,
  mockLegacyUpdateBillingAddress,
  mockLegacyUpdatePin,
  mockLegacyUpdateSecondaryPhone,
  mockLegacyUpdateShippingAddress,
  mockLegacyUpdateSmsOptIn,
  mockRefreshSession
} from '../../../../../setupTests';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import { accountPageViews } from '../../../AccountPage/__tests__/AccountPageViews';
import {
  DASH_ICON_ID,
  GREEN_CHECKMARK_ICON_ID,
  RED_X_ICON_ID
} from '../../../../components/icons/GreenCheckmarkIcon';
import { expectNotificationAlertContainsTitle } from '../../../../testUtils/assertions';

describe('LegacyAccountPage', () => {
  const customerId = '*********';
  const customer = createCustomer({
    id: customerId,
    email: createEmail({ address: '<EMAIL>' }),
    primaryPhone: createPhone({ number: '************' }),
    secondaryPhone: createPhone({ number: '************' }),
    billingAddress: createCustomerAddress({ address1: '123 Maple Ave' }),
    shippingAddress: createCustomerAddress({ address1: '321 Oak Blvd' }),
    smsOptIn: true,
    securityQuestionAnswer: {
      question: 'What is your favorite state fair?',
      answer: 'Hometown, USA'
    }
  });

  const renderPage = async (initialCustomer: Customer = customer) => {
    const result = renderWithLegacyProvidersAndState(
      <>
        <LegacyAccountPage />
        <LegacyCustomerContextViewer />
      </>,
      { initialCustomer: initialCustomer }
    );
    await runPendingPromises();
    return result;
  };

  beforeEach(() => {
    mockRefreshSession.mockResolvedValue(createRefreshSessionClaims());
    mockedUseFeatureFlags.useFeatureFlags.mockImplementation(() => createUseFeatureFlagResult());
  });

  describe('Account Info', () => {
    it('should display first and last name', async () => {
      await renderPage();

      const fullName = customer.firstName + ' ' + customer.lastName;
      expect(await screen.findByText(fullName)).toBeInTheDocument();
    });

    it('should render the password section if the customer is converted', async () => {
      await renderPage();

      expect(within(await views.accountInfo.section()).getByText('********')).toBeInTheDocument();
    });

    it('should display email', async () => {
      await renderPage();

      expect(
        within(await views.accountInfo.section()).getByText(customer.email!.address!)
      ).toBeInTheDocument();
    });
  });

  describe('Contact Info', () => {
    it('should display primary phone number', async () => {
      await renderPage();

      expect(await screen.findByText(customer.primaryPhone!.number!));
    });

    it('should display secondary phone number', async () => {
      await renderPage();

      expect(await screen.findByText(customer.secondaryPhone!.number!));
    });

    it('should display N/A when phone number not provided', async () => {
      const customerWithoutSecondaryPhone = { ...customer, secondaryPhone: undefined };
      mockLegacyGetCustomer.mockResolvedValue(customerWithoutSecondaryPhone);

      await renderPage(customerWithoutSecondaryPhone);

      expect(within(await views.contactInfo.section()).getByText('N/A')).toBeInTheDocument();
    });

    it('should update primary phone number', async () => {
      // See EditableAccountField tests;
      return true;
    });

    it('should update secondary phone number', async () => {
      let newPhoneNumber = '************';

      await renderPage();
      await actions.updateSecondaryPhoneNumber(newPhoneNumber);

      await act(async () => {
        await userEvent.click(views.secondaryPhone.saveButton());
      });

      const expectedRequest: UpdateSecondaryPhoneRequest = {
        phone: { id: customer.secondaryPhone!.id, number: newPhoneNumber }
      };
      expect(views.secondaryPhone.accountField()).toBeInTheDocument(); // Restore the display view
      expect(mockLegacyUpdateSecondaryPhone).toHaveBeenCalledWith(expectedRequest);
      expect(screen.getByText(newPhoneNumber)).toBeInTheDocument();
      await expectCustomerContextContains(newPhoneNumber);
    });
  });

  describe('Communication', () => {
    it('should display enabled switch when sms opt in is true', async () => {
      await renderPage();
      await waitFor(() => views.smsPreferences.switch());

      expect(views.smsPreferences.switch()).toHaveProperty('checked', true);
    });

    it('should update sms opt in and toggle switch when switch is clicked', async () => {
      await renderPage();
      await waitFor(() => views.smsPreferences.switch());

      await act(async () => {
        await userEvent.click(views.smsPreferences.switch());
      });

      const expectedRequest: UpdateSmsOptInRequest = {
        lastName: customer.lastName,
        primaryPhone: customer.primaryPhone,
        secondaryPhone: customer.secondaryPhone,
        newSmsOptIn: false
      };
      expect(mockLegacyUpdateSmsOptIn).toHaveBeenCalledWith(expectedRequest);
      expect(views.smsPreferences.switch()).toHaveProperty('checked', false);
      await expectCustomerContextContains('"smsOptIn":false');
    });
  });

  describe('Address Info', () => {
    it('should format and render the billing address', async () => {
      await renderPage();

      expect(await screen.findByText(formatAddress(customer.billingAddress))).toBeInTheDocument();
    });

    it('should display N/A shipping address when missing', async () => {
      let customerWithNoShippingAddress: Customer = { ...customer, shippingAddress: undefined };

      await renderPage(customerWithNoShippingAddress);

      expect(within(await views.addressInfo.section()).getByText('N/A')).toBeInTheDocument();
    });

    it('should update billing address', async () => {
      const updatedAddress: CustomerAddress = {
        ...customer.billingAddress!!,
        address1: '123 State St'
      };

      await renderPage();
      await actions.updateBillingAddress(updatedAddress);
      await act(async () => {
        await userEvent.click(views.billingAddress.saveButton());
      });

      const expectedRequest: UpdateBillingAddressRequest = { address: updatedAddress };
      expect(views.billingAddress.accountField()).toBeInTheDocument(); // Restore the display view
      expect(mockLegacyUpdateBillingAddress).toHaveBeenCalledWith(expectedRequest);
      expect(screen.getByText(formatAddress(updatedAddress))).toBeInTheDocument();
      await expectCustomerContextContains(updatedAddress.address1!!);
    });

    it('should update shipping address', async () => {
      const updatedAddress: CustomerAddress = {
        ...customer.billingAddress!!,
        address1: '123 Madison Rd'
      };

      await renderPage();
      await actions.updateShippingAddress(updatedAddress.address1!!);

      await act(async () => {
        await userEvent.click(views.shippingAddress.saveButton());
      });

      const expectedRequest: UpdateShippingAddressRequest = { address: updatedAddress };
      expect(views.shippingAddress.accountField()).toBeInTheDocument(); // Restore the display view
      expect(mockLegacyUpdateShippingAddress).toHaveBeenCalledWith(expectedRequest);
      expect(screen.getByText(formatAddress(updatedAddress))).toBeInTheDocument();
      await expectCustomerContextContains(updatedAddress.address1!!);
    });

    it('should update billing address when current is null', async () => {
      const updatedAddress: CustomerAddress = {
        ...customer.billingAddress!!,
        address1: '123 State St'
      };

      const emptyBillingAddress: CustomerAddress = createEmptyCustomerAddress({
        addressType: BILLING_ADDRESS_TYPE
      });
      let customerWithNoBillingAddress: Customer = {
        ...customer,
        billingAddress: emptyBillingAddress
      };

      await renderPage(customerWithNoBillingAddress);
      await actions.updateBillingAddress(updatedAddress);
      await act(async () => {
        await userEvent.click(views.billingAddress.saveButton());
      });

      const expectedRequest: UpdateBillingAddressRequest = {
        address: {
          ...updatedAddress,
          id: NEW_IDENTITY,
          address2: '',
          addressType: BILLING_ADDRESS_TYPE,
          regionCode: US_REGION_CODE
        }
      };
      expect(mockLegacyUpdateBillingAddress).toHaveBeenCalledWith(expectedRequest);
    });
  });

  describe('Support Info', () => {
    it('should render pin and security question and answer', async () => {
      await renderPage();

      expect(
        screen.getByRole('heading', {
          name: TranslationKeys.AccountPage.SupportInfo.HEADER
        })
      ).toBeInTheDocument();
      expect(within(await views.supportInfo.section()).getByText('****')).toBeInTheDocument();
      expect(
        within(await views.supportInfo.section()).getAllByText('************************')
      ).toHaveLength(2);
    });

    it('should prevent users from entering non-numeric characters', async () => {
      await renderPage();
      await act(async () => {
        await userEvent.click(views.pin.editButton());
      });
      await act(async () => {
        await userEvent.type(views.pin.inputField(), 'abc1');
      });

      expect(views.pin.inputField()).toHaveDisplayValue('1');
    });

    it('should allow users to update pin', async () => {
      const expectedPin = '1234';
      const expectedRequest: UpdatePinRequest = {
        pin: expectedPin,
        securityAnswer: customer.securityQuestionAnswer?.answer!!
      };

      await renderPage();

      const supportSection = await views.supportInfo.section();

      const editPinButton = accountPageViews.editButtonWithin(supportSection);
      expect(editPinButton).toBeInTheDocument();

      await act(async () => {
        await userEvent.click(editPinButton);
      });

      const pinInput = within(supportSection).getByLabelText(
        TranslationKeys.AccountPage.SupportInfo.Labels.SUPPORT_PIN
      );
      expect(within(supportSection).getByTestId(DASH_ICON_ID)).toBeInTheDocument();
      expect(within(supportSection).queryByTestId(GREEN_CHECKMARK_ICON_ID)).not.toBeInTheDocument();
      expect(pinInput).toHaveValue('');

      await act(async () => {
        await userEvent.type(pinInput, expectedPin);
      });

      expect(within(supportSection).getByTestId(GREEN_CHECKMARK_ICON_ID)).toBeInTheDocument();
      await act(async () => {
        await userEvent.click(accountPageViews.saveButtonWithin(supportSection));
      });
      expect(mockLegacyUpdatePin).toHaveBeenCalledWith(expectedRequest);
      expect(within(await views.supportInfo.section()).getByText('****')).toBeInTheDocument();

      expectNotificationAlertContainsTitle(
        TranslationKeys.CommonComponents.Notification.PIN_SAVE_SUCCEEDED
      );
    });

    it('should provide realtime error feedback while editing pin', async () => {
      await renderPage();
      const pinWrapper = views.pin.baseField();
      const editPinButton = views.pin.editButton();

      expect(editPinButton).toBeInTheDocument();
      await act(async () => {
        await userEvent.click(editPinButton);
      });

      const pinInput = views.pin.inputField();
      expect(within(pinWrapper).getByText('Contain 4 numbers')).toBeInTheDocument();
      expect(within(pinWrapper).getByTestId(DASH_ICON_ID)).toBeInTheDocument();
      expect(within(pinWrapper).queryByTestId(GREEN_CHECKMARK_ICON_ID)).not.toBeInTheDocument();
      expect(within(pinWrapper).queryByTestId(RED_X_ICON_ID)).not.toBeInTheDocument();

      expect(pinInput).toHaveValue('');

      await act(async () => {
        await userEvent.type(pinInput, '1');
      });

      expect(within(pinWrapper).queryByTestId(DASH_ICON_ID)).not.toBeInTheDocument();
      expect(within(pinWrapper).queryByTestId(GREEN_CHECKMARK_ICON_ID)).not.toBeInTheDocument();
      expect(within(pinWrapper).getByTestId(RED_X_ICON_ID)).toBeInTheDocument();
    });

    it('should clear helper text when modifying input after shown', async () => {
      await renderPage();

      const pinWrapper = views.pin.baseField();

      await act(async () => {
        await userEvent.click(views.pin.editButton());
      });

      const pinInput = views.pin.inputField();

      await act(async () => {
        await userEvent.type(pinInput, '1');
        await userEvent.click(views.pin.saveButton());
      });

      expect(
        within(pinWrapper).getByText(TranslationKeys.CommonComponents.Input.Error.Validation.BASE)
      ).toBeInTheDocument();
      expect(
        within(pinWrapper).getByText(TranslationKeys.CommonComponents.Input.Error.Validation.PIN)
      ).toBeInTheDocument();

      await act(async () => {
        await userEvent.type(pinInput, '1');
      });

      expect(
        within(pinWrapper).queryByText(TranslationKeys.CommonComponents.Input.Error.Validation.BASE)
      ).not.toBeInTheDocument();
    });

    it('should allow users to show and hide their pin while editing', async () => {
      await renderPage();

      await act(async () => {
        await userEvent.click(views.pin.editButton());
      });

      const pinInput = views.pin.inputField();

      expect(pinInput).toHaveAttribute('type', 'password');

      await act(async () => {
        await userEvent.click(views.pin.showHideButton());
      });

      expect(pinInput).toHaveAttribute('type', 'text');
    });

    it('should not update pin when user cancels edit pin', async () => {
      await renderPage();

      await act(async () => {
        await userEvent.click(views.pin.editButton());
      });
      await act(async () => {
        await userEvent.type(views.pin.inputField(), '123');
        await userEvent.click(views.pin.cancelButton());
      });

      expect(within(views.pin.baseField()).getByText('****')).toBeInTheDocument();
      expect(mockLegacyUpdatePin).not.toHaveBeenCalled();
    });
  });
});
