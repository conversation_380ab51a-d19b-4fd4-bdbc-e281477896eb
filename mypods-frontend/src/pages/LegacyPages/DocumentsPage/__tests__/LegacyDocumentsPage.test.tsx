import {
  renderWithLegacyProvidersAndState,
  runPendingPromises
} from '../../../../testUtils/RenderHelpers';
import { LegacyDocumentsPage } from '../LegacyDocumentsPage';
import { screen } from '@testing-library/react';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import {
  createAPIDocuments,
  createBillingInformation,
  createCustomer,
  createRefreshSessionClaims
} from '../../../../testUtils/MyPodsFactories';
import {
  mockGetBillingInformation,
  mockLegacyGetCustomer,
  mockLegacyGetDocuments,
  mockRefreshSession,
  mockSendSplitEvent
} from '../../../../../setupTests';

describe('Legacy Document Page', () => {
  const documents = createAPIDocuments();

  beforeEach(() => {
    mockLegacyGetCustomer.mockResolvedValue(createCustomer());
    mockRefreshSession.mockResolvedValue(createRefreshSessionClaims());
    mockGetBillingInformation.mockResolvedValue(createBillingInformation());
    mockLegacyGetDocuments.mockResolvedValue(documents);
    mockSendSplitEvent.mockResolvedValue({});
  });

  it('should render documents page', async () => {
    renderWithLegacyProvidersAndState(<LegacyDocumentsPage />);
    await runPendingPromises();

    expect(screen.getByText(TranslationKeys.DocumentsPage.HEADER)).toBeInTheDocument();
    expect(screen.getByText(TranslationKeys.DocumentsPage.SUBTITLE)).toBeInTheDocument();
    expect(await screen.findByText(`#${documents.documents[0].orderId}`)).toBeInTheDocument();
  });
});
