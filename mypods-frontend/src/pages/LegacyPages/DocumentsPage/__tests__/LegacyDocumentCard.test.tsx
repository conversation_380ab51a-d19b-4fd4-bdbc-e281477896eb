import React, { act } from 'react';
import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { createDocument } from '../../../../testUtils/MyPodsFactories';
import { LegacyDocumentCard } from '../LegacyDocumentCard';
import { DocumentDescription, IDocument } from '../../../../domain/DocumentEntities';
import {
  mockLegacyGetFile,
  mockLegacyGetRentalAgreement,
  mockRefreshSession
} from '../../../../../setupTests';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import {
  renderWithLegacyProvidersAndState,
  runPendingPromises
} from '../../../../testUtils/RenderHelpers';
import { DOCUMENT_TITLES, SIGNABLE_DOCUMENTS } from '../../../../helpers/Documents';
import { vi } from 'vitest';
import FileSaver from 'file-saver';
import { AxiosError, AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import { createSession } from 'react-router';

const saveAsSpy = vi.spyOn(FileSaver, 'saveAs');

describe('DocumentCard', () => {
  const user = userEvent.setup();
  const orderConfirmationDoc = createDocument();
  const renderCardForDocument = async (document: IDocument = orderConfirmationDoc) => {
    const result = renderWithLegacyProvidersAndState(<LegacyDocumentCard document={document} />);
    await runPendingPromises();
    return result;
  };

  const actions = {
    openDocument: async () => {
      await act(async () => await user.click(screen.getByRole('button')));
    }
  };

  beforeEach(() => {
    mockRefreshSession.mockResolvedValue(createSession());
  });

  it('displays the title & subtitle correctly', async () => {
    await renderCardForDocument();

    screen.getByText(orderConfirmationDoc.title);
    screen.getByText(`#${orderConfirmationDoc.orderId}`);
  });

  it.each(SIGNABLE_DOCUMENTS)(
    'displays the green checkmark for signed %s document',
    async (document) => {
      const signableDocument = createDocument({ title: document as DocumentDescription });
      await renderCardForDocument(signableDocument);
      screen.getByTestId('checkmark-icon');
    }
  );

  const unsignableDocuments = DOCUMENT_TITLES.filter(
    (title) => !SIGNABLE_DOCUMENTS.includes(title)
  );
  it.each(unsignableDocuments)(
    'displays the grey document for non-signable %s document',
    async (document) => {
      const unsignableDocument = createDocument({ title: document as DocumentDescription });
      await renderCardForDocument(unsignableDocument);
      screen.getByTestId('document-icon');
    }
  );

  describe('download links', () => {
    let expectedUrl = 'blob://file.pdf';

    it('should open a document in a new tab for an Order Confirmation', async () => {
      mockLegacyGetFile.mockResolvedValue(expectedUrl);
      await renderCardForDocument();

      await actions.openDocument();

      expect(mockLegacyGetFile).toHaveBeenCalledWith(orderConfirmationDoc.id, false);
      expect(saveAsSpy).toHaveBeenCalledWith(
        expectedUrl,
        `${orderConfirmationDoc.title} #${orderConfirmationDoc.orderId}.pdf`
      );
    });

    it('should open a document in a new tab for a Rental Agreement', async () => {
      mockLegacyGetRentalAgreement.mockResolvedValue(expectedUrl);
      const rentalAgreement = createDocument({ title: 'Local Rental Agreement' });
      await renderCardForDocument(rentalAgreement);

      await actions.openDocument();

      expect(mockLegacyGetRentalAgreement).toHaveBeenCalledWith(rentalAgreement.companyCode);
      expect(saveAsSpy).toHaveBeenCalledWith(
        expectedUrl,
        `${rentalAgreement.title} #${rentalAgreement.orderId}.pdf`
      );
    });

    it('should display an error notification if a document fails to open', async () => {
      mockLegacyGetFile.mockRejectedValue({});
      await renderCardForDocument();

      await actions.openDocument();
      expect(screen.getByRole('alert')).toHaveTextContent(
        TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE
      );
    });

    it('should display a NOT_FOUND error notification if a document is not found', async () => {
      const mockAxiosResponse: AxiosResponse<unknown, any> = {
        data: 'Resource not found', // or use undefined if you want no data
        status: 404,
        statusText: 'Not Found',
        headers: {},
        config: {} as InternalAxiosRequestConfig<any>
      };
      mockLegacyGetFile.mockRejectedValue(
        new AxiosError(
          'Request failed with status code 404',
          '404',
          undefined,
          undefined,
          mockAxiosResponse
        )
      );
      await renderCardForDocument();

      await actions.openDocument();
      expect(screen.getByRole('alert')).toHaveTextContent(
        TranslationKeys.CommonComponents.Notification.DOCUMENT_NOT_FOUND
      );
    });

    it('should display an error notification if documentId is zero', async () => {
      await renderCardForDocument(createDocument({ id: '0' }));

      await actions.openDocument();
      expect(mockLegacyGetFile).not.toHaveBeenCalled();
      expect(screen.getByRole('alert')).toHaveTextContent(
        TranslationKeys.CommonComponents.Notification.FILE_NOT_READY_ERROR
      );
    });
  });
});
