import userEvent, { UserEvent } from '@testing-library/user-event';
import { vi } from 'vitest';
import {
  renderWithLegacyProvidersAndState,
  runPendingPromises,
  testQueryClient
} from '../../../../testUtils/RenderHelpers';
import { LegacyMothInspectionVariantSelector } from '../LegacyMothInspectionVariantSelector';
import { screen, waitFor } from '@testing-library/react';
import {
  mockedUseFeatureFlags,
  mockLegacyGetCustomer,
  mockLegacyGetCustomerOrders,
  mockRefreshSession,
  mockLegacySignMothAgreement,
  mockNavigate,
  mockScrollIntoView
} from '../../../../../setupTests';
import {
  createContainer,
  createContainerApi,
  createCustomer,
  createEntryPointResult,
  createMoveLeg,
  createMoveLegAddress,
  createMoveLegApi,
  createOrder,
  createOrderApi,
  createOutstandingMothAgreement,
  createOutstandingRentalAgreement,
  createRefreshSessionClaims,
  createSignMothAgreementRequest,
  createUseFeatureFlagResult
} from '../../../../testUtils/MyPodsFactories';
import {
  Customer,
  formatAddress
} from '../../../../networkRequests/responseEntities/CustomerEntities';
import { initialEntryPointState } from '../../../../context/EntryPointContext';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import { OrderAPI } from '../../../../networkRequests/responseEntities/OrderAPIEntities';
import { LegacyQueryCacheKeys } from '../../../../networkRequests/QueryCacheKeys';
import {
  EntryPointResult,
  RefreshSessionClaims
} from '../../../../networkRequests/responseEntities/AuthorizationEntities';
import {
  getOrdersWithSignedMothAgreements,
  setOrdersWithSignedMothAgreements
} from '../../../../helpers/storageHelpers';
import * as exports from '../../../MothFlyInspectionFormPage/mothInspectionConfig';
import { SignMothAgreementRequest } from '../../../../networkRequests/responseEntities/DocumentApiEntities';
import { expectNotificationAlertContainsTitle } from '../../../../testUtils/assertions';
import { ROUTES } from '../../../../Routes';
import { PodsReadyRoutes } from '../../../../PodsReadyRoutes';
import { SplitEventType } from '../../../../config/SplitEventTypes';

const mockUseLocation = vi.hoisted(() => vi.fn());
vi.mock('react-router', async () => ({
  ...((await vi.importActual('react-router')) as any),
  useNavigate: () => mockNavigate,
  useLocation: mockUseLocation
}));

const mockSendSplitEvent = vi.hoisted(() => vi.fn());
vi.mock('../../../../config/useSplitEvents', async () => ({
  useSplitEvents: () => ({
    send: mockSendSplitEvent
  })
}));

const mothActions = (user: UserEvent) => ({
  submitForm: async () => {
    const agreeButton = screen.getByRole('button', {
      name: TranslationKeys.MothInspectionFormPage.AGREE_BUTTON
    });
    const addSignatureButton = screen.getByText(
      TranslationKeys.CommonComponents.AddSignature.BUTTON_TEXT
    );

    await waitFor(async () => user.click(addSignatureButton));
    await waitFor(async () => user.click(agreeButton));
  },
  fillForm: async () => {
    const bikeCheckbox = await screen.findByLabelText('Bicycles, Tricycles, Scooters');
    const sandboxCheckbox = await screen.findByLabelText('Sandboxes');
    await waitFor(() => user.click(sandboxCheckbox));
    await waitFor(() => user.click(bikeCheckbox));
  }
});

describe('LegacyMothInspectionVariantSelector', () => {
  let initialCustomer: RefreshSessionClaims;
  let user: UserEvent;
  let customer: Customer;
  let actions: { submitForm: () => Promise<void>; fillForm: () => Promise<void> };
  let orderId: string;
  let customerOrder: OrderAPI;
  let entryPointResult: EntryPointResult;

  const configureMockCustomer = () => {
    user = userEvent.setup();
    actions = mothActions(user);
    customer = createCustomer();
    mockLegacyGetCustomer.mockResolvedValue(customer);
    initialCustomer = createRefreshSessionClaims();
    mockRefreshSession.mockResolvedValue(initialCustomer);
    testQueryClient().setQueryData([LegacyQueryCacheKeys.LEGACY_CUSTOMER_CACHE_KEY], customer);
  };

  const configureMockOrderWithDocuments = () => {
    orderId = '12354';
    customerOrder = { ...createOrderApi({ orderId: orderId }) };
    mockLegacyGetCustomerOrders.mockResolvedValue([customerOrder]);

    setOrdersWithSignedMothAgreements([]);
    const mothAgreement = createOutstandingMothAgreement({ orderId: '12354' });
    entryPointResult = createEntryPointResult({
      outstandingMothAgreements: [mothAgreement]
    });
    mockLegacySignMothAgreement.mockResolvedValue(
      createSignMothAgreementRequest(customer, { orderId })
    );
  };
  beforeEach(() => {
    configureMockCustomer();
    configureMockOrderWithDocuments();
    mockUseLocation.mockReturnValue({ pathname: '/' });
  });

  afterEach(() => {
    setOrdersWithSignedMothAgreements([]);
    vi.resetAllMocks();
  });

  const renderSubject = async () => {
    const result = renderWithLegacyProvidersAndState(<LegacyMothInspectionVariantSelector />, {
      entryPointState: {
        ...initialEntryPointState,
        entryPointResult: entryPointResult
      },
      initialEntries: ['/']
    });
    await runPendingPromises();
    return result;
  };

  describe('isMothPagination feature flag is enabled', async () => {
    beforeEach(() => {
      mockedUseFeatureFlags.useFeatureFlags.mockImplementation(() =>
        createUseFeatureFlagResult({
          isPoetEnabled: () => true,
          isMothPaginationEnabled: () => true
        })
      );

      const mockConfig = [
        {
          titleKey: 'Recreational, Child Play things',
          subcategories: [
            {
              subtitleKey: "Children's Playthings",
              checkboxes: [
                {
                  id: 'bicycles-tricycles-scooters',
                  label: 'Foo',
                  pdfKey: 'Bicycles Tricycles Scooters',
                  checked: false,
                  restricted: true
                }
              ]
            }
          ]
        }
      ];

      vi.spyOn(exports, 'mothFormConfig', 'get').mockReturnValue(mockConfig);
    });

    it('should display paginated version when paginated-moth-form flag is on', async () => {
      mockedUseFeatureFlags.useFeatureFlags.mockImplementation(() =>
        createUseFeatureFlagResult({ isMothPaginationEnabled: () => true })
      );
      await renderSubject();

      expect(
        await screen.findByText(TranslationKeys.MothInspectionFormPage.INTRO_DESC)
      ).toBeInTheDocument();
    });

    it('should have previous button when next button clicked', async () => {
      await renderSubject();
      const button = await screen.findByRole('button', {
        name: TranslationKeys.CommonComponents.NEXT_BUTTON
      });

      await waitFor(() => user.click(button));

      const preButton = await screen.findByRole('button', {
        name: TranslationKeys.CommonComponents.PREVIOUS_BUTTON
      });
      expect(preButton).toBeInTheDocument();
    });

    it('should scroll to top when next button clicked', async () => {
      await renderSubject();
      const button = await screen.findByRole('button', {
        name: TranslationKeys.CommonComponents.NEXT_BUTTON
      });

      await waitFor(() => user.click(button));

      expect(mockScrollIntoView).toHaveBeenCalledTimes(2); // initial render + click next
    });

    it('when user clicks sign, enable agree button', async () => {
      await renderSubject();
      const button = await screen.findByRole('button', {
        name: TranslationKeys.CommonComponents.NEXT_BUTTON
      });
      await waitFor(() => user.click(button));
      const agreeButton = await screen.findByRole('button', {
        name: TranslationKeys.MothInspectionFormPage.AGREE_BUTTON
      });
      const addSignatureButton = await screen.findByText(
        TranslationKeys.CommonComponents.AddSignature.BUTTON_TEXT
      );
      expect(agreeButton).toBeDisabled();

      await waitFor(() => user.click(addSignatureButton));

      expect(agreeButton).toBeEnabled();
    });

    it('when a restricted checkbox is selected, the form should show restricted item error message ', async () => {
      await renderSubject();
      const bikeCheckbox = await screen.findByLabelText('Foo');

      await waitFor(() => user.click(bikeCheckbox));

      const restrictedTitleButton = await screen.findByText(
        TranslationKeys.MothInspectionFormPage.RESTRICTED_ITEM_MESSAGE
      );
      expect(restrictedTitleButton).toBeInTheDocument();
    });

    it('when user is on the last page, the Agree button should show and the Next button should not be present', async () => {
      await renderSubject();
      const button = await screen.findByRole('button', {
        name: TranslationKeys.CommonComponents.NEXT_BUTTON
      });

      await waitFor(() => user.click(button));

      const agreeButton = await screen.findByRole('button', {
        name: TranslationKeys.MothInspectionFormPage.AGREE_BUTTON
      });
      const nextButton = screen.queryByRole('button', {
        name: TranslationKeys.CommonComponents.NEXT_BUTTON
      });
      expect(agreeButton).toBeInTheDocument();
      expect(nextButton).not.toBeInTheDocument();
    });
  });

  describe('isMothPagination feature flag is disabled', async () => {
    beforeEach(() => {
      mockedUseFeatureFlags.useFeatureFlags.mockImplementation(() =>
        createUseFeatureFlagResult({ isMothPaginationEnabled: () => false })
      );
    });
    it('should show the non-paginated Moth inspection form', async () => {
      await renderSubject();

      const nonPaginated = screen.getByTestId('moth-form-non-paginated');

      expect(nonPaginated).toBeInTheDocument();
    });

    it('should navigate back when back button clicked', async () => {
      await renderSubject();
      const backBtn = await screen.findByTestId('back-link');

      await waitFor(() => user.click(backBtn));

      expect(mockNavigate).toHaveBeenCalledWith(-1);
    });

    it('should navigate back when previous button clicked', async () => {
      await renderSubject();

      const nextBtn = await screen.findByRole('button', {
        name: TranslationKeys.CommonComponents.NEXT_BUTTON
      });
      await waitFor(() => user.click(nextBtn));

      const button = await screen.findByRole('button', {
        name: TranslationKeys.CommonComponents.PREVIOUS_BUTTON
      });

      await waitFor(() => user.click(button));
      expect(mockNavigate).toHaveBeenCalledWith(-1);
    });

    it('when user clicks sign, enable agree button', async () => {
      await renderSubject();

      const nextBtn = await screen.findByRole('button', {
        name: TranslationKeys.CommonComponents.NEXT_BUTTON
      });
      await waitFor(() => user.click(nextBtn));
      const agreeButton = await screen.findByRole('button', {
        name: TranslationKeys.MothInspectionFormPage.AGREE_BUTTON
      });
      const addSignatureButton = await screen.findByText(
        TranslationKeys.CommonComponents.AddSignature.BUTTON_TEXT
      );
      expect(agreeButton).toBeDisabled();

      await waitFor(() => user.click(addSignatureButton));

      expect(agreeButton).toBeEnabled();
    });

    it('when checkboxes are selected, and the form is submitted, then request is made to sign moth agreement with expected request', async () => {
      const expectedRequest: SignMothAgreementRequest = createSignMothAgreementRequest(customer, {
        orderId: orderId,
        selectedCheckboxes: ['bicycles-tricycles-scooters', 'sandboxes'],
        firstName: customer.firstName,
        lastName: customer.lastName,
        email: customer.email?.address!!,
        address: formatAddress(customer.billingAddress),
        phone: customer.primaryPhone?.number!!
      });

      await renderSubject();

      const nextBtn = await screen.findByRole('button', {
        name: TranslationKeys.CommonComponents.NEXT_BUTTON
      });
      await waitFor(() => user.click(nextBtn));

      await actions.fillForm();
      await actions.submitForm();

      expect(mockLegacySignMothAgreement).toHaveBeenCalledWith(expectedRequest);
      expect(mockNavigate).toHaveBeenCalledWith(ROUTES.HOME, { replace: true });
      expectNotificationAlertContainsTitle(TranslationKeys.MothInspectionFormPage.SUCCESS_MESSAGE);
      expect(getOrdersWithSignedMothAgreements()).toContain(orderId);
    });

    it('when a restricted checkbox is selected, the form cannot be signed or submitted', async () => {
      const mockConfig = [
        {
          titleKey: 'Recreational, Child Play things',
          subcategories: [
            {
              subtitleKey: "Children's Playthings",
              checkboxes: [
                {
                  id: 'bicycles-tricycles-scooters',
                  label: 'Foo',
                  pdfKey: 'Bicycles Tricycles Scooters',
                  checked: false,
                  restricted: true
                }
              ]
            }
          ]
        }
      ];

      const mothFormConfigSpy = vi
        .spyOn(exports, 'mothFormConfig', 'get')
        .mockReturnValue(mockConfig);

      await renderSubject();
      const nextBtn = await screen.findByRole('button', {
        name: TranslationKeys.CommonComponents.NEXT_BUTTON
      });
      await waitFor(() => user.click(nextBtn));
      const bikeCheckbox = await screen.findByLabelText('Foo');

      await waitFor(() => user.click(bikeCheckbox));

      const agreeButton = await screen.findByRole('button', {
        name: TranslationKeys.MothInspectionFormPage.AGREE_BUTTON
      });
      const addSignatureButton = await screen.findByText(
        TranslationKeys.CommonComponents.AddSignature.BUTTON_TEXT
      );
      const restrictedTitleButton = await screen.findByText(
        TranslationKeys.MothInspectionFormPage.LegalCopy.RESTRICTED_ITEM_TITLE
      );
      expect(agreeButton).toBeDisabled();
      expect(addSignatureButton).toBeDisabled();
      expect(restrictedTitleButton).toBeInTheDocument();

      mothFormConfigSpy.mockRestore();
    });

    it('when "Other" checkboxes are selected & filled, and the form is submitted, then request is made to sign moth agreement with expected request', async () => {
      const expectedRequest: SignMothAgreementRequest = createSignMothAgreementRequest(customer, {
        orderId: orderId,
        otherCheckboxes: [{ id: 'other-child-playthings', description: 'ok' }]
      });
      await renderSubject();
      const nextBtn = await screen.findByRole('button', {
        name: TranslationKeys.CommonComponents.NEXT_BUTTON
      });
      await waitFor(() => user.click(nextBtn));
      const otherCheckbox = (await screen.findAllByLabelText('Other'))[0];
      await waitFor(() => user.click(otherCheckbox));
      const otherTextField = await screen.findByRole('textbox', { name: /other/i });
      await waitFor(() => expect(otherTextField).toBeInTheDocument());
      await waitFor(() => user.type(otherTextField, 'ok'));

      await actions.submitForm();

      expect(mockLegacySignMothAgreement).toHaveBeenCalledWith(expectedRequest);
      expectNotificationAlertContainsTitle(TranslationKeys.MothInspectionFormPage.SUCCESS_MESSAGE);
      expect(mockNavigate).toHaveBeenCalledWith(ROUTES.HOME, { replace: true });
    });

    it('when the initial move leg is "self-initial delivery", then the request is made with my billing address', async () => {
      const deliveryMoveLeg = createMoveLegApi({ moveLegType: 'SELF_INITIAL_DELIVERY' });
      const orderApi = createOrderApi({
        orderId: '12354',
        containers: [createContainerApi({ moveLegs: [deliveryMoveLeg] })]
      });
      const expectedRequest: SignMothAgreementRequest = createSignMothAgreementRequest(customer, {
        orderId: orderApi.orderId,
        address: formatAddress(customer.billingAddress),
        selectedCheckboxes: ['sandboxes']
      });
      mockLegacyGetCustomerOrders.mockResolvedValue([customerOrder]);
      await renderSubject();
      const nextBtn = await screen.findByRole('button', {
        name: TranslationKeys.CommonComponents.NEXT_BUTTON
      });
      await waitFor(() => user.click(nextBtn));
      const sandboxCheckbox = await screen.findByLabelText('Sandboxes');
      await waitFor(() => user.click(sandboxCheckbox));

      await actions.submitForm();

      expect(mockLegacySignMothAgreement).toHaveBeenCalledWith(expectedRequest);
      expectNotificationAlertContainsTitle(TranslationKeys.MothInspectionFormPage.SUCCESS_MESSAGE);
      expect(mockNavigate).toHaveBeenCalledWith(ROUTES.HOME, { replace: true });
    });

    it('makes the request with the origin address, when the initial move leg is "delivery"', async () => {
      const deliveryMoveLeg = createMoveLeg({
        moveLegType: 'INITIAL_DELIVERY',
        originationAddress: createMoveLegAddress({
          address1: '123 Lake Street',
          address2: '',
          city: 'Chicago',
          state: 'IL',
          postalCode: '12345'
        })
      });
      const order = createOrder({
        orderId: '12354',
        containers: [createContainer({ moveLegs: [deliveryMoveLeg] })]
      });
      const firstMoveLeg = order.containers[0].moveLegs[0];
      const expectedRequest: SignMothAgreementRequest = createSignMothAgreementRequest(customer, {
        orderId: order.orderId,
        address: formatAddress(firstMoveLeg.originationAddress),
        selectedCheckboxes: ['sandboxes']
      });
      await renderSubject();
      const nextBtn = await screen.findByRole('button', {
        name: TranslationKeys.CommonComponents.NEXT_BUTTON
      });
      await waitFor(() => user.click(nextBtn));
      const sandboxCheckbox = await screen.findByLabelText('Sandboxes');
      await waitFor(() => user.click(sandboxCheckbox));

      await actions.submitForm();

      expect(mockLegacySignMothAgreement).toHaveBeenCalledWith(expectedRequest);
      expectNotificationAlertContainsTitle(TranslationKeys.MothInspectionFormPage.SUCCESS_MESSAGE);
      expect(mockNavigate).toHaveBeenCalledWith(ROUTES.HOME, { replace: true });
    });

    it('displays error snackbar, when form submission fails', async () => {
      mockLegacySignMothAgreement.mockRejectedValue(new Error('Something went wrong'));
      await renderSubject();
      const nextBtn = await screen.findByRole('button', {
        name: TranslationKeys.CommonComponents.NEXT_BUTTON
      });
      await waitFor(() => user.click(nextBtn));
      const sandboxCheckbox = await screen.findByLabelText('Sandboxes');
      await waitFor(() => user.click(sandboxCheckbox));

      await actions.submitForm();

      expectNotificationAlertContainsTitle(
        TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE
      );
    });
  });

  describe('when in the PODS ready flow, it navigates to the next page based on the location state', () => {
    it('should navigate to PODS ready tasks page when there are tasks remaining', async () => {
      mockUseLocation.mockReturnValue({
        state: {
          onSuccessRoute: PodsReadyRoutes.TASKS
        },
        pathname: '/pods-ready'
      });
      await renderSubject();

      const nextBtn = await screen.findByRole('button', {
        name: TranslationKeys.CommonComponents.NEXT_BUTTON
      });

      await waitFor(() => user.click(nextBtn));
      await actions.fillForm();
      await actions.submitForm();

      expect(mockNavigate).toHaveBeenCalledWith(PodsReadyRoutes.TASKS, { replace: true });
    });
    it('should navigate to set password page when password onboarding is enabled & tasks are complete', async () => {
      mockUseLocation.mockReturnValue({
        state: {
          onSuccessRoute: PodsReadyRoutes.SET_PASSWORD
        },
        pathname: '/pods-ready'
      });

      await renderSubject();

      const nextBtn = await screen.findByRole('button', {
        name: TranslationKeys.CommonComponents.NEXT_BUTTON
      });

      await waitFor(() => user.click(nextBtn));
      await actions.fillForm();
      await actions.submitForm();

      expect(mockNavigate).toHaveBeenCalledWith(PodsReadyRoutes.SET_PASSWORD, { replace: true });
    });
    it('should navigate to the home page when password onboarding is disabled & tasks are complete', async () => {
      mockUseLocation.mockReturnValue({
        state: { onSuccessRoute: ROUTES.HOME },
        pathname: '/pods-ready'
      });

      await renderSubject();

      const nextBtn = await screen.findByRole('button', {
        name: TranslationKeys.CommonComponents.NEXT_BUTTON
      });
      // The child view renders an intro page
      await waitFor(() => user.click(nextBtn));
      // The child view renders the form
      await actions.fillForm();
      await actions.submitForm();

      expect(mockNavigate).toHaveBeenCalledWith(ROUTES.HOME, { replace: true });
    });
  });
  describe('Sends appropriate PODS_READY split events', () => {
    const renderWithEntryPointResult = async (myEntrypointResult: EntryPointResult) => {
      const result = renderWithLegacyProvidersAndState(<LegacyMothInspectionVariantSelector />, {
        entryPointState: {
          ...initialEntryPointState,
          entryPointResult: myEntrypointResult
        },
        initialEntries: ['/']
      });
      await runPendingPromises();
      return result;
    };
    it('Sends PODS_READY_COMPLETE if there are no rental agreements to sign', async () => {
      await renderSubject();

      const nextBtn = await screen.findByRole('button', {
        name: TranslationKeys.CommonComponents.NEXT_BUTTON
      });
      // The child view renders an intro page
      await waitFor(() => user.click(nextBtn));
      // The child view renders the form
      await actions.fillForm();
      await actions.submitForm();

      expect(mockNavigate).toHaveBeenCalledWith(ROUTES.HOME, { replace: true });
      expect(mockSendSplitEvent).toHaveBeenCalledWith(SplitEventType.PODS_READY_COMPLETE);
    });
    it('does not send PODS_READY_COMPLETE if there are remaining moth forms to sign', async () => {
      await renderWithEntryPointResult(
        createEntryPointResult({
          outstandingMothAgreements: [
            createOutstandingMothAgreement(),
            createOutstandingMothAgreement()
          ]
        })
      );

      const nextBtn = await screen.findByRole('button', {
        name: TranslationKeys.CommonComponents.NEXT_BUTTON
      });
      // The child view renders an intro page
      await waitFor(() => user.click(nextBtn));
      // The child view renders the form
      await actions.fillForm();
      await actions.submitForm();

      expect(mockNavigate).toHaveBeenCalledWith(ROUTES.HOME, { replace: true });
      expect(mockSendSplitEvent).not.toHaveBeenCalledWith(SplitEventType.PODS_READY_COMPLETE);
    });
    it('does not PODS_READY_COMPLETE if there are remaining rental agreements to sign', async () => {
      await renderWithEntryPointResult(
        createEntryPointResult({
          outstandingRentalAgreements: [createOutstandingRentalAgreement()],
          outstandingMothAgreements: [createOutstandingMothAgreement()]
        })
      );

      const nextBtn = await screen.findByRole('button', {
        name: TranslationKeys.CommonComponents.NEXT_BUTTON
      });
      // The child view renders an intro page
      await waitFor(() => user.click(nextBtn));
      // The child view renders the form
      await actions.fillForm();
      await actions.submitForm();

      expect(mockNavigate).toHaveBeenCalledWith(ROUTES.HOME, { replace: true });
      expect(mockSendSplitEvent).not.toHaveBeenCalledWith(SplitEventType.PODS_READY_COMPLETE);
    });
  });
});
