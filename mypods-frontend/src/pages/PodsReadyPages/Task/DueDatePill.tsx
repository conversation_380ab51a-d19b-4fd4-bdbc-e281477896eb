import React, { CSSProperties } from 'react';
import { CheckCircleIcon, ClockIcon } from '@phosphor-icons/react';
import { useTranslation } from 'react-i18next';
import { Design } from '../../../helpers/Design';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { formatShortMonthDayToLocale } from '../../../helpers/dateHelpers';

export interface DueDatePillProps {
  dueDate: Date;
  color: string;
  isComplete: boolean;
}

const getColor = (dueDate: Date, incompleteColor: string, completed: boolean) => {
  if (completed) {
    return Design.Alias.Color.successMain;
  }
  if (Date.now() > dueDate.getTime()) {
    return Design.Alias.Color.errorMain;
  }
  return incompleteColor;
};

const makeTransparent = (rgbColor: string) => `${rgbColor}15`;

export const DueDatePill = (props: DueDatePillProps) => {
  const { dueDate, color: incompleteColor, isComplete } = props;
  const { t } = useTranslation();
  const color = getColor(dueDate, incompleteColor, isComplete);
  const backgroundColor = makeTransparent(color);
  const Icon = isComplete ? CheckCircleIcon : ClockIcon;
  const dayString = isComplete
    ? t(TranslationKeys.TaskPage.DONE)
    : t(TranslationKeys.TaskPage.DUE_DATE, {
        dueDate: formatShortMonthDayToLocale(dueDate)
      });
  return (
    <div style={{ ...styles.container, color, backgroundColor }}>
      <Icon size={16} style={{ ...styles.svg }} />
      {dayString}
    </div>
  );
};

const styles = {
  container: {
    padding: '4px 8px',
    borderRadius: '100px',
    width: 'fit-content',
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'center'
  } as CSSProperties,
  svg: {
    position: 'relative',
    marginRight: '4px'
  } as CSSProperties
};
