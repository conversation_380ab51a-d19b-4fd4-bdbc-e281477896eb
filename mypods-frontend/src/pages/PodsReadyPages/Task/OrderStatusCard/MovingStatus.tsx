import React from 'react';
import { Grid, Typography } from '@mui/material';
import { ArrowRightIcon } from '@phosphor-icons/react';
import { Design } from '../../../../helpers/Design';
import { CityState } from '../../../../networkRequests/responseEntities/PodsReadyEntities';
import { ProgressBar } from './ProgressBar';
import { toTitleCase } from '../../../../helpers/stringHelper';

interface Props {
  origin: CityState;
  destination: CityState;
  startingDate?: string;
}
export const MovingStatus: React.FC<Props> = ({ origin, destination, startingDate }: Props) => (
  <Grid container {...styles.container}>
    <Grid container {...styles.typography}>
      <Grid item {...styles.addressBlock}>
        <Typography {...styles.city}>{toTitleCase(origin.city)}</Typography>
        <Typography {...styles.state}>{origin.state}</Typography>
      </Grid>
      <ArrowRightIcon size={24} />
      <Grid {...styles.addressBlock}>
        <Typography {...styles.city}>{toTitleCase(destination.city)}</Typography>
        <Typography {...styles.state}>{destination.state}</Typography>
      </Grid>
    </Grid>
    <ProgressBar startingDate={startingDate} />
  </Grid>
);

const styles = {
  container: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      gap: Design.Primitives.Spacing.xxs
    }
  },
  typography: {
    sx: {
      display: 'flex',
      flexDirection: 'row',
      gap: Design.Primitives.Spacing.xxs,
      color: Design.Alias.Color.neutral100
    }
  },
  addressBlock: {
    sx: {
      display: 'flex',
      flexDirection: 'row',
      alignItems: 'baseline',
      gap: Design.Primitives.Spacing.xxxs
    }
  },
  city: {
    color: 'inherit',
    ...Design.Alias.Text.Heading.Desktop.Md
  },
  state: {
    color: 'inherit',
    ...Design.Alias.Text.BodyUniversal.Xs
  }
};
