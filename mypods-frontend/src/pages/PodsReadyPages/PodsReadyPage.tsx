import React, { useEffect } from 'react';
import { useNavigate } from 'react-router';
import { useTranslation } from 'react-i18next';
import { useStartPodsReadySession } from '../../networkRequests/queries/podsReady/useStartPodsReadySession';
import { redirectToLoginWithEmail } from '../../context/ApigeeContext';
import { PodsReadyRoutes } from '../../PodsReadyRoutes';
import { TranslationKeys } from '../../locales/TranslationKeys';
import LoadingScreen from '../../components/Loading/LoadingScreen';
import {
  PODS_READY_PASSWORD_ONBOARDING,
  PODS_READY_SINGLE_ORDER_ENABLED,
  useFeatureFlags
} from '../../helpers/useFeatureFlags';
import { useShowPodsReadySingleOrder } from '../../helpers/useShowPodsReadySingleOrder';
import { usePasswordOnboardingEvents } from '../../config/usePasswordOnboardingEvents';

export const PodsReadyPage = () => {
  const { isReady, isPodsReadyPasswordOnboardingEnabled } = useFeatureFlags([
    PODS_READY_SINGLE_ORDER_ENABLED,
    PODS_READY_PASSWORD_ONBOARDING
  ]);
  const { sendPasswordOnboardingStart } = usePasswordOnboardingEvents();
  const { showPodsReadySingleOrder, isFetching: isFetchingShowPodsReadySingleOrder } =
    useShowPodsReadySingleOrder();
  const {
    hasToken,
    podsReadySessionClaims: { hasPassword, email }
  } = useStartPodsReadySession();
  const { t: translate } = useTranslation();
  const navigate = useNavigate();
  const passwordOnboarding = isPodsReadyPasswordOnboardingEnabled();

  const handleNavigation = () => {
    if (!isReady || isFetchingShowPodsReadySingleOrder) return;

    if (hasPassword && hasToken) {
      redirectToLoginWithEmail(email);
    } else if (showPodsReadySingleOrder) {
      navigate(PodsReadyRoutes.TASKS);
    } else if (passwordOnboarding && hasToken) {
      sendPasswordOnboardingStart();
      navigate(PodsReadyRoutes.SET_PASSWORD, { replace: true });
    } else {
      if (!hasPassword) {
        sendPasswordOnboardingStart();
      }
      redirectToLoginWithEmail(email);
    }
  };

  useEffect(handleNavigation, [
    hasPassword,
    email,
    isReady,
    showPodsReadySingleOrder,
    passwordOnboarding,
    isFetchingShowPodsReadySingleOrder
  ]);

  return <LoadingScreen loadingText={translate(TranslationKeys.LoadingScreens.HOME_PAGE)} />;
};
