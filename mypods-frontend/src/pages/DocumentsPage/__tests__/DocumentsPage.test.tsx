import {
  renderWithQueryProvider,
  runPendingPromises,
  testQueryClient
} from '../../../testUtils/RenderHelpers';
import { DocumentsPage } from '../DocumentsPage';
import { screen } from '@testing-library/react';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import {
  createBillingInformation,
  createCustomer,
  createOrderDocument,
  createRefreshSessionClaims
} from '../../../testUtils/MyPodsFactories';
import {
  mockGetBillingInformation,
  mockGetCustomer,
  mockGetCustomerDocuments,
  mockGetCustomerOrders,
  mockGetOrderDocuments,
  mockRefreshSession,
  mockSendSplitEvent
} from '../../../../setupTests';
import { QueryCacheKeys } from '../../../networkRequests/QueryCacheKeys';

describe('Document Page', () => {
  const testDoc = createOrderDocument();

  beforeEach(() => {
    mockRefreshSession.mockResolvedValue(createRefreshSessionClaims());
    mockGetBillingInformation.mockResolvedValue(createBillingInformation());
    mockGetCustomer.mockResolvedValue(createCustomer());
    mockGetCustomerDocuments.mockResolvedValue({ documents: [] });
    mockSendSplitEvent.mockResolvedValue({});
  });

  const queryClient = testQueryClient();

  it('should render documents page', async () => {
    mockGetCustomerOrders.mockResolvedValue({ orders: [] });
    mockGetOrderDocuments.mockResolvedValue({ documents: [testDoc] });

    renderWithQueryProvider(<DocumentsPage />, queryClient);
    await runPendingPromises();

    expect(await screen.findByText(TranslationKeys.DocumentsPage.HEADER)).toBeInTheDocument();
    expect(await screen.findByText(TranslationKeys.DocumentsPage.SUBTITLE)).toBeInTheDocument();
    expect(
      await screen.findByText(TranslationKeys.DocumentsPage.Types.RENTAL_AGREEMENT)
    ).toBeInTheDocument();
    expect(screen.getByText(`#${testDoc.orderId}`)).toBeInTheDocument();
  });

  it('displays a fallback message when the user has no documents', async () => {
    mockGetOrderDocuments.mockResolvedValue({ documents: [] });
    queryClient.setQueryData([QueryCacheKeys.ORDER_DOCUMENTS_KEY], { documents: [] });
    queryClient.setQueryData([QueryCacheKeys.CUSTOMER_DOCUMENTS_KEY], { documents: [] });
    queryClient.setQueryData([QueryCacheKeys.CUSTOMER_ORDERS_CACHE_KEY], { orders: [] });

    renderWithQueryProvider(<DocumentsPage />, queryClient);

    expect(screen.getByText(/No documents found/)).toBeInTheDocument();
  });
});
