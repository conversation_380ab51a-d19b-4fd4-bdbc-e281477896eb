import React, { act } from 'react';
import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { createDocument } from '../../../testUtils/MyPodsFactories';
import { DocumentCard } from '../DocumentCard';
import { DocumentDescription, IDocument } from '../../../domain/DocumentEntities';
import { mockGetSasUrl, mockRefreshSession } from '../../../../setupTests';
import {
  renderWithPoetProvidersAndState,
  runPendingPromises
} from '../../../testUtils/RenderHelpers';
import { DOCUMENT_TITLES, SIGNABLE_DOCUMENTS } from '../../../helpers/Documents';
import { createSession } from 'react-router';

describe('DocumentCard', () => {
  const user = userEvent.setup();
  const orderConfirmationDoc = createDocument({ isPoet: true });
  const displayName = 'Display Name';
  const documentName = 'docName';

  const renderCardForDocument = async (
    document: IDocument = orderConfirmationDoc,
    docName: string = documentName,
    fileName: string = ''
  ) => {
    const result = renderWithPoetProvidersAndState(
      <DocumentCard {...document} displayName={displayName} docName={docName} fileName={fileName} />
    );
    await runPendingPromises();
    return result;
  };

  const actions = {
    openDocument: async () => {
      await act(async () => await user.click(screen.getByRole('button')));
    }
  };

  beforeEach(() => {
    mockRefreshSession.mockResolvedValue(createSession());
  });

  it('displays the title & subtitle correctly', async () => {
    await renderCardForDocument();

    screen.getByText(displayName);
    screen.getByText(`#${orderConfirmationDoc.orderId}`);
  });

  it.each(SIGNABLE_DOCUMENTS)(
    'displays the green checkmark for signed %s document',
    async (documentName) => {
      const document = createDocument({ title: documentName as DocumentDescription });
      await renderCardForDocument(document, documentName);
      screen.getByTestId('checkmark-icon');
    }
  );

  const documentsThatCannotBeSigned = DOCUMENT_TITLES.filter(
    (title) => !SIGNABLE_DOCUMENTS.includes(title)
  );
  it.each(documentsThatCannotBeSigned)(
    'displays the grey document for non-signable %s document',
    async (documentName) => {
      const document = createDocument({ title: documentName as DocumentDescription });
      await renderCardForDocument(document, documentName);
      screen.getByTestId('document-icon');
    }
  );

  describe('poet download links', () => {
    const expectedUrl = 'https://file.pdf?sas=token';
    const fileName = 'https://file.pdf';
    const poetDocument = createDocument({ isPoet: true });
    const open = vi.fn();
    const originalOpen = window.open;

    beforeEach(() => {
      window.open = open;
    });

    afterEach(() => {
      window.open = originalOpen;
    });

    it('should redirect the user to returned url from the getSasUrl call', async () => {
      mockGetSasUrl.mockResolvedValue(expectedUrl);
      await renderCardForDocument(poetDocument, documentName, fileName);

      await actions.openDocument();

      expect(mockGetSasUrl).toHaveBeenCalledWith(fileName, false);
      expect(open).toHaveBeenCalledWith(expectedUrl, '_blank');
    });
  });
});
