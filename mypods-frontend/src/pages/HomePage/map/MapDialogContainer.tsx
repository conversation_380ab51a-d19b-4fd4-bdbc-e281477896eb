import { Text } from 'pods-component-library';
import React from 'react';
import { css } from 'pods-component-library/styled-system/css';
import { isBefore } from 'date-fns';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { MoveLegContainerSizeAndId } from './MarkerData';
import { ContainerImage } from './ContainerImage';
import { ScheduledStatus } from '../../../domain/OrderEntities';

type MapDialogContainerProps = {
  moveLeg: MoveLegContainerSizeAndId;
};

export const MapDialogContainer = ({ moveLeg }: MapDialogContainerProps) => {
  let displayId = '';
  if (moveLeg.containerId) {
    displayId = `#${moveLeg.containerId}`;
  }
  let status: ScheduledStatus = 'UNSCHEDULED';
  if (moveLeg.firstAvailableDate) {
    status = isBefore(moveLeg.firstAvailableDate, new Date()) ? 'PAST' : 'FUTURE';
  }
  return (
    <div className={styles.container}>
      <ContainerImage containerSize={moveLeg.containerSize} />
      <div className={styles.containerDetails}>
        <Text
          i18nKey={TranslationKeys.HomePage.Map.ContainerDialog[status]}
          values={{
            size: moveLeg.containerSize,
            id: displayId,
            date: moveLeg.firstAvailableDate?.toLocaleDateString(navigator.language, {
              day: 'numeric',
              month: 'long'
            })
          }}
          components={{
            id: <strong key={`${moveLeg.moveLegId}-id`} className={styles.id} />,
            status: <strong key={`${moveLeg.moveLegId}-status`} className={styles.status} />
          }}
        />
      </div>
    </div>
  );
};

const styles = {
  container: css({
    display: 'flex'
  }),
  containerDetails: css({
    display: 'flex',
    fontFamily: 'secondary',
    fontSize: 14,
    fontStyle: 'normal',
    fontWeight: 600,
    color: 'secondary500'
  }),
  id: css({
    fontWeight: 400
  }),
  status: css({
    fontWeight: 400,
    color: 'neutral700'
  })
};
