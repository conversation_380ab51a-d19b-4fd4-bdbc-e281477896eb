import { useMapsLibrary } from '@vis.gl/react-google-maps';
import { useEffect, useRef, useState } from 'react';
import { MoveLegAddress } from '../../../domain/OrderEntities';

const CACHED_LOCATIONS_KEY = 'cachedLocations';

export type GeoLocatedAddress = {
  addressString: string;
  location: google.maps.LatLngLiteral;
};

export const useGeocoder = () => {
  const geocodingLibrary = useMapsLibrary('geocoding');
  const [knownLocations, setKnownLocations] = useState<{
    [key: string]: google.maps.LatLngLiteral;
  }>({});
  const geocoder = useRef<google.maps.Geocoder | null>(null);
  const [ready, setReady] = useState<boolean>(!!geocoder.current);

  useEffect(() => {
    if (geocodingLibrary && !geocoder.current) {
      geocoder.current = new geocodingLibrary.Geocoder();
      const locationsString = localStorage.getItem(CACHED_LOCATIONS_KEY);
      if (locationsString) {
        setKnownLocations(JSON.parse(locationsString));
      }
      setReady(true);
    }
  }, [geocodingLibrary]);

  const getLocation = (address: MoveLegAddress): Promise<GeoLocatedAddress> => {
    const addressString = `${address.address1}, ${address.city}, ${address.state} ${address.postalCode} ${address.country}`;
    return new Promise<GeoLocatedAddress>((resolve, reject) => {
      const resolveWithAddress = (location: google.maps.LatLngLiteral) => {
        resolve({ addressString, location });
      };
      const cachedLocation = knownLocations[addressString];
      if (cachedLocation) {
        resolveWithAddress(cachedLocation);
      } else if (geocoder.current) {
        geocoder.current
          .geocode({ address: addressString })
          .then((response) => {
            const { location } = response.results[0].geometry;
            const latLon = { lat: location.lat(), lng: location.lng() };
            knownLocations[addressString] = latLon;
            localStorage.setItem(CACHED_LOCATIONS_KEY, JSON.stringify(knownLocations));
            resolveWithAddress(latLon);
          })
          .catch(reject);
      } else {
        reject(new Error('Geocoder not ready'));
      }
    });
  };

  return {
    getLocation,
    ready
  };
};
