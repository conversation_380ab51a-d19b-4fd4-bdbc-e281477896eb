import React from 'react';
import { AdvancedMarker } from '@vis.gl/react-google-maps';
import { MapIconType } from './getMapIconTypeAndAddress';
import { HomeOnMapPinIcon } from '../../../assets/HomeOnMapPinIcon';
import { WarehouseOnMapPinIcon } from '../../../assets/WarehouseOnMapIcon';

import { MarkerData } from './MarkerData';

type MapMarkerProps = {
  markerData: MarkerData;
  onClick: (markerData: MarkerData) => void;
};

export const MapMarker = ({ markerData, onClick }: MapMarkerProps) => {
  const preventEventPropagationSoDialogDoesNotInstantlyCloseOnAndroid = (
    event: google.maps.MapMouseEvent
  ) => {
    event.domEvent.stopPropagation();
    event.domEvent.preventDefault();
  };

  const handleClick = (event: google.maps.MapMouseEvent) => {
    preventEventPropagationSoDialogDoesNotInstantlyCloseOnAndroid(event);
    if (markerData.screen) {
      onClick(markerData);
    }
  };
  const numberOfContainers = markerData.moveLegs.length;
  return (
    <AdvancedMarker position={markerData.location} onClick={handleClick}>
      {markerData.type === MapIconType.HOME && (
        <HomeOnMapPinIcon numberOfContainers={numberOfContainers} />
      )}
      {markerData.type !== MapIconType.HOME && (
        <WarehouseOnMapPinIcon numberOfContainers={numberOfContainers} />
      )}
    </AdvancedMarker>
  );
};
