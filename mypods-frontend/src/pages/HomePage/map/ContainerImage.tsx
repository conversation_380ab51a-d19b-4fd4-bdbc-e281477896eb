import React from 'react';
import { css } from 'pods-component-library/styled-system/css';
import { ENV_VARS } from '../../../environment';

type ContainerImageProps = {
  containerSize: string;
};

export const ContainerImage = ({ containerSize }: ContainerImageProps) => (
  <div className={styles.container}>
    <img
      alt={`${containerSize}ft container`}
      src={`${ENV_VARS.ASSETS_BASE_URL}/images/containers/${containerSize}ftContainer.png`}
      height={38}
      width="auto"
    />
  </div>
);

const styles = {
  container: css({
    width: '100px'
  })
};
