import React, { ReactNode, useEffect, useState } from 'react';
import { css } from 'pods-component-library/styled-system/css';
import { Map, useMap } from '@vis.gl/react-google-maps';
import isEmpty from 'lodash/isEmpty';
import { Container, MoveLeg, Order } from '../../../domain/OrderEntities';
import { useGeocoder } from './useGeocoder';
import { getMapIconTypeAndAddress } from './getMapIconTypeAndAddress';
import { MapMarker } from './MapMarker';
import { MapDialog } from './MapDialog';
import { MarkerData } from './MarkerData';

const GOOGLE_MAP_ID = '1fcf79dc59775d96';

type MapViewProps = {
  order: Order;
};

export const MapView = ({ order }: MapViewProps) => {
  const [center, setCenter] = useState<google.maps.LatLngLiteral | null>(null);
  const [markers, setMarkers] = useState<ReactNode[] | null>(null);
  const [mapBounds, setMapBounds] = useState<google.maps.LatLngBounds | null>(null);
  const [preventZoomInOnMarkerClick, setPreventZoomInOnMarkerClick] = useState<boolean>(false);
  const { getLocation, ready: geoCoderReady } = useGeocoder();
  const [geocodedMarkerData] = useState<{ [key: string]: MarkerData }>({});
  const [selectedMarker, setSelectedMarker] = useState<MarkerData | null>(null);

  const geoCodeMarkers = () =>
    order.containers.map((container: Container) => {
      // we actually want to return promises so all the network requests can start then once all are finished we create markers and set up the map
      const currentMoveLeg = container.moveLegs.find(
        (moveLeg: MoveLeg) => moveLeg.isCurrentMoveLeg
      );
      if (currentMoveLeg) {
        const { type, address } = getMapIconTypeAndAddress(currentMoveLeg);
        return getLocation(address).then(({ addressString, location }) => {
          if (!geocodedMarkerData[addressString]) {
            geocodedMarkerData[addressString] = { type, location, moveLegs: [], screen: null };
          }
          geocodedMarkerData[addressString].moveLegs.push({ ...currentMoveLeg, ...container });
        });
      }
      return Promise.resolve();
    });

  const createMarkersAndBoundsAndSetCenter = () => {
    const currentBounds = new google.maps.LatLngBounds();
    const markerElements = Object.values(geocodedMarkerData).map((data, index) => {
      currentBounds.extend(data.location);
      return <MapMarker key={index} markerData={data} onClick={setSelectedMarker} />;
    });
    setMarkers(markerElements);
    setMapBounds(currentBounds);
    const boundCenter = currentBounds.getCenter();
    setCenter({ lat: boundCenter.lat(), lng: boundCenter.lng() });
  };

  useEffect(() => {
    if (geoCoderReady && isEmpty(geocodedMarkerData)) {
      Promise.all(geoCodeMarkers()).then(createMarkersAndBoundsAndSetCenter);
    }
  }, [geoCoderReady, order]);

  const isPoint = (bounds: google.maps.LatLngBounds) =>
    bounds.getNorthEast().equals(bounds.getSouthWest());

  const closeDialog = () => {
    setSelectedMarker(null);
  };

  const FitToBounds = () => {
    const map = useMap();
    useEffect(() => {
      if (!map || !mapBounds || isPoint(mapBounds) || preventZoomInOnMarkerClick) return;
      map.fitBounds(mapBounds);
      setPreventZoomInOnMarkerClick(true);
    });
    return null;
  };

  const ConvertWorldToScreenCoords = () => {
    const locationsNeedingScreenCords = Object.values(geocodedMarkerData).filter(
      (marker) => !marker.screen
    );
    const map = useMap();
    useEffect(() => {
      if (!map || isEmpty(locationsNeedingScreenCords)) return;
      google.maps.event.addListenerOnce(map, 'idle', () => {
        const overlay = new google.maps.OverlayView();
        overlay.onAdd = () => {};
        overlay.draw = () => {
          const projection = overlay.getProjection();
          locationsNeedingScreenCords.forEach((location) => {
            const divPixel = projection.fromLatLngToContainerPixel(location.location);
            if (divPixel) {
              location.screen = { left: divPixel.x, top: divPixel.y };
            }
          });
        };
        overlay.onRemove = () => {};
        overlay.setMap(map);

        return () => {
          overlay.setMap(null);
        };
      });
    }, [map, geocodedMarkerData]);

    return null;
  };

  if (!center || !markers || !mapBounds) {
    return <div className={styles.map} />;
  }

  return (
    <div className={styles.container}>
      <div className={styles.map}>
        <Map
          defaultZoom={11}
          defaultCenter={center}
          gestureHandling="none"
          mapId={GOOGLE_MAP_ID}
          clickableIcons={false}
          scrollwheel={false}
          disableDoubleClickZoom
          keyboardShortcuts={false}
          disableDefaultUI>
          {markers}
          <FitToBounds />
          <ConvertWorldToScreenCoords />
        </Map>
      </div>
      {selectedMarker && (
        <MapDialog markerData={selectedMarker} onClose={closeDialog} isOpen={!!selectedMarker} />
      )}
    </div>
  );
};

const styles = {
  container: css({
    position: 'relative'
  }),
  map: css({
    width: '100%',
    height: '220px',
    borderRadius: '24px',
    marginBottom: '16px',
    overflow: 'hidden'
  })
};
