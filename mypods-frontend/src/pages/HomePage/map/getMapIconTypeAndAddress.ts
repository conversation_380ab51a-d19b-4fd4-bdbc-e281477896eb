import {
  MoveLeg,
  MoveLegAddress,
  MoveLegType,
  MoveLegTypeEnum
} from '../../../domain/OrderEntities';

export enum MapIconType {
  'WAREHOUSE',
  'HOME'
}

export interface MapIconTypeAndAddress {
  type: MapIconType;
  address: MoveLegAddress;
}

const futureHomeTypes: MoveLegType[] = [
  MoveLegTypeEnum.INITIAL_DELIVERY,
  MoveLegTypeEnum.SELF_INITIAL_DELIVERY
];

const pastHomeTypes: MoveLegType[] = [
  ...futureHomeTypes,
  MoveLegTypeEnum.REDELIVERY,
  MoveLegTypeEnum.CITY_SERVICE_DELIVERY,
  MoveLegTypeEnum.CITY_SERVICE_REDELIVERY
];

const pastOriginAddressTypes: MoveLegType[] = [MoveLegTypeEnum.VISIT_CONTAINER];

export const getMapIconTypeAndAddress = (moveLeg: MoveLeg): MapIconTypeAndAddress => {
  let type = MapIconType.WAREHOUSE;
  let address = moveLeg.destinationAddress;
  if (moveLeg.scheduledStatus === 'PAST') {
    if (pastHomeTypes.includes(moveLeg.moveLegType)) {
      type = MapIconType.HOME;
    }
    if (pastOriginAddressTypes.includes(moveLeg.moveLegType)) {
      address = moveLeg.originationAddress;
    }
  } else if (futureHomeTypes.includes(moveLeg.moveLegType)) {
    type = MapIconType.HOME;
  }
  return { type, address };
};
