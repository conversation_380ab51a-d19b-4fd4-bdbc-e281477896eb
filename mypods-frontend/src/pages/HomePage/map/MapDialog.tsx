import React, { useEffect } from 'react';
import { css } from 'pods-component-library/styled-system/css';
import { MarkerData } from './MarkerData';
import { MapDialogContainer } from './MapDialogContainer';
import { Design } from '../../../helpers/Design';

const DIALOG_WIDTH = 246;

type MapDialogProps = {
  markerData: MarkerData;
  isOpen: boolean;
  onClose: () => void;
};

export const MapDialog = ({ markerData, onClose, isOpen }: MapDialogProps) => {
  const actualLeft = markerData.screen!.left - DIALOG_WIDTH / 2;
  const actualBottom = 220 - (markerData.screen!.top - 48);

  useEffect(() => {
    if (!isOpen) return;
    const onKeyDown = (keyEvent: KeyboardEvent) => {
      if (keyEvent.key === 'Escape') {
        keyEvent.preventDefault();
        keyEvent.stopPropagation();
        onClose();
        return false;
      }
      return true;
    };
    document.addEventListener('keydown', onKeyDown);
    return () => {
      document.removeEventListener('keydown', onKeyDown);
    };
  }, [isOpen]);

  return (
    <>
      <div
        style={{ bottom: actualBottom, left: actualLeft }}
        className={styles.dialog}
        role="dialog">
        {markerData.moveLegs.map((moveLeg) => (
          <MapDialogContainer key={moveLeg.moveLegId} moveLeg={moveLeg} />
        ))}
      </div>
      <div className={styles.dialogOverlay} aria-hidden="true" onClick={onClose} />
    </>
  );
};

const styles = {
  dialog: css({
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
    gap: '8px',
    backgroundColor: 'white',
    borderRadius: '16px',
    width: `${DIALOG_WIDTH}px`,
    boxShadow: '0 0 11px 0 rgba(0, 0, 0, 0.11);',
    paddingY: '12px',
    position: 'absolute',
    zIndex: Design.Alias.ZIndex.mapDialog
  }),
  dialogOverlay: css({
    position: 'fixed',
    inset: 0,
    backgroundColor: 'transparent',
    zIndex: Design.Alias.ZIndex.mapDialog - 1
  })
};
