import { MapIconType } from './getMapIconTypeAndAddress';
import { ScreenCoords } from './ScreenCoords';
import { MoveLeg } from '../../../domain/OrderEntities';

export interface MoveLegContainerSizeAndId extends MoveLeg {
  containerId: string;
  containerSize: string;
}

export interface MarkerData {
  type: MapIconType;
  location: google.maps.LatLngLiteral;
  screen: ScreenCoords | null;
  moveLegs: MoveLegContainerSizeAndId[];
}
