import { createMoveLeg, createMoveLegAddress } from '../../../../testUtils/MyPodsFactories';
import { getMapIconTypeAndAddress, MapIconType } from '../getMapIconTypeAndAddress';
import { MoveLegType, MoveLegTypeEnum, ScheduledStatus } from '../../../../domain/OrderEntities';

describe('getMapIconTypeAndAddress should return', () => {
  const destinationAddress = createMoveLegAddress({
    address1: 'destination',
    city: 'destination city',
    state: 'ds'
  });
  const originationAddress = createMoveLegAddress({
    address1: 'origin',
    city: 'origin city',
    state: 'os'
  });

  const createMoveLegWithAddresses = (overrides: {
    moveLegType: MoveLegType;
    scheduledStatus: ScheduledStatus;
  }) => {
    overrides.moveLegType;
    return createMoveLeg({
      moveLegType: overrides.moveLegType,
      scheduledStatus: overrides.scheduledStatus,
      destinationAddress,
      originationAddress
    });
  };

  describe('HOME and destination address when', () => {
    [
      MoveLegTypeEnum.INITIAL_DELIVERY,
      MoveLegTypeEnum.SELF_INITIAL_DELIVERY,
      MoveLegTypeEnum.REDELIVERY,
      MoveLegTypeEnum.CITY_SERVICE_DELIVERY,
      MoveLegTypeEnum.CITY_SERVICE_REDELIVERY
    ].forEach((moveLegType: MoveLegType) => {
      it(`moveLegType is ${moveLegType} and scheduledStatus is PAST`, () => {
        const moveLeg = createMoveLegWithAddresses({ moveLegType, scheduledStatus: 'PAST' });

        const actual = getMapIconTypeAndAddress(moveLeg);

        expect(actual).toEqual({ type: MapIconType.HOME, address: destinationAddress });
      });
    });

    [MoveLegTypeEnum.INITIAL_DELIVERY, MoveLegTypeEnum.SELF_INITIAL_DELIVERY].forEach(
      (moveLegType: MoveLegType) => {
        const statuses: ScheduledStatus[] = ['FUTURE', 'UNSCHEDULED'];

        statuses.forEach((status) => {
          it(`moveLegType is ${moveLegType} and scheduledStatus is ${status}`, () => {
            const moveLeg = createMoveLegWithAddresses({ moveLegType, scheduledStatus: status });

            const actual = getMapIconTypeAndAddress(moveLeg);

            expect(actual).toEqual({
              type: MapIconType.HOME,
              address: destinationAddress
            });
          });
        });
      }
    );
  });

  describe('WAREHOUSE and destination address when', () => {
    [
      MoveLegTypeEnum.PICKUP,
      MoveLegTypeEnum.FINAL_PICKUP,
      MoveLegTypeEnum.SELF_FINAL_PICKUP,
      MoveLegTypeEnum.CITY_SERVICE_RETURN,
      MoveLegTypeEnum.CITY_SERVICE_FINAL_PICKUP
    ].forEach((moveLegType: MoveLegType) => {
      it(`moveLegType is ${moveLegType} and scheduledStatus is PAST`, () => {
        const moveLeg = createMoveLegWithAddresses({ moveLegType, scheduledStatus: 'PAST' });

        const actual = getMapIconTypeAndAddress(moveLeg);

        expect(actual).toEqual({ type: MapIconType.WAREHOUSE, address: destinationAddress });
      });
    });

    [
      MoveLegTypeEnum.PORT_TO_WAREHOUSE,
      MoveLegTypeEnum.LOCAL_PORT_TO_WAREHOUSE,
      MoveLegTypeEnum.WAREHOUSE_TO_WAREHOUSE
    ].forEach((moveLegType: MoveLegType) => {
      const statuses: ScheduledStatus[] = ['FUTURE', 'UNSCHEDULED', 'PAST'];
      statuses.forEach((status) => {
        it(`moveLegType is ${moveLegType} and scheduledStatus is ${status}`, () => {
          const moveLeg = createMoveLegWithAddresses({ moveLegType, scheduledStatus: status });

          const actual = getMapIconTypeAndAddress(moveLeg);

          expect(actual).toEqual({ type: MapIconType.WAREHOUSE, address: destinationAddress });
        });
      });
    });
  });

  describe('WAREHOUSE and origin address when', () => {
    it(`moveLegType is VISIT_CONTAINER and scheduledStatus is PAST`, () => {
      const moveLeg = createMoveLegWithAddresses({
        moveLegType: 'VISIT_CONTAINER',
        scheduledStatus: 'PAST'
      });

      const actual = getMapIconTypeAndAddress(moveLeg);

      expect(actual).toEqual({ type: MapIconType.WAREHOUSE, address: originationAddress });
    });
  });
});
