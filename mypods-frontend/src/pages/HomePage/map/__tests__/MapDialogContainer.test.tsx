import { screen } from '@testing-library/react';
import {
  renderWithPoetProvidersAndState,
  runPendingPromises
} from '../../../../testUtils/RenderHelpers';
import { MapDialogContainer } from '../MapDialogContainer';
import {
  createCustomer,
  createMoveLeg,
  createRefreshSessionClaims
} from '../../../../testUtils/MyPodsFactories';
import { MoveLegContainerSizeAndId } from '../MarkerData';
import { mockGetCustomer, mockRefreshSession } from '../../../../../setupTests';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import { subDays, addDays } from 'date-fns';

describe('MapDialogContainer', () => {
  const createMoveLegWithContainerSize = (
    overrides: Partial<MoveLegContainerSizeAndId> = {}
  ): MoveLegContainerSizeAndId => {
    return {
      ...createMoveLeg(overrides),
      containerSize: '16',
      containerId: '156348',
      ...overrides
    };
  };
  const render = async (moveLeg: MoveLegContainerSizeAndId) => {
    const result = renderWithPoetProvidersAndState(<MapDialogContainer moveLeg={moveLeg} />);
    await runPendingPromises();
    return result;
  };
  const sessionClaims = createRefreshSessionClaims();
  const customer = createCustomer();
  const pastDate = subDays(new Date(), 2);
  const futureDate = addDays(new Date(), 2);

  beforeEach(() => {
    mockGetCustomer.mockResolvedValue(customer);
    mockRefreshSession.mockResolvedValue(sessionClaims);
  });

  describe('should render the', () => {
    [
      { size: '8', altText: '8ft container' },
      { size: '12', altText: '12ft container' },
      { size: '16', altText: '16ft container' }
    ].forEach(({ size, altText }) => {
      it(altText, async () => {
        const moveLeg = createMoveLegWithContainerSize({ containerSize: size });

        await render(moveLeg);

        expect(await screen.findByAltText(altText)).toBeInTheDocument();
      });
    });

    it('the delivered message when first available date is in the PAST (ignore scheduledStatus)', async () => {
      const moveLeg = createMoveLegWithContainerSize({
        firstAvailableDate: pastDate,
        scheduledStatus: 'FUTURE'
      });

      await render(moveLeg);

      expect(
        await screen.findByText(TranslationKeys.HomePage.Map.ContainerDialog.PAST)
      ).toBeInTheDocument();
    });

    it('the arriving message when first available date is in the FUTURE (ignore scheduledStatus)', async () => {
      const moveLeg = createMoveLegWithContainerSize({
        firstAvailableDate: futureDate,
        scheduledStatus: 'PAST'
      });

      await render(moveLeg);

      expect(
        await screen.findByText(TranslationKeys.HomePage.Map.ContainerDialog.FUTURE)
      ).toBeInTheDocument();
    });

    it('the delivered message when first available date is undefined (ignore scheduledStatus)', async () => {
      const moveLeg = createMoveLegWithContainerSize({
        firstAvailableDate: undefined,
        scheduledStatus: 'PAST'
      });

      await render(moveLeg);

      expect(
        await screen.findByText(TranslationKeys.HomePage.Map.ContainerDialog.UNSCHEDULED)
      ).toBeInTheDocument();
    });
  });
});
