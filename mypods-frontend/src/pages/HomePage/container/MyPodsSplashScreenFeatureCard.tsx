import { useTranslation } from 'react-i18next';
import { FlexBox } from 'pods-component-library/src/components/Layout/FlexBox';
import { css } from 'pods-component-library/styled-system/css';
import Typography from '@mui/material/Typography/Typography';
import React, { ReactNode } from 'react';
import { Design } from '../../../helpers/Design';

type SplashScreenItemProps = {
  translations: {
    TITLE: string;
    DESCRIPTION: string;
  };
  icon: ReactNode;
};

export const MyPodsSplashScreenFeatureCard = ({ translations, icon }: SplashScreenItemProps) => {
  const { t } = useTranslation();
  return (
    <FlexBox>
      <div style={styles.icon}>{icon}</div>
      <FlexBox css={stylesheet.titleAndDescription}>
        <Typography {...styles.title}>{t(translations.TITLE)}</Typography>
        <Typography {...styles.description}>{t(translations.DESCRIPTION)}</Typography>
      </FlexBox>
    </FlexBox>
  );
};

const styles = {
  title: {
    ...Design.Alias.Text.Heading.Desktop.Xs,
    lineHeight: 1.5,
    color: Design.Alias.Color.accent900
  },
  description: {
    ...Design.Alias.Text.BodyUniversal.Xs,
    color: Design.Alias.Color.neutral700
  },
  icon: {
    display: 'flex',
    flex: '0 0 auto',
    color: Design.Alias.Color.secondary500,
    backgroundColor: Design.Alias.Color.infoLight,
    width: '35px',
    height: '35px',
    borderRadius: '50%',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: '8px'
  }
};

const stylesheet = {
  titleAndDescription: css.raw({
    flexDirection: 'column'
  })
};
