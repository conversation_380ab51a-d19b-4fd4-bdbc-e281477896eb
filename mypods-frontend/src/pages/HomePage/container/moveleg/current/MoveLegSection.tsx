import { Grid, Typography, useMediaQuery } from '@mui/material';
import React, { useMemo, useState } from 'react';
import Divider from '@mui/material/Divider';
import { addDays } from 'date-fns';
import { ServiceCountdownAlert } from '../../serviceCountdown/ServiceCountdownAlert';
import { DateComponent } from '../DateComponent';
import { formatAddress } from '../../../../../networkRequests/responseEntities/CustomerEntities';
import { ContainerProgressLine, ProgressLineVariant } from '../../ContainerProgressLine';
import { ScheduleMoveLegButton } from './ScheduleMoveLegButton';
import {
  ApplyQuoteToOrderRequest,
  ScheduledStatus,
  serviceCountdownTypeForMoveLegType,
  UpdateMoveLegRequest
} from '../../../../../domain/OrderEntities';
import { ScheduleMoveLeg } from '../../scheduling/ScheduleMoveLeg';
import { theme } from '../../../../../PodsTheme';
import { InTransitMoveLeg } from './InTransitMoveLeg';
import { AddressComponent } from '../AddressComponent';
import { EditButton } from '../../../../../components/buttons/EditButton';
import { isWithin24Hours } from '../../../../../helpers/dateHelpers';
import { getDateLabels } from '../../../../../locales/TranslationConstants';
import { VisitContainerSection } from '../VisitContainerSection';
import useMoveLegContext from '../MoveLegContext';
import { sharedMoveLegStyles } from '../sharedMoveLegStyles';
import useContainerContext from '../../../../../context/ContainerContext';
import { ReviewInitialDeliveryAlert } from '../../../../../components/alert/ReviewInitialDeliveryAlert';
import useSingleOrderContext from '../../../../../context/SingleOrderContext';
import { useGtmEvents } from '../../../../../config/google/useGtmEvents';
import { PickupWindow } from '../PickupWindow';
import {
  ORDER_MODIFICATION_ENABLED,
  REDESIGNED_SCHEDULING,
  REDESIGNED_SCHEDULING_PICKUP,
  useFeatureFlags
} from '../../../../../helpers/useFeatureFlags';
import { ChatToScheduleComponent } from '../ChatToScheduleComponent';
import { ManagePickupPanel } from '../../../Scheduling/ManagePickupPanel';
import { ManageVisitPanel, OnMoveLegUpdateSaveProps } from '../../../Scheduling/ManageVisitPanel';
import { ManageDropOffContainer } from '../../../Scheduling/ManageDropOffContainer';
import useOrdersContext from '../../../../../context/OrdersContext';
import { UpdateMoveLegResponse } from '../../../../../networkRequests/responseEntities/OrderAPIEntities';
import { GtmScheduleType } from '../../../../../config/google/GoogleEntities';
import {
  createGtmErrorRequest,
  GA_GENERIC_BACKEND_MESSAGE
} from '../../../../../config/google/googleAnalyticsUtils';
import { isStaleDataError } from '../../../utils';
import { useApplyQuoteToOrder } from '../../../../../networkRequests/mutations/useApplyQuoteToOrder';
import { useUpdateMoveLeg } from '../../../../../networkRequests/mutations/useUpdateMoveLeg';
import {
  createGtmScheduleType,
  createUpdateMoveLegRequest,
  isMoveOrRedelivery
} from '../../scheduling/moveLegHelpers';

export const MoveLegSection = () => {
  const { moveLeg, isLastMoveLeg, isLastRenderedMoveLeg, title } = useMoveLegContext();
  const { refetch, refetchOnFailure } = useOrdersContext();
  const { order, container } = useContainerContext();
  const {
    scheduling: { dateState, addressState }
  } = useMoveLegContext();
  const {
    currentlySelectedQuote,
    moveLegScheduling: {
      currentlySelectedMoveLeg,
      setSelectedOrderId,
      editMoveLegScheduling,
      stopMoveLegScheduling,
      isSaving,
      isCancelling
    }
  } = useSingleOrderContext();
  const gtmEvents = useGtmEvents();
  const { startSchedule, startEditSchedule } = gtmEvents;
  const applyQuoteToOrder = useApplyQuoteToOrder();
  const updateMoveLeg = useUpdateMoveLeg();
  const {
    isReady,
    isOrderModEnabled,
    isRedesignedSchedulingEnabled,
    isRedesignedSchedulingPickupEnabled
  } = useFeatureFlags([
    ORDER_MODIFICATION_ENABLED,
    REDESIGNED_SCHEDULING,
    REDESIGNED_SCHEDULING_PICKUP
  ]);
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = moveLegStyles(isMobile);
  // TODO: panel management can go into the context like the save / edit states.
  const [isPickupPanelOpen, setIsPickupPanelOpen] = useState(false);
  const [isVisitPanelOpen, setIsVisitPanelOpen] = useState(false);
  const [isDropOffDrawerOpen, setIsDropOffDrawerOpen] = useState(false);
  const [priceDifferenceResponse, setPriceDifferenceResponse] = useState<
    UpdateMoveLegResponse | null | undefined
  >();
  const [isEditingGtmEvent, setIsEditingGtmEvent] = useState<boolean>(false);
  const [isUpdateMoveLeg, setIsUpdateMoveLeg] = useState<boolean>(false);
  const [showInitialDeliveryAlert, setShowInitialDeliveryAlert] = useState<boolean>(
    moveLeg.moveLegType === 'INITIAL_DELIVERY' && !order.initialDeliveryPlacementIsReviewed
  );

  const orderModEnabled = useMemo(
    () => isReady && isOrderModEnabled(),
    [isReady, isOrderModEnabled]
  );

  const gtmSchedulePayLoad = {
    transactionId: order.orderId,
    moveLegId: moveLeg.moveLegId,
    moveLegType: moveLeg.moveLegType.toString(),
    containerId: container.containerId
  };

  const scheduledStatusToProgressLineVariant = (
    scheduledStatus: ScheduledStatus
  ): ProgressLineVariant => {
    if (scheduledStatus === 'UNSCHEDULED') {
      return 'FADED_DASHED';
    }
    return 'DASHED';
  };

  function isCallToScheduleLeg(): boolean {
    return (
      moveLeg.moveLegType === 'WAREHOUSE_TO_WAREHOUSE' &&
      moveLeg.scheduledStatus === 'UNSCHEDULED' &&
      !isSchedulableButNotOnline()
    );
  }

  const isUnscheduledContainerVisit =
    moveLeg.moveLegType === 'VISIT_CONTAINER' && moveLeg.containerVisitDate == null;
  const isScheduledContainerVisit =
    moveLeg.moveLegType === 'VISIT_CONTAINER' && moveLeg.containerVisitDate != null;
  const isSchedulingMoveLeg = currentlySelectedMoveLeg === moveLeg && !isCancelling;

  const hideInitialDeliveryAlert = () => {
    setShowInitialDeliveryAlert(false);
  };

  const shouldRenderScheduledButton = () => {
    if (!moveLeg.isSchedulableOnline) return false;
    if (moveLeg.isCityService) return false;
    if (moveLeg.isCrossBorder) return false;
    if (moveLeg.isHawaii) return false;
    if (isUnscheduledContainerVisit) return true;
    return moveLeg.scheduledStatus === 'UNSCHEDULED';
  };

  const shouldRenderEditButton = () => {
    if (isSchedulingMoveLeg) return false;
    if (!moveLeg.isSchedulableOnline) return false;
    if (moveLeg.moveLegType === 'VISIT_CONTAINER') return false;
    if (moveLeg.scheduledStatus !== 'FUTURE') return false;
    if (isWithin24Hours(moveLeg.scheduledDate)) return false;
    if (moveLeg.isCrossBorder) return false;
    if (moveLeg.isHawaii) return false;
    return !moveLeg.isCityService;
  };

  const isSchedulableButNotOnline = () =>
    !moveLeg.isTransitLeg && (moveLeg.isCrossBorder || moveLeg.isHawaii || moveLeg.isCityService);

  const toggleEditMoveLegOn = () => {
    editMoveLegScheduling(moveLeg);
  };

  const toggleEdit = () => {
    setSelectedOrderId(order.orderId);

    if (
      serviceCountdownTypeForMoveLegType(moveLeg.moveLegType) === 'PICKUP' &&
      isRedesignedSchedulingPickupEnabled()
    ) {
      setIsPickupPanelOpen(true);
    } else if (
      isRedesignedSchedulingEnabled() &&
      serviceCountdownTypeForMoveLegType(moveLeg.moveLegType) === 'VISIT'
    ) {
      setIsVisitPanelOpen(true);
    } else if (
      isRedesignedSchedulingEnabled() &&
      serviceCountdownTypeForMoveLegType(moveLeg.moveLegType) === 'DROPOFF'
    ) {
      setIsDropOffDrawerOpen(true);
    } else {
      toggleEditMoveLegOn();
    }
  };

  const dateLabels = getDateLabels(moveLeg.moveLegType);

  function getFirstDateValue() {
    if (moveLeg.moveLegType === 'VISIT_CONTAINER') {
      return moveLeg.arrivalDate;
    }
    return moveLeg.scheduledDate;
  }

  const getSecondDateValue = () => {
    if (moveLeg.moveLegType === 'VISIT_CONTAINER') {
      return moveLeg.moveDate;
    }
    if (moveLeg.scheduledDate) {
      return addDays(moveLeg.scheduledDate, moveLeg.transitDays);
    }
    return undefined;
  };

  const shouldShowVisitContainerSection =
    isScheduledContainerVisit && moveLeg.isSchedulableOnline && !isSchedulingMoveLeg;

  if (moveLeg.isTransitLeg) return <InTransitMoveLeg />;

  function triggerGtmForSuccess(isSuccess: boolean, gtmRequest: GtmScheduleType) {
    if (isSuccess) {
      const eventType = isEditingGtmEvent ? 'success_edit_schedule' : 'success_schedule';
      gtmEvents.pushMoveLegScheduleEvent(eventType, gtmRequest);
    } else {
      const eventType = isEditingGtmEvent ? 'submit_edit_schedule' : 'submit_schedule';
      gtmEvents.pushMoveLegScheduleEvent(eventType, gtmRequest);
    }
  }

  const handleVisitContainerEdit = () => {
    setIsEditingGtmEvent(true);
    setIsUpdateMoveLeg(true);
    startEditSchedule(gtmSchedulePayLoad);
    toggleEdit();
  };

  const handlePriceDifference = (
    salesforceQuoteId: string,
    gtmSchedule: GtmScheduleType,
    onSuccess = () => {},
    onError = (_error: unknown) => {}
  ) => {
    const applyQuoteToOrderRequest: ApplyQuoteToOrderRequest = {
      sfQuoteId: salesforceQuoteId
    };

    applyQuoteToOrder.mutate(applyQuoteToOrderRequest, {
      onSuccess: () => {
        triggerGtmForSuccess(true, gtmSchedule);
        setPriceDifferenceResponse(null);
        refetch(() => {
          onSuccess();
        });
      },
      onError: (error: unknown) => {
        const eventType = isEditingGtmEvent ? 'submit_edit_schedule' : 'submit_schedule';
        gtmEvents.errorEvent(
          createGtmErrorRequest(GA_GENERIC_BACKEND_MESSAGE, eventType, 'backend')
        );
        if (isStaleDataError(error)) {
          refetchOnFailure();
        } else {
          onError(error);
        }
      }
    });
  };

  const sendUpdateMovelegRequest = (
    request: UpdateMoveLegRequest,
    gtmSchedule: GtmScheduleType,
    onSuccess = () => {},
    onError = (_error: unknown) => {}
  ) => {
    updateMoveLeg.mutate(request, {
      onSuccess: (response) => {
        triggerGtmForSuccess(true, gtmSchedule);
        if (response.quoteId) {
          setPriceDifferenceResponse(response);
          return;
        }
        setPriceDifferenceResponse(null);
        refetch(() => {
          onSuccess();
        });
      },
      onError: (error: unknown) => {
        const eventType = isEditingGtmEvent ? 'submit_edit_schedule' : 'submit_schedule';
        gtmEvents.errorEvent(
          createGtmErrorRequest(GA_GENERIC_BACKEND_MESSAGE, eventType, 'backend')
        );
        if (isStaleDataError(error)) {
          refetchOnFailure();
        } else {
          onError(error);
        }
      }
    });
  };

  const onScheduleSave = async ({
    containerPlacement,
    onSuccess,
    onError
  }: OnMoveLegUpdateSaveProps) => {
    addressState.displayErrorForAllFields();
    // don't save if selected date is not changed, unless its move or redelivery
    if (isMoveOrRedelivery(moveLeg)) {
      if (!addressState.isValid() || !dateState.selectedDateIsValid() || containerPlacement == null)
        return;
    } else if (!dateState.selectedDateIsValid() || !dateState.selectedDateIsDifferent()) return;
    const request = createUpdateMoveLegRequest({
      order,
      container,
      moveLeg,
      selectedDate: dateState.selectedDate!,
      serviceAddress: addressState.value,
      completedContainerPlacement: containerPlacement,
      currentlySelectedQuote: currentlySelectedQuote,
      priceDifferenceResponse: priceDifferenceResponse,
      isCancelLeg: isCancelling,
      isUpdating: isUpdateMoveLeg,
      isIfOpenCalendar: false
    });
    const gtmSchedule = createGtmScheduleType(order, container, moveLeg, request);
    triggerGtmForSuccess(false, gtmSchedule);

    if (priceDifferenceResponse?.quoteId) {
      handlePriceDifference(priceDifferenceResponse.quoteId, gtmSchedule, onSuccess, onError);
    } else {
      sendUpdateMovelegRequest(request, gtmSchedule, onSuccess, onError);
    }
  };

  return (
    <Grid container {...styles.moveLegSection} data-testid={`move-leg-${moveLeg.moveLegId}`}>
      <Grid item>
        <ContainerProgressLine
          variant={scheduledStatusToProgressLineVariant(moveLeg.scheduledStatus)}
          isUpNext={moveLeg.isUpNext}
          isFinal={isLastMoveLeg}
          dataTestId={`progress-line-${moveLeg.moveLegId}`}
        />
      </Grid>
      <Grid item xs {...styles.mainBody}>
        <Grid container {...styles.titleContainer} data-testid="move-leg-title-container">
          <Grid container item xs {...styles.titleWithEdit}>
            <Typography
              color="inherit"
              variant="h4"
              {...styles.titleText}
              data-testid="move-leg-title">
              {title}
            </Typography>
          </Grid>
          {shouldRenderEditButton() && orderModEnabled && (
            <EditButton
              dataTestId="move-leg"
              onClick={() => {
                setIsEditingGtmEvent(true);
                startEditSchedule(gtmSchedulePayLoad);
                toggleEdit();
              }}
              disabled={isSaving}
            />
          )}
        </Grid>
        {isSchedulableButNotOnline() && <ChatToScheduleComponent />}
        <ServiceCountdownAlert moveLeg={moveLeg} />
        {showInitialDeliveryAlert && (
          <ReviewInitialDeliveryAlert
            hideAlertCallback={hideInitialDeliveryAlert}
            currentlySelectedQuote={currentlySelectedQuote}
          />
        )}
        <Grid container {...styles.detailsContainer}>
          <Grid container {...styles.detailsContainer}>
            <DateComponent
              firstDateLabel={dateLabels.firstDateLabel}
              firstDateValue={getFirstDateValue()}
              secondDateLabel={dateLabels.secondDateLabel}
              secondDateValue={getSecondDateValue()}
            />
            <AddressComponent moveLeg={moveLeg} address={formatAddress(moveLeg.displayAddress)} />
          </Grid>
          {moveLeg.moveLegType === 'SELF_FINAL_PICKUP' && moveLeg.scheduledDate && (
            <PickupWindow etaWindow={moveLeg.eta} />
          )}
          {isSchedulingMoveLeg ? (
            <ScheduleMoveLeg
              onStopScheduling={stopMoveLegScheduling}
              isEditingGtmEvent={isEditingGtmEvent}
              currentlySelectedQuote={currentlySelectedQuote}
            />
          ) : (
            shouldRenderScheduledButton() &&
            orderModEnabled && (
              <ScheduleMoveLegButton
                moveLeg={moveLeg}
                onClick={() => {
                  setIsUpdateMoveLeg(false);
                  startSchedule(gtmSchedulePayLoad);
                  toggleEdit();
                }}
                disabled={isSaving}
              />
            )
          )}
          {(isCallToScheduleLeg() || !orderModEnabled) && <ChatToScheduleComponent />}
        </Grid>
        {/* // TODO Test that it should be schedulable online */}
        {shouldShowVisitContainerSection && (
          <VisitContainerSection
            moveLeg={moveLeg}
            currentlySelectedQuote={currentlySelectedQuote}
            onEdit={handleVisitContainerEdit}
          />
        )}

        {isLastRenderedMoveLeg ? (
          <Grid container {...styles.dividerContainer}>
            <Divider />
          </Grid>
        ) : (
          <Grid container style={{ height: '24px' }} />
        )}

        <ManagePickupPanel
          isOpen={isPickupPanelOpen}
          onClose={() => {
            setIsPickupPanelOpen(false);
          }}
          onSave={onScheduleSave}
        />
        <ManageVisitPanel
          isOpen={isVisitPanelOpen}
          onClose={() => {
            setIsVisitPanelOpen(false);
          }}
          onSave={onScheduleSave}
        />
        <ManageDropOffContainer
          isOpen={isDropOffDrawerOpen}
          onClose={() => {
            setIsDropOffDrawerOpen(false);
          }}
        />
      </Grid>
    </Grid>
  );
};

const moveLegStyles = (isMobile: boolean) => ({
  ...sharedMoveLegStyles(isMobile),
  titleWithEdit: {
    sx: {
      flexDirection: 'row',
      alignItems: 'center'
    }
  }
});
