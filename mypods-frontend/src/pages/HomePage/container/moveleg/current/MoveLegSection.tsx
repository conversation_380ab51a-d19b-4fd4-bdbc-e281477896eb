import { Grid, Typography, useMediaQuery } from '@mui/material';
import React, { useMemo, useState } from 'react';
import Divider from '@mui/material/Divider';
import { addDays } from 'date-fns';
import { ServiceCountdownAlert } from '../../serviceCountdown/ServiceCountdownAlert';
import { DateComponent } from '../DateComponent';
import { formatAddress } from '../../../../../networkRequests/responseEntities/CustomerEntities';
import { ContainerProgressLine, ProgressLineVariant } from '../../ContainerProgressLine';
import { ScheduleMoveLegButton } from './ScheduleMoveLegButton';
import {
  ApplyQuoteToOrderRequest,
  ContainerPlacement,
  ScheduledStatus,
  serviceCountdownTypeForMoveLegType,
  UpdateMoveLegRequest
} from '../../../../../domain/OrderEntities';
import { ScheduleMoveLeg } from '../../scheduling/ScheduleMoveLeg';
import { theme } from '../../../../../PodsTheme';
import { InTransitMoveLeg } from './InTransitMoveLeg';
import { AddressComponent } from '../AddressComponent';
import { EditButton } from '../../../../../components/buttons/EditButton';
import { formatDate, isWithin24Hours } from '../../../../../helpers/dateHelpers';
import { getDateLabels } from '../../../../../locales/TranslationConstants';
import { VisitContainerSection } from '../VisitContainerSection';
import useMoveLegContext from '../MoveLegContext';
import { sharedMoveLegStyles } from '../sharedMoveLegStyles';
import useContainerContext from '../../../../../context/ContainerContext';
import { ReviewInitialDeliveryAlert } from '../../../../../components/alert/ReviewInitialDeliveryAlert';
import useSingleOrderContext from '../../../../../context/SingleOrderContext';
import { useGtmEvents } from '../../../../../config/google/useGtmEvents';
import { PickupWindow } from '../PickupWindow';
import {
  ORDER_MODIFICATION_ENABLED,
  REDESIGNED_SCHEDULING,
  useFeatureFlags
} from '../../../../../helpers/useFeatureFlags';
import { ChatToScheduleComponent } from '../ChatToScheduleComponent';
import { ManagePickupPanel } from '../../../Scheduling/ManagePickupPanel';
import { ManageVisitContainer } from '../../../Scheduling/ManageVisitContainer';
import { ManageDropOffContainer } from '../../../Scheduling/ManageDropOffContainer';
import useOrdersContext from '../../../../../context/OrdersContext';
import { UpdateMoveLegResponse } from '../../../../../networkRequests/responseEntities/OrderAPIEntities';
import { GtmScheduleType } from '../../../../../config/google/GoogleEntities';
import {
  createGtmErrorRequest,
  GA_GENERIC_BACKEND_MESSAGE
} from '../../../../../config/google/googleAnalyticsUtils';
import { isStaleDataError } from '../../../utils';
import { getVisitorId } from '../../../../../config/getVisitorId';
import { useApplyQuoteToOrder } from '../../../../../networkRequests/mutations/useApplyQuoteToOrder';
import { useUpdateMoveLeg } from '../../../../../networkRequests/mutations/useUpdateMoveLeg';

export const MoveLegSection = () => {
  const { moveLeg, isLastMoveLeg, isLastRenderedMoveLeg, title } = useMoveLegContext();
  const { refetch, refetchOnFailure } = useOrdersContext();
  const [priceDifferenceResponse, setPriceDifferenceResponse] =
    useState<UpdateMoveLegResponse | null>();
  const { order, container } = useContainerContext();
  const {
    scheduling: { dateState, addressState }
  } = useMoveLegContext();
  const {
    currentlySelectedQuote,
    moveLegScheduling: {
      currentlySelectedMoveLeg,
      setSelectedOrderId,
      editMoveLegScheduling,
      stopMoveLegScheduling,
      isSaving,
      isCancelling
    }
  } = useSingleOrderContext();
  const gtmEvents = useGtmEvents();
  const { startSchedule, startEditSchedule } = gtmEvents;
  const applyQuoteToOrder = useApplyQuoteToOrder();
  const updateMoveLeg = useUpdateMoveLeg();
  const { isReady, isOrderModEnabled, isRedesignedSchedulingEnabled } = useFeatureFlags([
    ORDER_MODIFICATION_ENABLED,
    REDESIGNED_SCHEDULING
  ]);
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = moveLegStyles(isMobile);
  // TODO: panel management can go into the context like the save / edit states.
  const [isPickupPanelOpen, setIsPickupPanelOpen] = useState(false);
  const [isVisitDrawerOpen, setIsVisitDrawerOpen] = useState(false);
  const [isDropOffDrawerOpen, setIsDropOffDrawerOpen] = useState(false);
  const [isEditingGtmEvent, setIsEditingGtmEvent] = useState<boolean>(false);
  const [showInitialDeliveryAlert, setShowInitialDeliveryAlert] = useState<boolean>(
    moveLeg.moveLegType === 'INITIAL_DELIVERY' && !order.initialDeliveryPlacementIsReviewed
  );

  const orderModEnabled = useMemo(
    () => isReady && isOrderModEnabled(),
    [isReady, isOrderModEnabled]
  );

  const gtmSchedulePayLoad = {
    transactionId: order.orderId,
    moveLegId: moveLeg.moveLegId,
    moveLegType: moveLeg.moveLegType.toString(),
    containerId: container.containerId
  };

  const scheduledStatusToProgressLineVariant = (
    scheduledStatus: ScheduledStatus
  ): ProgressLineVariant => {
    if (scheduledStatus === 'UNSCHEDULED') {
      return 'FADED_DASHED';
    }
    return 'DASHED';
  };

  // -- move leg helpers --
  function isCallToScheduleLeg(): boolean {
    return (
      moveLeg.moveLegType === 'WAREHOUSE_TO_WAREHOUSE' &&
      moveLeg.scheduledStatus === 'UNSCHEDULED' &&
      !isSchedulableButNotOnline()
    );
  }

  const isUnscheduledContainerVisit =
    moveLeg.moveLegType === 'VISIT_CONTAINER' && moveLeg.containerVisitDate == null;
  const isScheduledContainerVisit =
    moveLeg.moveLegType === 'VISIT_CONTAINER' && moveLeg.containerVisitDate != null;
  const isSchedulingMoveLeg = currentlySelectedMoveLeg === moveLeg && !isCancelling;

  const isMoveOrRedelivery = () =>
    moveLeg.moveLegType === 'REDELIVERY' || moveLeg.moveLegType === 'MOVE';

  const hideInitialDeliveryAlert = () => {
    setShowInitialDeliveryAlert(false);
  };

  const shouldRenderScheduledButton = () => {
    if (!moveLeg.isSchedulableOnline) return false;
    if (moveLeg.isCityService) return false;
    if (moveLeg.isCrossBorder) return false;
    if (moveLeg.isHawaii) return false;
    if (isUnscheduledContainerVisit) return true;
    return moveLeg.scheduledStatus === 'UNSCHEDULED';
  };

  const shouldRenderEditButton = () => {
    if (isSchedulingMoveLeg) return false;
    if (!moveLeg.isSchedulableOnline) return false;
    if (moveLeg.moveLegType === 'VISIT_CONTAINER') return false;
    if (moveLeg.scheduledStatus !== 'FUTURE') return false;
    if (isWithin24Hours(moveLeg.scheduledDate)) return false;
    if (moveLeg.isCrossBorder) return false;
    if (moveLeg.isHawaii) return false;
    return !moveLeg.isCityService;
  };

  const isSchedulableButNotOnline = () => {
    if (moveLeg.isTransitLeg) return false;
    if (moveLeg.isCrossBorder) return true;
    if (moveLeg.isHawaii) return true;
    return moveLeg.isCityService;
  };

  // -- schedule & edit behavior --
  const toggleEditMoveLegOn = () => {
    setSelectedOrderId(order.orderId);
    editMoveLegScheduling(moveLeg);
  };

  const toggleMoveLegDrawer = () => {
    if (serviceCountdownTypeForMoveLegType(moveLeg.moveLegType) === 'VISIT') {
      setIsVisitDrawerOpen(true);
    } else if (serviceCountdownTypeForMoveLegType(moveLeg.moveLegType) === 'DROPOFF') {
      setIsDropOffDrawerOpen(true);
    } else if (serviceCountdownTypeForMoveLegType(moveLeg.moveLegType) === 'PICKUP') {
      setIsPickupPanelOpen(true);
    } else {
      toggleEditMoveLegOn();
    }
  };

  const toggleEdit = () => {
    setIsEditingGtmEvent(true);
    setSelectedOrderId(order.orderId);
    if (isRedesignedSchedulingEnabled()) {
      toggleMoveLegDrawer();
    } else {
      toggleEditMoveLegOn();
    }
  };

  const onPickupPanelClose = () => {
    setIsPickupPanelOpen(false);
    // TODO: clear the date out, but date state can't be null.
  };

  // -- dates --
  const dateLabels = getDateLabels(moveLeg.moveLegType);

  function getFirstDateValue() {
    if (moveLeg.moveLegType === 'VISIT_CONTAINER') {
      return moveLeg.arrivalDate;
    }
    return moveLeg.scheduledDate;
  }

  const getSecondDateValue = () => {
    if (moveLeg.moveLegType === 'VISIT_CONTAINER') {
      return moveLeg.moveDate;
    }
    if (moveLeg.scheduledDate) {
      return addDays(moveLeg.scheduledDate, moveLeg.transitDays);
    }
    return undefined;
  };

  const shouldShowVisitContainerSection =
    isScheduledContainerVisit && moveLeg.isSchedulableOnline && !isSchedulingMoveLeg;

  if (moveLeg.isTransitLeg) return <InTransitMoveLeg />;

  // -- save order modification --
  const createUpdateMoveLegRequest = (
    completedContainerPlacement?: ContainerPlacement
  ): UpdateMoveLegRequest => {
    const request: UpdateMoveLegRequest = {
      orderId: order.orderId,
      containerOrderId: container.containerOrderId,
      moveLegId: moveLeg.moveLegId,
      moveLegType: moveLeg.moveLegType,
      requestedDate: formatDate(dateState.selectedDate!, 'yyyy-MM-dd'),
      transitDays: moveLeg.transitDays,
      // Likely used to cancel a visit to a container warehouse
      isCancelLeg: false,
      containerPlacement: completedContainerPlacement,
      locationFields: {
        zip: addressState.value.postalCode,
        moveLegType: moveLeg.moveLegType,
        orderType: order.orderType,
        siteIdentity: moveLeg.siteIdentity,
        isIfOpenCalendar: false,
        custTrackingId: getVisitorId()
      }
    };
    if (isMoveOrRedelivery()) {
      request.serviceAddress = addressState.value;
      request.containerPlacement = completedContainerPlacement!!;
    }
    if (priceDifferenceResponse) request.quoteId = priceDifferenceResponse.quoteId;
    return request;
  };

  const createGtmScheduleType = (request: UpdateMoveLegRequest): GtmScheduleType => ({
    transactionId: order.orderId,
    containerId: container.containerId,
    moveLegId: moveLeg.moveLegId,
    moveLegType: moveLeg.moveLegType,
    addressChanged: hasAddressChanged(request),
    deliverySiteType: request.containerPlacement?.siteType.toString(),
    containerPlacement: request.containerPlacement?.placement.toString(),
    pavedSurface: request.containerPlacement?.isPavedSurface,
    gated: false, // TODO: This will need to be updated once we introduce feature
    optionalNotes: !!(
      request.containerPlacement && request.containerPlacement.driverNotes.length > 0
    )
  });

  function triggerGtmForSuccess(isSuccess: boolean, gtmRequest: GtmScheduleType) {
    if (isSuccess) {
      const eventType = isEditingGtmEvent ? 'success_edit_schedule' : 'success_schedule';
      gtmEvents.pushMoveLegScheduleEvent(eventType, gtmRequest);
    } else {
      const eventType = isEditingGtmEvent ? 'submit_edit_schedule' : 'submit_schedule';
      gtmEvents.pushMoveLegScheduleEvent(eventType, gtmRequest);
    }
  }

  const hasAddressChanged = (request: UpdateMoveLegRequest) => {
    if (request.serviceAddress) {
      if (moveLeg.displayAddress.address1 == null) return true;
      return !(
        moveLeg.displayAddress.address1.toLowerCase() ===
        request.serviceAddress?.address1.toLowerCase()
      );
    }
    return false;
  };

  const handlePriceDifference = (
    salesforceQuoteId: string,
    gtmRequest: GtmScheduleType,
    onSuccess = () => {},
    onError = () => {}
  ) => {
    const applyQuoteToOrderRequest: ApplyQuoteToOrderRequest = {
      sfQuoteId: salesforceQuoteId
    };

    applyQuoteToOrder.mutate(applyQuoteToOrderRequest, {
      onSuccess: () => {
        triggerGtmForSuccess(true, gtmRequest);
        setPriceDifferenceResponse(null);
        refetch(() => {
          onSuccess();
        });
      },
      onError: (error: unknown) => {
        const eventType = isEditingGtmEvent ? 'submit_edit_schedule' : 'submit_schedule';
        gtmEvents.errorEvent(
          createGtmErrorRequest(GA_GENERIC_BACKEND_MESSAGE, eventType, 'backend')
        );
        if (isStaleDataError(error)) {
          refetchOnFailure();
        } else {
          onError();
        }
      }
    });
  };

  const sendUpdateMovelegRequest = (
    request: UpdateMoveLegRequest,
    gtmRequest: GtmScheduleType,
    onSuccess = () => {},
    onError = () => {}
  ) => {
    updateMoveLeg.mutate(request, {
      onSuccess: (response) => {
        triggerGtmForSuccess(true, gtmRequest);
        if (response.quoteId) {
          setPriceDifferenceResponse(response);
          return;
        }
        setPriceDifferenceResponse(null);
        refetch(() => {
          onSuccess();
        });
      },
      onError: (error: unknown) => {
        const eventType = isEditingGtmEvent ? 'submit_edit_schedule' : 'submit_schedule';
        gtmEvents.errorEvent(
          createGtmErrorRequest(GA_GENERIC_BACKEND_MESSAGE, eventType, 'backend')
        );
        if (isStaleDataError(error)) {
          refetchOnFailure();
        } else {
          onError();
        }
      }
    });
  };

  const onScheduleSave = async (
    containerPlacement: ContainerPlacement | undefined,
    onSuccess: () => void,
    onError: () => void
  ) => {
    addressState.displayErrorForAllFields();
    // don't save is selected date is not changed, unless its move or redelivery
    if (isMoveOrRedelivery()) {
      if (!addressState.isValid() || !dateState.selectedDateIsValid() || containerPlacement == null)
        return;
    } else if (!dateState.selectedDateIsValid() || !dateState.selectedDateIsDifferent()) return;

    const request = createUpdateMoveLegRequest(containerPlacement);
    const gtmSchedule = createGtmScheduleType(request);
    triggerGtmForSuccess(false, gtmSchedule);

    if (priceDifferenceResponse?.quoteId) {
      handlePriceDifference(priceDifferenceResponse.quoteId, gtmSchedule, onSuccess, onError);
    } else {
      sendUpdateMovelegRequest(request, gtmSchedule, onSuccess, onError);
    }
  };

  return (
    <Grid container {...styles.moveLegSection} data-testid={`move-leg-${moveLeg.moveLegId}`}>
      <Grid item>
        <ContainerProgressLine
          variant={scheduledStatusToProgressLineVariant(moveLeg.scheduledStatus)}
          isUpNext={moveLeg.isUpNext}
          isFinal={isLastMoveLeg}
          dataTestId={`progress-line-${moveLeg.moveLegId}`}
        />
      </Grid>
      <Grid item xs {...styles.mainBody}>
        <Grid container {...styles.titleContainer} data-testid="move-leg-title-container">
          <Grid container item xs {...styles.titleWithEdit}>
            <Typography
              color="inherit"
              variant="h4"
              {...styles.titleText}
              data-testid="move-leg-title">
              {title}
            </Typography>
          </Grid>
          {shouldRenderEditButton() && orderModEnabled && (
            <EditButton
              dataTestId="move-leg"
              onClick={() => {
                startEditSchedule(gtmSchedulePayLoad);
                toggleEdit();
              }}
              disabled={isSaving}
            />
          )}
        </Grid>
        {isSchedulableButNotOnline() && <ChatToScheduleComponent />}
        <ServiceCountdownAlert moveLeg={moveLeg} />
        {showInitialDeliveryAlert && (
          <ReviewInitialDeliveryAlert
            hideAlertCallback={hideInitialDeliveryAlert}
            currentlySelectedQuote={currentlySelectedQuote}
          />
        )}
        <Grid container {...styles.detailsContainer}>
          <Grid container {...styles.detailsContainer}>
            <DateComponent
              firstDateLabel={dateLabels.firstDateLabel}
              firstDateValue={getFirstDateValue()}
              secondDateLabel={dateLabels.secondDateLabel}
              secondDateValue={getSecondDateValue()}
            />
            <AddressComponent moveLeg={moveLeg} address={formatAddress(moveLeg.displayAddress)} />
          </Grid>
          {moveLeg.moveLegType === 'SELF_FINAL_PICKUP' && moveLeg.scheduledDate && (
            <PickupWindow etaWindow={moveLeg.eta} />
          )}
          {isSchedulingMoveLeg ? (
            <ScheduleMoveLeg
              onStopScheduling={stopMoveLegScheduling}
              isEditingGtmEvent={isEditingGtmEvent}
              currentlySelectedQuote={currentlySelectedQuote}
            />
          ) : (
            shouldRenderScheduledButton() &&
            orderModEnabled && (
              <ScheduleMoveLegButton
                moveLeg={moveLeg}
                onClick={() => {
                  startSchedule(gtmSchedulePayLoad);
                  toggleEdit();
                }}
                disabled={isSaving}
              />
            )
          )}
          {(isCallToScheduleLeg() || !orderModEnabled) && <ChatToScheduleComponent />}
        </Grid>
        {/* // TODO Test that it should be schedulable online */}
        {shouldShowVisitContainerSection && (
          <VisitContainerSection
            moveLeg={moveLeg}
            currentlySelectedQuote={currentlySelectedQuote}
            onEdit={() => {
              setIsEditingGtmEvent(true);
              startEditSchedule(gtmSchedulePayLoad);
              toggleEditMoveLegOn();
            }}
          />
        )}

        {isLastRenderedMoveLeg ? (
          <Grid container {...styles.dividerContainer}>
            <Divider />
          </Grid>
        ) : (
          <Grid container style={{ height: '24px' }} />
        )}
        <ManagePickupPanel
          isOpen={isPickupPanelOpen}
          onClose={onPickupPanelClose}
          onSave={onScheduleSave}
        />
        <ManageVisitContainer
          isOpen={isVisitDrawerOpen}
          onClose={() => setIsVisitDrawerOpen(false)}
          moveLeg={moveLeg}
        />
        <ManageDropOffContainer
          isOpen={isDropOffDrawerOpen}
          onClose={() => setIsDropOffDrawerOpen(false)}
          moveLeg={moveLeg}
        />
      </Grid>
    </Grid>
  );
};

const moveLegStyles = (isMobile: boolean) => ({
  ...sharedMoveLegStyles(isMobile),
  titleWithEdit: {
    sx: {
      flexDirection: 'row',
      alignItems: 'center'
    }
  }
});
