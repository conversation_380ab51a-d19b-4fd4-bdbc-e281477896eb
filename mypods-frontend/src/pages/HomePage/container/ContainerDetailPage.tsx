import React, { useMemo } from 'react';
import { useLocation, useNavigate, useParams } from 'react-router';
import { FlexBox } from 'pods-component-library/src/components/Layout/FlexBox';
import { css } from 'pods-component-library/styled-system/css';
import { ContainerProvider } from '../../../context/ContainerContext';
import { SingleOrderProvider } from '../../../context/SingleOrderContext';
import { ContainerTile } from './ContainerTile';
import { ROUTES } from '../../../Routes';
import { Container, Order } from '../../../domain/OrderEntities';
import { useGetCustomerOrders } from '../../../networkRequests/queries/useGetCustomerOrders';

type NavState = { container: Container; order: Order } | null;

export const ContainerDetailPage: React.FC = () => {
  const { containerId } = useParams<{ containerId: string }>();
  const location = useLocation();
  const navigate = useNavigate();

  const navState = (location.state as NavState) ?? null;

  const { customerOrders, isLoading, isError } = useGetCustomerOrders();

  const { container, order } = useMemo(() => {
    if (customerOrders && containerId) {
      const result = customerOrders.reduce<{ container: Container | null; order: Order | null }>(
        (acc, currentOrder) => {
          if (acc.container) return acc;

          const foundContainer = currentOrder.containers?.find(
            (c) => c.containerId === containerId || c.containerOrderId === containerId
          );

          if (foundContainer) {
            return { container: foundContainer, order: currentOrder };
          }

          return acc;
        },
        { container: null, order: null }
      );

      if (result.container && result.order) {
        return result;
      }
    }

    if (navState?.container && navState?.order && !customerOrders) {
      return navState;
    }

    return { container: null, order: null };
  }, [customerOrders, containerId, navState]);

  if (isError || !container || !order) {
    if (!isLoading) {
      navigate(ROUTES.HOME);
    }
    return null;
  }

  return (
    <FlexBox css={stylesheet.wrapper} data-testid="container-detail-page">
      <FlexBox css={stylesheet.content}>
        <SingleOrderProvider state={{ order }}>
          <ContainerProvider state={{ container, order }}>
            <ContainerTile />
          </ContainerProvider>
        </SingleOrderProvider>
      </FlexBox>
    </FlexBox>
  );
};

const stylesheet = {
  wrapper: css.raw({
    width: '100%',
    flexDirection: 'column',
    gap: '12px',
    justifyContent: 'center',
    alignItems: 'center'
  }),
  content: css.raw({
    maxWidth: '688px',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    paddingY: '1rem',
    paddingX: '1rem',
    boxSizing: 'border-box',
    xsToSm: {
      paddingX: '1rem'
    }
  })
};
