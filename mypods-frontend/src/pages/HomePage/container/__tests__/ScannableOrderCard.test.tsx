import { createOrder, createRefreshSessionClaims } from '../../../../testUtils/MyPodsFactories';
import { screen } from '@testing-library/react';
import {
  renderWithPoetProvidersAndState,
  runPendingPromises
} from '../../../../testUtils/RenderHelpers';
import { ScannableOrderCard } from '../ScannableOrderCard';
import { mockRefreshSession } from '../../../../../setupTests';
import { TranslationKeys } from '../../../../locales/TranslationKeys';

describe('ScannableOrderCard', () => {
  const order = createOrder();
  const sessionClaims = createRefreshSessionClaims();

  const renderPage = async () => {
    mockRefreshSession.mockResolvedValue(sessionClaims);
    const result = renderWithPoetProvidersAndState(
      <ScannableOrderCard order={order}>
        <div>Just a test</div>
      </ScannableOrderCard>
    );
    await runPendingPromises();
    return result;
  };

  it('displays order number', async () => {
    await renderPage();

    expect(
      await screen.findByText(TranslationKeys.HomePage.Disclosure.Summary.ORDER_NUMBER)
    ).toBeInTheDocument();
  });
});
