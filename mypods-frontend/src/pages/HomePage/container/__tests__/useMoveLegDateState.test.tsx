import { QueryClientProvider } from '@tanstack/react-query';
import { renderHook } from '@testing-library/react';
import { Suspense } from 'react';
import { runPendingPromises, testQueryClient } from '../../../../testUtils/RenderHelpers';
import { useMoveLegDateState } from '../scheduling/useMoveLegDateState';
import { MoveLeg, OrderType } from '../../../../domain/OrderEntities';
import { createMoveLeg } from '../../../../testUtils/MyPodsFactories';
import { it } from 'vitest';
import addYears from 'date-fns/addYears';
import { addDays, addWeeks, format, startOfDay } from 'date-fns';
import { mockGetContainerAvailability } from '../../../../../setupTests';
import { ContainerContext, IContainerContextState } from '../../../../context/ContainerContext';

describe('useMoveLegDateState', () => {
  let moveLeg: MoveLeg = createMoveLeg();
  const containerState: IContainerContextState = {
    container: {
      containerId: '',
      containerSize: '8',
      moveLegs: []
    },
    order: {
      orderId: '',
      quoteId: 0,
      orderType: OrderType.LOCAL,
      containers: [],
      orderDate: undefined,
      price: 0,
      initialDeliveryPlacementIsReviewed: true
    }
  };

  const renderUseMoveLegDateState = async () => {
    // @ts-ignore
    const wrapper = ({ children }) => (
      <ContainerContext.Provider value={containerState}>
        <QueryClientProvider client={testQueryClient()}>
          <Suspense fallback={'SUSPENSE_FALLBACK'}>{children}</Suspense>
        </QueryClientProvider>
      </ContainerContext.Provider>
    );
    const { result } = renderHook(() => useMoveLegDateState(moveLeg), { wrapper });
    await runPendingPromises();
    return result.current;
  };

  it('should return the correct initial state', async () => {
    const state = await renderUseMoveLegDateState();

    expect(state.selectedDate).toEqual(moveLeg.scheduledDate);
    expect(state.containerAvailabilityPending).toBe(false);
    expect(state.selectedDateIsValid()).toBe(true);
    expect(state.selectedDateIsDifferent()).toBe(false);
  });

  describe('getCalendarStartDate', () => {
    it('should return the firstAvailableDate, when it is after the defaultEarliestAvailability', async () => {
      moveLeg = createMoveLeg({
        firstAvailableDate: addYears(new Date(), 1)
      });
      const state = await renderUseMoveLegDateState();
      expect(state.getCalendarStartDate()).toEqual(moveLeg.firstAvailableDate);
    });

    it('should return the defaultEarliestAvailability (the next business day), when it is after the firstAvailableDate and earlier than 5', async () => {
      vi.setSystemTime(new Date(2025, 0, 1, 1));

      moveLeg = createMoveLeg({
        firstAvailableDate: addDays(startOfDay(new Date()), -1)
      });

      const state = await renderUseMoveLegDateState();

      expect(state.getCalendarStartDate()).toEqual(addDays(startOfDay(new Date()), 1));
    });

    it('should return the defaultEarliestAvailability (the next business day), when it is after the firstAvailableDate and later than 5', async () => {
      vi.setSystemTime(new Date(2025, 0, 1, 23, 1));
      moveLeg = createMoveLeg({
        firstAvailableDate: addDays(startOfDay(new Date()), -1)
      });

      const state = await renderUseMoveLegDateState();

      expect(state.getCalendarStartDate()).toEqual(addDays(startOfDay(new Date()), 2));
    });
  });

  describe('getCalendarStartDateWithAvailability', () => {
    let today: Date;
    let todayFormatted: string;
    let tomorrow: Date;
    let twoDaysFromNow: Date;
    let threeDaysFromNow: Date;
    let tomorrowFormatted: string;
    let twoDaysFromNowFormatted: string;
    let threeDaysFromNowFormatted: string;

    beforeEach(() => {
      vi.setSystemTime(new Date(2025, 0, 1, 1));
      today = startOfDay(new Date('2025-01-01T00:00:00.000Z'));
      tomorrow = addDays(today, 1);
      twoDaysFromNow = addDays(today, 2);
      threeDaysFromNow = addDays(today, 3);
      todayFormatted = format(today, 'yyyy-MM-dd');
      tomorrowFormatted = format(tomorrow, 'yyyy-MM-dd');
      twoDaysFromNowFormatted = format(twoDaysFromNow, 'yyyy-MM-dd');
      threeDaysFromNowFormatted = format(threeDaysFromNow, 'yyyy-MM-dd');
    });

    it('should return the result of getCalendarStartDate, when there is no availability', async () => {
      moveLeg = createMoveLeg({
        firstAvailableDate: addWeeks(new Date(), 1)
      });
      const state = await renderUseMoveLegDateState();
      expect(state.getCalendarStartDateWithAvailability()).toEqual(moveLeg.firstAvailableDate);
    });

    it('should return the first available date, when there is availability', async () => {
      containerState.container.moveLegs = [];
      moveLeg = createMoveLeg({
        firstAvailableDate: addDays(today, -1),
        lastAvailableDate: addDays(today, 4)
      });
      mockGetContainerAvailability.mockResolvedValue({
        eightFootAvailability: [
          { date: todayFormatted, isAvailable: false },
          { date: tomorrowFormatted, isAvailable: false },
          { date: twoDaysFromNowFormatted, isAvailable: false },
          { date: threeDaysFromNowFormatted, isAvailable: true }
        ],
        twelveFootAvailability: [],
        sixteenFootAvailability: []
      });
      const state = await renderUseMoveLegDateState();

      state.addContainerAvailabilitiesFor3Months(today);
      await runPendingPromises();

      expect(state.getCalendarStartDateWithAvailability()).toEqual(threeDaysFromNow);
    });

    it('should return the skip dates of scheduled move legs', async () => {
      containerState.container.moveLegs = [createMoveLeg({ scheduledDate: twoDaysFromNow })];
      moveLeg = createMoveLeg({
        firstAvailableDate: addDays(today, -1),
        lastAvailableDate: addDays(today, 4)
      });
      mockGetContainerAvailability.mockResolvedValue({
        eightFootAvailability: [
          { date: todayFormatted, isAvailable: false },
          { date: tomorrowFormatted, isAvailable: false },
          { date: twoDaysFromNowFormatted, isAvailable: true },
          { date: threeDaysFromNowFormatted, isAvailable: true }
        ],
        twelveFootAvailability: [],
        sixteenFootAvailability: []
      });
      const state = await renderUseMoveLegDateState();

      state.addContainerAvailabilitiesFor3Months(today);
      await runPendingPromises();

      expect(state.getCalendarStartDateWithAvailability()).toEqual(threeDaysFromNow);
    });
  });
});
