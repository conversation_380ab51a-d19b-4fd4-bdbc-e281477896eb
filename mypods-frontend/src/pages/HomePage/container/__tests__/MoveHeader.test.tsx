import {
  createContainer,
  createMoveLeg,
  createMoveLegAddress,
  createOrder,
  createRefreshSessionClaims
} from '../../../../testUtils/MyPodsFactories';
import { screen } from '@testing-library/react';
import { mockRefreshSession } from '../../../../../setupTests';
import {
  renderWithPoetProvidersAndState,
  runPendingPromises
} from '../../../../testUtils/RenderHelpers';
import { MoveHeader } from '../MoveHeader';
import { Order } from '../../../../domain/OrderEntities';

describe('MoveHeader', () => {
  const OLD_CITY = 'old house';
  const OLD_STATE = 'old state';
  const NEW_CITY = 'new house';
  const NEW_STATE = 'new state';
  const WAREHOUSE_CITY = 'warehouse';
  const WAREHOUSE_STATE = 'warehouse state';
  const sessionClaims = createRefreshSessionClaims();
  const deliveryMoveLeg = createMoveLeg({
    moveLegType: 'INITIAL_DELIVERY',
    originationAddress: createMoveLegAddress({ city: WAREHOUSE_CITY, state: WAREHOUSE_STATE }),
    destinationAddress: createMoveLegAddress({ city: OLD_CITY, state: OLD_STATE })
  });
  const newDestinationMoveLeg = createMoveLeg({
    moveLegType: 'FINAL_PICKUP',
    originationAddress: createMoveLegAddress({ city: NEW_CITY, state: NEW_STATE }),
    destinationAddress: createMoveLegAddress({ city: WAREHOUSE_CITY, state: WAREHOUSE_STATE })
  });
  const oldDestinationMoveLeg = createMoveLeg({
    moveLegType: 'FINAL_PICKUP',
    originationAddress: createMoveLegAddress({ city: OLD_CITY, state: OLD_STATE }),
    destinationAddress: createMoveLegAddress({ city: WAREHOUSE_CITY, state: WAREHOUSE_STATE })
  });

  const renderPage = async (order: Order) => {
    mockRefreshSession.mockResolvedValue(sessionClaims);
    const result = renderWithPoetProvidersAndState(<MoveHeader order={order} />);
    await runPendingPromises();
    return result;
  };

  describe('moving to a different city', async () => {
    const order = createOrder({
      containers: [createContainer({ moveLegs: [deliveryMoveLeg, newDestinationMoveLeg] })]
    });

    it('has origin', async () => {
      await renderPage(order);

      expect(await screen.findByTestId('origin')).toBeInTheDocument();
    });

    it('has arrow', async () => {
      await renderPage(order);

      expect(await screen.findByTestId('arrow')).toBeInTheDocument();
    });

    it('has destination', async () => {
      await renderPage(order);

      expect(await screen.findByTestId('destination')).toBeInTheDocument();
    });
  });

  describe('moving in a city or storage only', () => {
    const order = createOrder({
      containers: [createContainer({ moveLegs: [deliveryMoveLeg, oldDestinationMoveLeg] })]
    });

    it('has origin', async () => {
      await renderPage(order);

      expect(await screen.findByTestId('origin')).toBeInTheDocument();
    });

    it('does not have arrow', async () => {
      await renderPage(order);

      expect(screen.queryByTestId('arrow')).not.toBeInTheDocument();
    });

    it('does not have destination', async () => {
      await renderPage(order);

      expect(screen.queryByTestId('destination')).not.toBeInTheDocument();
    });
  });
});
