import { screen } from '@testing-library/react';
import {
  renderWithPoetProvidersAndState,
  runPendingPromises
} from '../../../../testUtils/RenderHelpers';
import { ContainerTileWrapper } from '../ContainerTileWrapper';
import {
  createCustomer,
  createOrder,
  createRefreshSessionClaims,
  mockIsHomepageScannabilityEnabled
} from '../../../../testUtils/MyPodsFactories';
import {
  mockGetCustomer,
  mockGetCustomerOrders,
  mockRefreshSession
} from '../../../../../setupTests';
import { FAKE_MAP_TEXT } from '../../../../testUtils/TestMapView';

describe('ContainerTileWrapper', () => {
  const render = async () => {
    const result = renderWithPoetProvidersAndState(<ContainerTileWrapper />);
    await runPendingPromises();
    return result;
  };
  const sessionClaims = createRefreshSessionClaims();
  const customer = createCustomer();
  const customerOrders = [createOrder()];

  beforeEach(() => {
    mockGetCustomer.mockResolvedValue(customer);
    mockRefreshSession.mockResolvedValue(sessionClaims);
    mockGetCustomerOrders.mockResolvedValue(customerOrders);
  });

  describe('homepageScannabilityEnabled is true', () => {
    beforeEach(() => {
      mockIsHomepageScannabilityEnabled.mockReturnValue(true);
    });

    it('should display the map', async () => {
      await render();

      expect(await screen.findByText(FAKE_MAP_TEXT)).toBeInTheDocument();
    });

    describe('homepageScannabilityEnabled is false', () => {
      beforeEach(() => {
        mockIsHomepageScannabilityEnabled.mockReturnValue(false);
      });

      it('should not display a map', async () => {
        await render();

        expect(screen.queryByText(FAKE_MAP_TEXT)).not.toBeInTheDocument();
      });
    });
  });
});
