import {
  createContainer,
  createMoveLeg,
  createMoveLegAddress,
  createOrder
} from '../../../../testUtils/MyPodsFactories';
import { MoveLegType } from '../../../../domain/OrderEntities';
import { getOriginAndDestinationCities } from '../getOriginAndDestinationCities';

describe('getOriginAndDestinationCities', () => {
  const OLD_CITY = 'old house';
  const OLD_STATE = 'old state';
  const NEW_CITY = 'new house';
  const NEW_STATE = 'new state';
  const WAREHOUSE_CITY = 'warehouse';
  const WAREHOUSE_STATE = 'warehouse state';
  const deliveryMoveLeg = createMoveLeg({
    moveLegType: 'INITIAL_DELIVERY',
    originationAddress: createMoveLegAddress({ city: WAREHOUSE_CITY, state: WAREHOUSE_STATE }),
    destinationAddress: createMoveLegAddress({ city: OLD_CITY, state: OLD_STATE })
  });
  const selfDeliveryMoveLeg = createMoveLeg({
    moveLegType: 'SELF_INITIAL_DELIVERY',
    originationAddress: createMoveLegAddress({ city: WAREHOUSE_CITY, state: WAREHOUSE_STATE }),
    destinationAddress: createMoveLegAddress({ city: OLD_CITY, state: OLD_STATE })
  });
  const cityDeliveryMoveLeg = createMoveLeg({
    moveLegType: 'CITY_SERVICE_DELIVERY',
    originationAddress: createMoveLegAddress({ city: WAREHOUSE_CITY, state: WAREHOUSE_STATE }),
    destinationAddress: createMoveLegAddress({ city: OLD_CITY, state: OLD_STATE })
  });
  const storageOnlyOrSameCityMoveDestinationMoveLeg = createMoveLeg({
    moveLegType: 'FINAL_PICKUP',
    originationAddress: createMoveLegAddress({ city: OLD_CITY, state: OLD_STATE }),
    destinationAddress: createMoveLegAddress({ city: WAREHOUSE_CITY, state: WAREHOUSE_STATE })
  });
  const newDestinationMoveLeg = createMoveLeg({
    moveLegType: 'FINAL_PICKUP',
    originationAddress: createMoveLegAddress({ city: NEW_CITY, state: NEW_STATE }),
    destinationAddress: createMoveLegAddress({ city: WAREHOUSE_CITY, state: WAREHOUSE_STATE })
  });
  const cityMoveDestinationMoveLeg = createMoveLeg({
    moveLegType: 'CITY_SERVICE_FINAL_PICKUP',
    originationAddress: createMoveLegAddress({ city: NEW_CITY, state: NEW_STATE }),
    destinationAddress: createMoveLegAddress({ city: WAREHOUSE_CITY, state: WAREHOUSE_STATE })
  });
  const selfMoveDestinationMoveLeg = createMoveLeg({
    moveLegType: 'SELF_FINAL_PICKUP',
    originationAddress: createMoveLegAddress({ city: NEW_CITY, state: NEW_STATE }),
    destinationAddress: createMoveLegAddress({ city: WAREHOUSE_CITY, state: WAREHOUSE_STATE })
  });
  const weightTicketMoveLeg = createMoveLeg({ moveLegType: 'WEIGHT_TICKET_EMPTY' });
  const allOtherMoveLegTypes = [
    'MOVE',
    'VISIT_CONTAINER',
    'REDELIVERY',
    'WAREHOUSE_TO_WAREHOUSE',
    'CITY_SERVICE_RETURN',
    'CITY_SERVICE_REDELIVERY',
    'LOCAL_PORT_TO_LOCAL_PORT',
    'LOCAL_PORT_TO_WAREHOUSE',
    'WEIGHT_TICKET_EMPTY',
    'WEIGHT_TICKET_FULL',
    'PORT_TO_PORT',
    'PORT_TO_WAREHOUSE',
    'UNKNOWN',
    'VISIT_LOCKOUT',
    'VISIT_OTHER',
    'VISIT_REMOVE_LOCK',
    'VISIT_REPOSITION_LEG',
    'STORAGE_CENTER_RELOCATION',
    'WAREHOUSE_TO_LOCAL_PORT',
    'STORAGE_CENTER_TO_PORT'
  ].map((moveType) => {
    return createMoveLeg({ moveLegType: moveType as MoveLegType });
  });
  const expectedOrigin = {
    city: OLD_CITY,
    state: OLD_STATE
  };
  const expectedDestination = {
    city: NEW_CITY,
    state: NEW_STATE
  };

  describe('long distance', () => {
    describe('normal move', () => {
      const order = createOrder({
        containers: [
          createContainer({
            moveLegs: [deliveryMoveLeg, ...allOtherMoveLegTypes, newDestinationMoveLeg]
          })
        ]
      });

      it('finds origin', () => {
        const actual = getOriginAndDestinationCities(order);

        expect(actual.origin).toEqual(expectedOrigin);
      });

      it('finds destination', () => {
        const actual = getOriginAndDestinationCities(order);

        expect(actual.destination).toEqual(expectedDestination);
      });
    });

    describe('self move', () => {
      const order = createOrder({
        containers: [
          createContainer({
            moveLegs: [selfDeliveryMoveLeg, ...allOtherMoveLegTypes, selfMoveDestinationMoveLeg]
          })
        ]
      });

      it('finds origin', () => {
        const actual = getOriginAndDestinationCities(order);

        expect(actual.origin).toEqual(expectedOrigin);
      });

      it('finds destination', () => {
        const actual = getOriginAndDestinationCities(order);

        expect(actual.destination).toEqual(expectedDestination);
      });
    });
  });

  describe('storage only or same city move', () => {
    const order = createOrder({
      containers: [
        createContainer({
          moveLegs: [
            deliveryMoveLeg,
            ...allOtherMoveLegTypes,
            storageOnlyOrSameCityMoveDestinationMoveLeg
          ]
        })
      ]
    });

    it('finds origin', () => {
      const actual = getOriginAndDestinationCities(order);

      expect(actual.origin).toEqual(expectedOrigin);
    });

    it('finds null destination', () => {
      const actual = getOriginAndDestinationCities(order);

      expect(actual.destination).toEqual(null);
    });
  });

  describe('city service move', () => {
    const order = createOrder({
      containers: [
        createContainer({
          moveLegs: [cityDeliveryMoveLeg, ...allOtherMoveLegTypes, cityMoveDestinationMoveLeg]
        })
      ]
    });

    it('finds origin', () => {
      const actual = getOriginAndDestinationCities(order);

      expect(actual.origin).toEqual(expectedOrigin);
    });

    it('finds destination', () => {
      const actual = getOriginAndDestinationCities(order);

      expect(actual.destination).toEqual(expectedDestination);
    });
  });

  describe('move tickets can be before initial delivery', () => {
    const order = createOrder({
      containers: [
        createContainer({ moveLegs: [weightTicketMoveLeg, deliveryMoveLeg, newDestinationMoveLeg] })
      ]
    });

    it('finds origin', () => {
      const actual = getOriginAndDestinationCities(order);

      expect(actual.origin).toEqual(expectedOrigin);
    });
  });

  describe('handles no destination', () => {
    const order = createOrder({
      containers: [createContainer({ moveLegs: [deliveryMoveLeg, ...allOtherMoveLegTypes] })]
    });

    it('returns null destination', () => {
      const actual = getOriginAndDestinationCities(order);

      expect(actual.destination).toEqual(null);
    });
  });
});
