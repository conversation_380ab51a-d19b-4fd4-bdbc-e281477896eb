import React, { useState } from 'react';
import Typography from '@mui/material/Typography/Typography';
import { Button } from 'pods-component-library';
import { css } from 'pods-component-library/styled-system/css';
import { useTranslation } from 'react-i18next';
import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';
import CloseIcon from '@mui/icons-material/Close';
import useMediaQuery from '@mui/system/useMediaQuery/useMediaQuery';
import { CalendarCheckIcon, ChatCircleIcon, PencilIcon } from '@phosphor-icons/react';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { Design } from '../../../helpers/Design';
import { MyPodsSplashScreenFeatureCard } from './MyPodsSplashScreenFeatureCard';
import { theme } from '../../../PodsTheme';
import { PodsModal } from '../../../components/Modals/PodsModal';
import { useShowMyPodsIntroSplashScreen } from '../../../helpers/useShowMyPodsIntroSplashScreen';
import { useGetSplashScreenTruckHelper } from '../../../helpers/useGetSplashScreenTruckHelper';

export const MyPodsSplashScreenModal = () => {
  const SplashScreenTranslationKeys = TranslationKeys.HomePage.SplashScreen;
  const { t } = useTranslation();
  const { useShouldShow, setShown } = useShowMyPodsIntroSplashScreen();
  const [isOpen, setIsOpen] = useState<boolean>(true);
  const onClose = () => {
    setShown();
    setIsOpen(false);
  };
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = style(isMobile);
  const truckImageUrl = useGetSplashScreenTruckHelper();

  if (useShouldShow()) {
    return (
      <PodsModal open={isOpen}>
        <Grid sx={{ ...styles.main }}>
          <div style={styles.pictureColumn}>
            <img src={truckImageUrl} alt="Truck illustration logo" style={styles.image} />
          </div>
          <Grid sx={{ ...styles.textAndAgreeButtonColumn }}>
            <div style={styles.textDetails}>
              <Typography variant="h2" {...styles.title}>
                {t(SplashScreenTranslationKeys.TITLE)}
              </Typography>
              <Typography {...styles.description}>
                {t(SplashScreenTranslationKeys.DESCRIPTION)}
              </Typography>
              <MyPodsSplashScreenFeatureCard
                translations={SplashScreenTranslationKeys.Features.Scheduling}
                icon={<CalendarCheckIcon size={24} />}
              />
              <MyPodsSplashScreenFeatureCard
                translations={SplashScreenTranslationKeys.Features.OnePlace}
                icon={<PencilIcon size={24} />}
              />
              <MyPodsSplashScreenFeatureCard
                translations={SplashScreenTranslationKeys.Features.IntegratedSupport}
                icon={<ChatCircleIcon size={24} />}
              />
            </div>
            <div>
              <Button
                variant="filled"
                buttonSize="large"
                color="primary"
                css={stylesheet.getStartedButton}
                onPress={onClose}>
                {t(SplashScreenTranslationKeys.ACCEPT_BUTTON)}
              </Button>
            </div>
          </Grid>
          <div style={styles.closeButton}>
            <IconButton onClick={onClose} aria-label="close">
              <CloseIcon {...styles.closeIcon} />
            </IconButton>
          </div>
        </Grid>
      </PodsModal>
    );
  }
  return <div></div>;
};

const style = (isMobile: boolean) => ({
  main: {
    display: 'flex',
    flexDirection: isMobile ? 'column' : 'row',
    height: '100%'
  },
  pictureColumn: {},
  image: {
    display: 'block',
    width: isMobile ? '100%' : 'auto',
    height: isMobile ? 'auto' : '100%'
  },
  textAndAgreeButtonColumn: {
    display: 'flex',
    padding: isMobile ? '16px' : '24px',
    flexDirection: 'column',
    flex: '1 1 100%',
    justifyContent: 'space-between',
    flexGrow: '1',
    minHeight: 0,
    gap: '1rem'
  },
  textDetails: {
    display: 'flex',
    flexDirection: 'column',
    gap: '16px',
    flexGrow: 1,
    overflow: 'auto',
    minHeight: 0
  } as React.CSSProperties,
  title: {
    ...Design.Alias.Text.Heading.Desktop.Xl,
    color: Design.Alias.Color.accent900,
    paddingTop: Design.Primitives.Spacing.md
  },
  description: {
    ...Design.Alias.Text.BodyUniversal.Sm,
    color: Design.Alias.Color.neutral700
  },
  closeButton: {
    position: 'absolute',
    right: '20px',
    top: '20px'
  } as React.CSSProperties,
  closeIcon: {
    sx: {
      padding: 0,
      width: '24px',
      height: '24px',
      color: Design.Alias.Color.neutral900
    }
  }
});

const stylesheet = {
  getStartedButton: css.raw({
    paddingY: 'xs',
    paddingX: 'sm',
    position: 'sticky'
  })
};
