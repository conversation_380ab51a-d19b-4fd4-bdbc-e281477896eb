import { FlexBox } from 'pods-component-library/src/components/Layout/FlexBox';
import { Disclosure, Text } from 'pods-component-library';
import React from 'react';
import { css } from 'pods-component-library/styled-system/css';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { Order } from '../../../domain/OrderEntities';
import { MoveHeader } from './MoveHeader';

interface ScannableOrderCardProps {
  order: Order;
  children: React.ReactNode;
}

export const ScannableOrderCard = ({ order, children }: ScannableOrderCardProps) => (
  <Disclosure
    summary={
      <FlexBox css={summeryStyle}>
        <Text
          i18nKey={TranslationKeys.HomePage.Disclosure.Summary.ORDER_NUMBER}
          values={{ orderNumber: order.orderId }}
          css={orderNumberStyle}
        />
        <MoveHeader order={order} />
      </FlexBox>
    }>
    {children}
  </Disclosure>
);

const summeryStyle = css.raw({
  flexDirection: 'column',
  gap: '8px'
});

const orderNumberStyle = css.raw({
  fontFamily: 'secondary',
  fontSize: 14,
  fontWeight: 600,
  letterSpacing: '0',
  lineHeight: 1.5,
  color: 'neutral700'
});
