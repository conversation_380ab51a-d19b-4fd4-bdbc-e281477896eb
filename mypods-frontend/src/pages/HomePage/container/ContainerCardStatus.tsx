import { Box } from 'pods-component-library/src/components/Layout/Box';
import { FlexBox } from 'pods-component-library/src/components/Layout/FlexBox';
import React, { useContext } from 'react';
import { css, cva } from 'pods-component-library/styled-system/css';
import { useTranslation } from 'react-i18next';
import { getUpNextMoveLeg, ScheduledStatus } from '../../../domain/OrderEntities';
import { formatAddress } from '../../../networkRequests/responseEntities/CustomerEntities';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { ContainerContext } from '../../../context/ContainerContext';
import { useServiceDateState } from './useServiceDateState';

export const ContainerCardStatus = () => {
  const { container } = useContext(ContainerContext);
  const upNextMoveLeg = getUpNextMoveLeg(container);
  const { t: translate } = useTranslation();
  const { serviceDateTextForTitle } = useServiceDateState(upNextMoveLeg);

  const Tx = TranslationKeys.HomePage.ContainerCard;
  const status: ScheduledStatus = upNextMoveLeg?.scheduledStatus ?? 'UNSCHEDULED';

  const subtitle = upNextMoveLeg?.displayAddress
    ? formatAddress(upNextMoveLeg.displayAddress)
    : translate(Tx.Status.UNSCHEDULED_CONTAINER_SUBTITLE);

  return (
    <FlexBox css={stylesheet.arrivalDataStatusContainer}>
      <Box
        data-testid="container-status-bar"
        css={stylesheet.arrivalDataStatusBar.raw({ status })}></Box>
      <Box css={stylesheet.arrivalDataStatusTextContainer}>
        <FlexBox
          data-testid="container-status-title"
          css={stylesheet.arrivalDataStatusText.raw({ status })}>
          {serviceDateTextForTitle}
          {upNextMoveLeg?.eta && (
            <Box data-testid="container-status-eta" css={stylesheet.arrivalEtaText}>
              {upNextMoveLeg?.eta}
            </Box>
          )}
        </FlexBox>
        <Box data-testid="container-status-subtitle" css={stylesheet.arrivalDataStatusSubtitleText}>
          {subtitle}
        </Box>
      </Box>
    </FlexBox>
  );
};

const stylesheet = {
  arrivalDataStatusContainer: css.raw({
    gap: '6px',
    alignItems: 'stretch'
  }),
  arrivalDataStatusTextContainer: css.raw({
    gap: '2px'
  }),
  arrivalDataStatusSubtitleText: css.raw({
    color: 'neutral700',
    textStyle: 'bodyUniversal.xs',
    fontSize: 'bodyUniversal.xs'
  }),
  arrivalEtaText: css.raw({
    color: 'successMain',
    textStyle: 'bodyUniversal.sm',
    fontSize: 'bodyUniversal.sm'
  }),
  arrivalDataStatusBar: cva({
    base: {
      width: '4px',
      borderRadius: '2px',
      alignSelf: 'stretch',
      margin: '4px 0'
    },
    variants: {
      status: {
        FUTURE: { backgroundColor: 'successMain' },
        UNSCHEDULED: { backgroundColor: 'neutral400' },
        PAST: {}
      }
    }
  }),
  arrivalDataStatusText: cva({
    base: {
      textStyle: 'bodyUniversal.smBold',
      fontSize: 'bodyUniversal.sm'
    },
    variants: {
      status: {
        FUTURE: { color: 'successMain' },
        UNSCHEDULED: { color: 'neutral600' },
        PAST: {}
      }
    }
  })
};
