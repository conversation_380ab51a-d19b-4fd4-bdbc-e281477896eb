import React, { useContext } from 'react';
import { Typography, useMediaQuery } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { FlexBox } from 'pods-component-library/src/components/Layout/FlexBox';
import { Box } from 'pods-component-library/src/components/Layout/Box';
import { css } from 'pods-component-library/styled-system/css';
import { Link } from 'react-router';
import { ContainerContext } from '../../../context/ContainerContext';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { Design } from '../../../helpers/Design';
import { RightChevronIcon } from '../../../components/icons/RightChevronIcon';
import { ENV_VARS } from '../../../environment';
import { ContainerCardStatus } from './ContainerCardStatus';
import { ContainerCardTimeline } from './ContainerCardTimeline';
import { buildContainerRoute } from '../../../Routes';
import { theme } from '../../../PodsTheme';

export const ContainerCard = () => {
  const { container, order } = useContext(ContainerContext);
  const { containerSize } = container;
  const { t: translate } = useTranslation();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = inlineStyles(isMobile);
  const to = buildContainerRoute(container.containerOrderId ?? container.containerId ?? '');

  const Tx = TranslationKeys.HomePage;

  function getContainerId(): string {
    return container.containerId ? `# ${container.containerId}` : '';
  }

  const getContainerImageSrcUrlBySize = (size: string) =>
    `${ENV_VARS.ASSETS_BASE_URL}/images/containers/${size}ftContainer_cropped.png`;

  const getImageScale = (size: string) => {
    const scaleMap: { [key: string]: number } = {
      '8': 0.8,
      '12': 0.9,
      '16': 1.0
    };

    return scaleMap[size] || 1.0;
  };

  return (
    <Link to={to} state={{ container, order }} style={{ textDecoration: 'none' }}>
      <FlexBox css={stylesheet.cardContainer}>
        {!isMobile && (
          <FlexBox css={stylesheet.imageContainer} data-testid="container-image-container">
            <img
              src={getContainerImageSrcUrlBySize(containerSize)}
              alt="container-image"
              style={{
                width: `${getImageScale(containerSize) * 100}%`,
                height: 'auto',
                maxHeight: '100%',
                objectFit: 'contain'
              }}
            />
          </FlexBox>
        )}

        <FlexBox css={stylesheet.container}>
          <Box css={stylesheet.contentContainer}>
            <FlexBox css={stylesheet.headerRow}>
              {isMobile && (
                <FlexBox css={stylesheet.mobileImageContainer}>
                  <img
                    src={getContainerImageSrcUrlBySize(containerSize)}
                    alt="container-image"
                    style={{
                      width: `${getImageScale(containerSize) * 100}%`,
                      maxWidth: '100%',
                      height: 'auto',
                      maxHeight: '60px',
                      objectFit: 'contain',
                      objectPosition: 'center'
                    }}
                  />
                </FlexBox>
              )}
              <FlexBox css={stylesheet.headerWrapper}>
                <FlexBox css={stylesheet.headerAndChevron}>
                  <Typography sx={styles.headerContainerName}>
                    {translate(Tx.ContainerTile.ContainerHeader.CONTAINER_TYPE, {
                      size: containerSize
                    })}
                  </Typography>
                  <Box css={stylesheet.rightChevronIconWrapper}>
                    <RightChevronIcon style={styles.chevron} />
                  </Box>
                </FlexBox>
                <Typography sx={styles.headerContainerId}>{getContainerId()}</Typography>
              </FlexBox>
            </FlexBox>
            <FlexBox css={stylesheet.timelineWrapper}>
              <ContainerCardStatus />
            </FlexBox>

            <ContainerCardTimeline
              moveLegs={container.moveLegs}
              upNextMoveLegId={container.upNextMoveLegId}
            />
          </Box>
        </FlexBox>
      </FlexBox>
    </Link>
  );
};

const stylesheet = {
  headerWrapper: css.raw({
    width: '100%',
    alignItems: 'center',
    xsToSm: {
      alignItems: 'flex-start',
      flexDirection: 'column'
    }
  }),
  cardContainer: css.raw({
    borderWidth: '1px',
    borderColor: 'neutral200',
    borderStyle: 'solid',
    borderRadius: '20px'
  }),
  container: css.raw({
    alignItems: 'flex-start',
    gap: 'sm',
    flex: 1,
    padding: 'sm',
    xsToSm: {
      paddingX: 'xxs',
      paddingY: 'xxxs'
    }
  }),
  imageContainer: css.raw({
    width: '140px',
    flexShrink: 0,
    overflow: 'hidden',
    borderRight: '1px',
    borderRightColor: 'neutral200',
    borderRightStyle: 'solid',
    alignItems: 'center',
    justifyContent: 'center',
    boxSizing: 'border-box',
    paddingX: 'sm',
    paddingY: 'xxs',
    alignSelf: 'stretch'
  }),
  mobileImageContainer: css.raw({
    height: '60px',
    width: '100px',
    flexShrink: 0,
    overflow: 'hidden',
    alignItems: 'center',
    justifyContent: 'center',
    boxSizing: 'border-box',
    alignSelf: 'stretch'
  }),
  headerAndChevron: css.raw({
    flex: 1,
    alignItems: 'center',
    gap: 'xxxs',
    xsToSm: {
      width: '100%',
      justifyContent: 'space-between'
    }
  }),
  contentContainer: css.raw({
    display: 'flex',
    flexDirection: 'column',
    gap: 'xxxs',
    flex: 1,
    xsToSm: {
      padding: 'xs'
    }
  }),
  headerRow: css.raw({
    display: 'flex',
    alignItems: 'center',
    gap: 'xxs',
    flexWrap: 'nowrap',
    width: '100%'
  }),
  rightChevronIconWrapper: css.raw({
    display: 'flex',
    width: 'sm',
    height: 'sm',
    alignItems: 'center',
    justifyContent: 'center'
  }),
  timelineWrapper: css.raw({
    xsToSm: {
      paddingLeft: '4px'
    }
  })
};

const inlineStyles = (isMobile: boolean) => ({
  headerContainerName: {
    ...Design.Alias.Text.BodyUniversal.LgBold,
    color: Design.Alias.Color.secondary500,
    gap: Design.Primitives.Spacing.xxs
  },
  headerContainerId: {
    ...Design.Alias.Text.BodyUniversal.XxsSemi,
    color: Design.Alias.Color.neutral600,
    gap: Design.Primitives.Spacing.xxs,
    textAlign: isMobile ? 'left' : 'right',
    marginLeft: isMobile ? 0 : 'auto'
  },
  chevron: {
    color: Design.Alias.Color.secondary500,
    width: isMobile ? '1.5rem' : '1rem',
    height: isMobile ? '1.5rem' : '1rem'
  }
});
