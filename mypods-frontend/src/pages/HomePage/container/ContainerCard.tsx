import React, { useContext } from 'react';
import { Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { FlexBox } from 'pods-component-library/src/components/Layout/FlexBox';
import { Box } from 'pods-component-library/src/components/Layout/Box';
import { css } from 'pods-component-library/styled-system/css';
import { ContainerContext } from '../../../context/ContainerContext';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { Design } from '../../../helpers/Design';
import { RightChevronIcon } from '../../../components/icons/RightChevronIcon';
import { ENV_VARS } from '../../../environment';

export interface ContainerCardProps {
  containerNumber?: number;
}

export const ContainerCard = () => {
  const { container } = useContext(ContainerContext);
  const { containerSize } = container;
  const { t: translate } = useTranslation();

  const Tx = TranslationKeys.HomePage.ContainerTile;

  function getContainerId(): string {
    return `${container.containerId ? `# ${container.containerId}` : ''}`;
  }

  const getContainerImageSrcUrlBySize = (size: string) =>
    `${ENV_VARS.ASSETS_BASE_URL}/images/containers/${size}ft-container-cropped.png`;

  return (
    <FlexBox css={cssStyles.cardContainer}>
      <FlexBox css={cssStyles.imageContainer} data-testid="container-image-container">
        <img
          src={getContainerImageSrcUrlBySize(containerSize)}
          alt="container-image"
          style={{
            width: '100px'
          }}
        />
      </FlexBox>
      <FlexBox css={cssStyles.container}>
        <Box css={cssStyles.contentContainer}>
          <FlexBox css={cssStyles.headerRow}>
            <FlexBox>
              <Typography sx={inlineStyles.headerContainerName}>
                {translate(Tx.ContainerHeader.CONTAINER_TYPE, { size: containerSize })}
              </Typography>
              <Box css={cssStyles.rightChevronIconWrapper}>
                <RightChevronIcon
                  style={{ color: Design.Alias.Color.secondary500, width: '100%', height: '100%' }}
                />
              </Box>
            </FlexBox>
            <Typography sx={inlineStyles.headerContainerId}>{getContainerId()}</Typography>
          </FlexBox>
        </Box>
      </FlexBox>
    </FlexBox>
  );
};

const cssStyles = {
  cardContainer: css.raw({
    borderWidth: '1px',
    borderColor: 'neutral200',
    borderStyle: 'solid',
    borderRadius: '20px'
  }),
  container: css.raw({
    alignItems: 'flex-start',
    padding: 'sm',
    gap: 'sm',
    flex: 1
  }),
  imageContainer: css.raw({
    width: '100px',
    height: '100px',
    flexShrink: 0,
    overflow: 'hidden',
    borderRight: '1px',
    borderRightColor: 'neutral200',
    borderRightStyle: 'solid',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 'sm'
  }),
  image: {
    width: '100px',
    height: '22px',
    flexShrink: 0
  },
  contentContainer: css.raw({
    gap: 'xs',
    flex: 1
  }),
  headerRow: css.raw({
    display: 'flex',
    alignItems: 'center',
    gap: 'xxxs',
    flexWrap: 'nowrap',
    width: '100%'
  }),
  rightChevronIconWrapper: css.raw({
    display: 'flex',
    width: 'sm',
    height: 'sm',
    alignItems: 'center',
    justifyContent: 'center'
  })
};

const inlineStyles = {
  headerContainerName: {
    ...Design.Alias.Text.BodyUniversal.LgBold,
    color: Design.Alias.Color.secondary500,
    gap: Design.Primitives.Spacing.xxs
  },
  headerContainerId: {
    ...Design.Alias.Text.BodyUniversal.XxsSemi,
    color: Design.Alias.Color.neutral600,
    gap: Design.Primitives.Spacing.xxs,
    textAlign: 'right',
    marginLeft: 'auto'
  }
};
