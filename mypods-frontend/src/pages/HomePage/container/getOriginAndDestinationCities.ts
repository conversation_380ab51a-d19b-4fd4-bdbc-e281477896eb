import { MoveLeg, MoveLegType, Order } from '../../../domain/OrderEntities';

interface OriginAndDestinationCities {
  origin: {
    city: string;
    state: string;
  };
  destination: {
    city: string;
    state: string;
  } | null;
}

const originTypes: MoveLegType[] = [
  'INITIAL_DELIVERY',
  'SELF_INITIAL_DELIVERY',
  'CITY_SERVICE_DELIVERY'
];
const destinationTypes: MoveLegType[] = [
  'FINAL_PICKUP',
  'SELF_FINAL_PICKUP',
  'CITY_SERVICE_FINAL_PICKUP'
];

export const getOriginAndDestinationCities = (order: Order): OriginAndDestinationCities => {
  const originLeg = order.containers[0].moveLegs.find((moveLeg: MoveLeg) =>
    originTypes.includes(moveLeg.moveLegType)
  );
  const origin = {
    city: originLeg?.destinationAddress.city!,
    state: originLeg?.destinationAddress.state!
  };
  const destinationLeg = order.containers[0].moveLegs.find((moveLeg: MoveLeg) =>
    destinationTypes.includes(moveLeg.moveLegType)
  );
  const getDestination = () => {
    if (!destinationLeg) {
      return null;
    }
    const destination = {
      city: destinationLeg.originationAddress.city,
      state: destinationLeg.originationAddress.state
    };
    if (destination.city === origin.city && destination.state === origin.state) {
      return null;
    }
    return destination;
  };
  const destination = getDestination();
  return {
    origin,
    destination
  };
};
