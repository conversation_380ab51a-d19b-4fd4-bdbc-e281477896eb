import { getDestinationAddress, getOriginAddress, Order } from '../../../domain/OrderEntities';

interface OriginAndDestinationCities {
  origin: {
    city: string;
    state: string;
  };
  destination: {
    city: string;
    state: string;
  } | null;
}

export const getOriginAndDestinationCities = (order: Order): OriginAndDestinationCities => {
  const originLeg = getOriginAddress(order);
  const origin = {
    city: originLeg.city,
    state: originLeg.state
  };
  const destinationAddress = getDestinationAddress(order);
  const getDestination = () => {
    if (!destinationAddress) {
      return null;
    }
    if (destinationAddress.city === origin.city && destinationAddress.state === origin.state) {
      return null;
    }
    return { city: destinationAddress.city, state: destinationAddress.state };
  };
  const destination = getDestination();
  return {
    origin,
    destination
  };
};
