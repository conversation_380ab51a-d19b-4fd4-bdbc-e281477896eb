import { useTranslation } from 'react-i18next';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { getServiceDateContext } from '../../../locales/TranslationConstants';
import { MoveLeg } from '../../../domain/OrderEntities';
import { getDaysUntil } from '../../../helpers/dateHelpers';

const Tx = TranslationKeys.HomePage.ContainerCard;

export const useServiceDateState = (upNextMoveLeg?: MoveLeg) => {
  const { t: translate } = useTranslation();

  const getServiceDateTextForTitle = () =>
    upNextMoveLeg?.eta && upNextMoveLeg?.scheduledStatus !== 'UNSCHEDULED'
      ? `${getServiceDateText()}, `
      : getServiceDateText();

  const getServiceDateText = () => {
    if (!upNextMoveLeg?.scheduledDate || upNextMoveLeg?.scheduledStatus === 'UNSCHEDULED')
      return translate(Tx.Status.UNSCHEDULED_CONTAINER_TITLE);

    const daysUntil = getDaysUntil(upNextMoveLeg.scheduledDate);
    const context = getServiceDateContext(upNextMoveLeg);

    if (daysUntil !== null && daysUntil <= 1) {
      return translate(Tx.SERVICE_DATE_TODAY_TOMORROW, {
        count: daysUntil,
        context
      });
    }

    const date = new Date(upNextMoveLeg.scheduledDate);
    const month = date.toLocaleDateString('en-US', { month: 'short' });
    const day = date.getDate();

    return translate(Tx.SERVICE_DATE, {
      day,
      month,
      context
    });
  };

  return {
    serviceDateTextForTitle: getServiceDateTextForTitle(),
    serviceDateText: getServiceDateText()
  };
};
