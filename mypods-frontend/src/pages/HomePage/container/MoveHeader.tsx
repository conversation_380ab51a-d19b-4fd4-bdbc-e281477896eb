import React from 'react';
import { css } from 'pods-component-library/styled-system/css';
import { FlexBox } from 'pods-component-library/src/components/Layout/FlexBox';
import { ArrowRightIcon } from '@phosphor-icons/react';
import { getOriginAndDestinationCities } from './getOriginAndDestinationCities';
import { Order } from '../../../domain/OrderEntities';
import { CityStateLabel } from './CityStateLabel';

interface MoveHeaderProps {
  order: Order;
}

export const MoveHeader = ({ order }: MoveHeaderProps) => {
  const originAndDestination = getOriginAndDestinationCities(order);
  return (
    <FlexBox css={headerStyle}>
      <CityStateLabel testId="origin" address={originAndDestination.origin} />
      {!!originAndDestination.destination && (
        <>
          <ArrowRightIcon data-testid="arrow" className={arrowStyle} />
          <CityStateLabel testId="destination" address={originAndDestination.destination} />
        </>
      )}
    </FlexBox>
  );
};

const headerStyle = css.raw({
  marginBottom: '14px',
  alignItems: 'center'
});

const arrowStyle = css({
  width: '20px',
  height: '20px',
  color: 'accent900',
  padding: '0 8px'
});
