import React, { FC, useEffect } from 'react';
import { ContainerProvider } from '../../../context/ContainerContext';
import { SingleOrderProvider } from '../../../context/SingleOrderContext';
import { useGetCustomerOrders } from '../../../networkRequests/queries/useGetCustomerOrders';
import { ContainerSkeleton } from '../../../components/Loading/ContainerSkeleton';
import {
  CONTAINER_CARDS,
  HOMEPAGE_SCANNABILITY,
  useFeatureFlags
} from '../../../helpers/useFeatureFlags';
import { ScannableOrderCard } from './ScannableOrderCard';
import { ContainerCardOrTile } from './ContainerCardOrTile';
import { Order } from '../../../domain/OrderEntities';

export const ContainerTileWrapper: FC<{
  topOfPageRef?: React.RefObject<HTMLDivElement>;
}> = ({ topOfPageRef }) => {
  const { customerOrders: orders, isError, refetch } = useGetCustomerOrders();
  const { isContainerCardsEnabled, isHomepageScannabilityEnabled } = useFeatureFlags([
    CONTAINER_CARDS,
    HOMEPAGE_SCANNABILITY
  ]);

  useEffect(() => {
    if (isError) {
      refetch();
    }
  }, [isError]);

  if (isError) {
    return <ContainerSkeleton isError topOfPageRef={topOfPageRef} />;
  }
  const containerCardsEnabled = isContainerCardsEnabled();
  const homepageScannabilityEnabled = isHomepageScannabilityEnabled();

  const renderContainers = (order: Order) =>
    order.containers?.map((container, index) => (
      <ContainerProvider
        key={container.containerId ?? `container-context${index}}`}
        state={{ container, order }}>
        {ContainerCardOrTile(containerCardsEnabled)}
      </ContainerProvider>
    ));

  return (
    // eslint-disable-next-line react/jsx-no-useless-fragment
    <>
      {orders?.map((order) => (
        <SingleOrderProvider state={{ order }} key={order.orderId}>
          {homepageScannabilityEnabled ? (
            <ScannableOrderCard order={order}>{renderContainers(order)}</ScannableOrderCard>
          ) : (
            renderContainers(order)
          )}
        </SingleOrderProvider>
      ))}
    </>
  );
};
