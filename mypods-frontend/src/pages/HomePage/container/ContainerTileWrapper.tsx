import React, { FC, useEffect } from 'react';
import { FlexBox } from 'pods-component-library/src/components/Layout/FlexBox';
import { css } from 'pods-component-library/styled-system/css';
import { ContainerProvider } from '../../../context/ContainerContext';
import { SingleOrderProvider } from '../../../context/SingleOrderContext';
import { useGetCustomerOrders } from '../../../networkRequests/queries/useGetCustomerOrders';
import { ContainerSkeleton } from '../../../components/Loading/ContainerSkeleton';
import {
  CONTAINER_CARDS,
  HOMEPAGE_SCANNABILITY,
  useFeatureFlags
} from '../../../helpers/useFeatureFlags';
import { ScannableOrderCard } from './ScannableOrderCard';
import { ContainerCardOrTile } from './ContainerCardOrTile';
import { Order } from '../../../domain/OrderEntities';
import { MapView } from '../map/MapView';

export const ContainerTileWrapper: FC<{
  topOfPageRef?: React.RefObject<HTMLDivElement>;
}> = ({ topOfPageRef }) => {
  const { customerOrders: orders, isError, refetch } = useGetCustomerOrders();
  const { isContainerCardsEnabled, isHomepageScannabilityEnabled } = useFeatureFlags([
    CONTAINER_CARDS,
    HOMEPAGE_SCANNABILITY
  ]);

  useEffect(() => {
    if (isError) {
      refetch();
    }
  }, [isError]);

  if (isError) {
    return <ContainerSkeleton isError topOfPageRef={topOfPageRef} />;
  }
  const containerCardsEnabled = isContainerCardsEnabled();
  const homepageScannabilityEnabled = isHomepageScannabilityEnabled();

  const renderContainers = (order: Order) =>
    order.containers?.map((container, index) => (
      <ContainerProvider
        key={container.containerId ?? `container-context${index}}`}
        state={{ container, order }}>
        {ContainerCardOrTile(containerCardsEnabled)}
      </ContainerProvider>
    ));

  return (
    <>
      {orders?.map((order) => (
        <SingleOrderProvider state={{ order }} key={order.orderId}>
          {homepageScannabilityEnabled ? (
            <ScannableOrderCard order={order}>
              <FlexBox css={stylesheet.description}>
                <MapView order={order} />
                {renderContainers(order)}
              </FlexBox>
            </ScannableOrderCard>
          ) : (
            renderContainers(order)
          )}
        </SingleOrderProvider>
      ))}
    </>
  );
};

const stylesheet = {
  description: css.raw({
    flexDirection: 'column'
  })
};
