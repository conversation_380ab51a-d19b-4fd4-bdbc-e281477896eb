import React, { FC } from 'react';
import { FlexBox } from 'pods-component-library/src/components/Layout/FlexBox';
import { Box } from 'pods-component-library/src/components/Layout/Box';
import { css } from 'pods-component-library/styled-system/css';
import { Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { MoveLeg, MoveLegType } from '../../../domain/OrderEntities';
import { Design } from '../../../helpers/Design';
import { TimelineTruckIcon } from '../../../components/icons/containerCard/TimelineTruckIcon';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { TimelineCheckmarkCircleIcon } from '../../../components/icons/containerCard/TimelineCheckmarkCircleIcon';

const STAGES = {
  DELIVERY: 'delivery',
  PICKUP: 'pickup',
  STORAGE: 'storage',
  TRANSIT: 'transit',
  DROPOFF: 'dropoff',
  RETURN: 'return',
  VISIT: 'visit'
} as const;

type StageType = (typeof STAGES)[keyof typeof STAGES];

const TRUCK_ELIGIBLE = [
  STAGES.PICKUP,
  STAGES.DROPOFF,
  STAGES.RETURN,
  STAGES.DELIVERY,
  STAGES.TRANSIT
] as const;

type TruckEligibleStage = (typeof TRUCK_ELIGIBLE)[number];

const isTruckEligible = (s: StageType): s is TruckEligibleStage =>
  (TRUCK_ELIGIBLE as readonly StageType[]).includes(s);

const mapMoveLegTypeToStage = (moveLegType: MoveLegType): StageType => {
  switch (moveLegType) {
    case 'INITIAL_DELIVERY':
    case 'SELF_INITIAL_DELIVERY':
    case 'CITY_SERVICE_DELIVERY':
      return STAGES.DELIVERY;
    case 'REDELIVERY':
    case 'CITY_SERVICE_REDELIVERY':
      return STAGES.DROPOFF;
    case 'PICKUP':
      return STAGES.PICKUP;
    case 'FINAL_PICKUP':
    case 'SELF_FINAL_PICKUP':
    case 'CITY_SERVICE_FINAL_PICKUP':
      return STAGES.RETURN;
    case 'STORAGE_CENTER_RELOCATION':
    case 'MOVE':
    case 'PORT_TO_PORT':
    case 'LOCAL_PORT_TO_LOCAL_PORT':
    case 'PORT_TO_WAREHOUSE':
    case 'LOCAL_PORT_TO_WAREHOUSE':
    case 'WAREHOUSE_TO_LOCAL_PORT':
    case 'WAREHOUSE_TO_WAREHOUSE':
    case 'STORAGE_CENTER_TO_PORT':
      return STAGES.TRANSIT;
    case 'VISIT_CONTAINER':
      return STAGES.STORAGE;
    default:
      return STAGES.TRANSIT;
  }
};

interface TimelineStage {
  id: string;
  label: string;
  icon?: React.ReactNode;
  isActive: boolean;
  isCompleted: boolean;
  moveLeg?: MoveLeg;
}

interface ContainerTimelineProps {
  moveLegs: MoveLeg[];
  upNextMoveLegId?: string;
}

export const ContainerCardTimeline: FC<ContainerTimelineProps> = ({
  moveLegs,
  upNextMoveLegId
}) => {
  const { t: translate } = useTranslation();

  const Tx = TranslationKeys.HomePage.ContainerCard.Timeline;

  const getStageLabels = (): Record<string, string> => ({
    delivery: translate(Tx.Labels.DELIVERY),
    pickup: translate(Tx.Labels.PICKUP),
    storage: translate(Tx.Labels.STORAGE),
    transit: translate(Tx.Labels.TRANSIT),
    dropoff: translate(Tx.Labels.DROPOFF),
    return: translate(Tx.Labels.RETURN),
    visit: translate(Tx.Labels.VISIT)
  });

  const stageLabels = getStageLabels();

  const getStageIcon = (
    stage: StageType,
    isCompleted: boolean,
    isActive: boolean,
    isUnscheduled: boolean
  ): React.ReactNode => {
    if (isTruckEligible(stage) && isActive && !isCompleted && !isUnscheduled) {
      return <TimelineTruckIcon data-testid="timeline-truck-icon" />;
    }
    if (isCompleted) {
      return <TimelineCheckmarkCircleIcon data-testid="timeline-checkmark-icon" />;
    }

    return <Box css={stylesheet.unscheduledCircle} data-testid="timeline-empty-circle-icon" />;
  };

  const stageStatusMap = new Map<
    string,
    {
      isCompleted: boolean;
      isActive: boolean;
      isUnscheduled: boolean;
      moveLeg: MoveLeg;
    }
  >();

  moveLegs.forEach((moveLeg) => {
    const stage = mapMoveLegTypeToStage(moveLeg.moveLegType);

    if (stage === 'visit') return;

    const isActive = moveLeg.moveLegId === upNextMoveLegId;
    const isCompleted = moveLeg.scheduledStatus === 'PAST';
    const isUnscheduled = moveLeg.scheduledStatus === 'UNSCHEDULED';

    const existing = stageStatusMap.get(stage);

    if (!existing || isActive || (isCompleted && !existing.isCompleted)) {
      stageStatusMap.set(stage, {
        isCompleted,
        isActive,
        isUnscheduled,
        moveLeg
      });
    }
  });

  const timelineStages: TimelineStage[] = [];
  const addedStages = new Set<string>();

  moveLegs.forEach((moveLeg) => {
    const stage = mapMoveLegTypeToStage(moveLeg.moveLegType);

    if (stage === STAGES.VISIT || addedStages.has(stage)) return;

    addedStages.add(stage);
    const stageStatus = stageStatusMap.get(stage)!;

    timelineStages.push({
      id: stage,
      label: stageLabels[stage],
      isActive: stageStatus.isActive,
      isCompleted: stageStatus.isCompleted,
      moveLeg: stageStatus.moveLeg,
      icon: getStageIcon(
        stage,
        stageStatus.isCompleted,
        stageStatus.isActive,
        stageStatus.isUnscheduled
      )
    });

    if (stage === STAGES.PICKUP && !addedStages.has(STAGES.STORAGE)) {
      addedStages.add(STAGES.STORAGE);
      timelineStages.push({
        id: STAGES.STORAGE,
        label: stageLabels.storage,
        isActive: false,
        isCompleted: stageStatus.isCompleted,
        icon: getStageIcon(
          STAGES.STORAGE,
          stageStatus.isCompleted,
          false,
          stageStatus.isUnscheduled
        )
      });
    }
  });

  return (
    <FlexBox css={stylesheet.timelineContainer}>
      <FlexBox css={stylesheet.stagesContainer}>
        {timelineStages.map((stage, index) => {
          const isUnscheduled: boolean = stage.moveLeg?.scheduledStatus === 'UNSCHEDULED';

          return (
            <FlexBox key={stage.id} css={stylesheet.frag}>
              {index > 0 && <Box css={stylesheet.line} data-complete={stage.isCompleted} />}

              <FlexBox css={stylesheet.stageWrapper} className="group">
                <Box css={stylesheet.stageIconContainer}>
                  <Box css={stylesheet.iconWrapper}>{stage.icon}</Box>
                </Box>
                <Box css={stylesheet.stageLabel}>
                  <Typography sx={getStageStyles(stage.isActive, stage.isCompleted, isUnscheduled)}>
                    {stage.label}
                  </Typography>
                </Box>
              </FlexBox>
            </FlexBox>
          );
        })}
      </FlexBox>
    </FlexBox>
  );
};

const getStageStyles = (isActive: boolean, isCompleted: boolean, isUnscheduled: boolean) => {
  let color;
  if (isActive && !isUnscheduled) {
    color = Design.Alias.Color.accent900;
  } else if (isCompleted) {
    color = Design.Alias.Color.neutral900;
  } else {
    color = Design.Alias.Color.neutral700;
  }

  return {
    ...Design.Alias.Text.BodyUniversal.Xxs,
    textAlign: 'center',
    lineHeight: '14px',
    marginTop: '4px',
    color,
    ...(isActive && !isUnscheduled && { fontWeight: 'bold' })
  };
};

const stylesheet = {
  frag: css.raw({
    display: 'flex',
    alignItems: 'center',
    flex: { base: 1, _first: 'none' }
  }),
  line: css.raw({
    flex: 1,
    borderTopWidth: '1px',
    borderTopStyle: 'dashed',
    borderTopColor: 'accent900/40',
    '&[data-complete=true]': {
      borderTopColor: 'neutral900',
      borderTopStyle: 'solid'
    }
  }),
  timelineContainer: css.raw({
    width: '100%',
    paddingTop: 'xxs',
    alignItems: 'center',
    justifyContent: 'center',
    paddingLeft: '1rem',
    paddingRight: '1rem',
    paddingBottom: 'sm',
    boxSizing: 'border-box'
  }),
  stagesContainer: css.raw({
    position: 'relative',
    display: 'flex',
    alignItems: 'center',
    width: '100%'
  }),
  stageWrapper: css.raw({
    position: 'relative',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center'
  }),
  stageIconContainer: css.raw({
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: '14px',
    height: '14px'
  }),
  iconWrapper: css.raw({
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    height: '100%',
    width: '100%'
  }),
  stageLabel: css.raw({
    position: 'absolute',
    top: '100%',
    whiteSpace: 'nowrap',
    left: '50%',
    transform: 'translateX(-50%)'
  }),
  unscheduledCircle: css.raw({
    width: '100%',
    height: '100%',
    borderWidth: '1px',
    borderStyle: 'solid',
    borderColor: 'neutral500',
    borderRadius: '50%',
    boxSizing: 'border-box'
  })
};
