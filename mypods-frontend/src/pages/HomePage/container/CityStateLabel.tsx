import { Text } from 'pods-component-library';
import React from 'react';
import { css } from 'pods-component-library/styled-system/css';
import { TranslationKeys } from '../../../locales/TranslationKeys';

interface CityStateProps {
  address: {
    city: string;
    state: string;
  };
  testId: string;
}

export const CityStateLabel = ({ address: { city, state }, testId }: CityStateProps) => (
  <Text
    i18nKey={TranslationKeys.HomePage.Disclosure.Summary.CITY_STATE}
    values={{ city, state }}
    css={stylesheet.city}
    components={{
      soft: <strong key={crypto.randomUUID()} className={stylesheet.state} />,
      narrowNoBreakSpace: <>{'\u202F'}</>
    }}
    data-testid={testId}
  />
);

const stylesheet = {
  state: css({
    fontFamily: 'secondary',
    fontSize: 18,
    fontWeight: 400,
    letterSpacing: '0',
    lineHeight: 1.5,
    color: 'accent900'
  }),
  city: css.raw({
    fontFamily: 'primary',
    fontSize: 32,
    fontWeight: 900,
    letterSpacing: '0',
    lineHeight: 1.1875,
    color: 'accent900'
  })
};
