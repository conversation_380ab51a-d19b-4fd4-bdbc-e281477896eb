import { Text } from 'pods-component-library';
import React from 'react';
import { css } from 'pods-component-library/styled-system/css';
import { TranslationKeys } from '../../../locales/TranslationKeys';

interface CityStateProps {
  address: {
    city: string;
    state: string;
  };
  testId: string;
}
/* eslint-disable react/jsx-no-useless-fragment */
export const CityStateLabel = ({ address: { city, state }, testId }: CityStateProps) => (
  <Text
    i18nKey={TranslationKeys.HomePage.Disclosure.Summary.CITY_STATE}
    values={{ city, state }}
    css={cityStyle}
    components={{
      soft: <strong key={crypto.randomUUID()} style={stateStyle} />,
      narrowNoBreakSpace: <>{'\u202F'}</>
    }}
    data-testid={testId}
  />
);
/* eslint-enable react/jsx-no-useless-fragment */

const stateStyle = {
  fontFamily: 'secondary',
  fontSize: 18,
  fontWeight: 400,
  letterSpacing: '0',
  lineHeight: 1.5,
  color: 'accent900'
};

const cityStyle = css.raw({
  fontFamily: 'primary',
  fontSize: 32,
  fontWeight: 900,
  letterSpacing: '0',
  lineHeight: 1.1875,
  color: 'accent900'
});
