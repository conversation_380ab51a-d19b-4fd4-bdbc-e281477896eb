import { ReactNode, useEffect, useState } from 'react';
import { ContainerPlacementScreenName } from './ContainerPlacementModalTypes';
import {
  ContainerPlacement,
  FinalContainerPlacement
} from '../../../../../../domain/OrderEntities';

// With the current usage, a node either has previous, next and children
// OR it has a parent and could have children
export interface ContainerPlacementNode {
  previous?: ContainerPlacementNode;
  next?: ContainerPlacementNode;
  children: ContainerPlacementNode[];
  parent?: ContainerPlacementNode;

  name: ContainerPlacementScreenName;
  component: ReactNode;
}

export type ContainerPlacementScreenData = {
  selectedChoice?: any;
  childScreenName?: ContainerPlacementScreenName;
};

export interface IContainerPlacementScreenManager {
  currentScreen: ContainerPlacementNode;
  selectChoice: (choice: any) => void;
  selectChoiceWithChild: (choice: any, childScreen: ContainerPlacementScreenName) => void;
  hasNextStep: () => boolean;
  nextStep: () => void;
  hasPreviousStep: () => boolean;
  previousStep: () => void;
  goto: (screen: ContainerPlacementScreenName) => void;
  gotoSiteTypeSelectedChild: () => void;
  completedPercentage: () => number;
}
const getTotalSteps = (node: ContainerPlacementNode) => {
  let total = 1;
  let temp = node;
  while (temp.next != null) {
    total += 1;
    temp = temp.next!;
  }
  return total;
};

export const defaultInitialDeliveryPlacement: ContainerPlacement = {
  isPavedSurface: true,
  siteType: 'DRIVEWAY',
  placement: 'DRIVEWAY_L_SHAPED_FAR_REAR',
  driverNotes: ''
};

const getInitialScreenData = (initialContainerPlacement?: ContainerPlacement) => {
  const map = new Map<ContainerPlacementScreenName, ContainerPlacementScreenData>();
  if (initialContainerPlacement == null) return map;
  map.set('PAVED_SURFACE', { selectedChoice: initialContainerPlacement.isPavedSurface });
  map.set('PLACEMENT_TIPS', { selectedChoice: 'anything' });
  map.set('SITE_TYPE', {
    selectedChoice: initialContainerPlacement.siteType,
    childScreenName: 'DRIVEWAY_TYPE'
  });
  map.set('DRIVEWAY_TYPE', {
    selectedChoice: 'DRIVEWAY_L_SHAPED',
    childScreenName: 'DRIVEWAY_L_SHAPED'
  });
  map.set('DRIVEWAY_L_SHAPED', {
    selectedChoice: 'DRIVE_L_SHAPED_FAR',
    childScreenName: 'DRIVE_L_SHAPED_FAR'
  });
  map.set('DRIVE_L_SHAPED_FAR', {
    selectedChoice: 'DRIVEWAY_L_SHAPED_FAR_REAR' as FinalContainerPlacement
  });
  map.set('DRIVER_NOTES', {
    selectedChoice: ''
  });
  return map;
};

export const useContainerPlacementScreenManager = (
  firstNode: ContainerPlacementNode,
  initialContainerPlacement?: ContainerPlacement
) => {
  const [currentNode, setCurrentNode] = useState<ContainerPlacementNode>(firstNode);
  const [totalSteps] = useState<number>(getTotalSteps(firstNode));
  const [screenData, setScreenData] = useState<
    Map<ContainerPlacementScreenName, ContainerPlacementScreenData>
  >(getInitialScreenData(initialContainerPlacement));

  useEffect(() => {
    if (initialContainerPlacement) {
      goto('REVIEW');
    }
  }, [initialContainerPlacement]);

  const updateScreenData = (
    key: ContainerPlacementScreenName,
    data: ContainerPlacementScreenData
  ) => {
    const newMap = new Map(screenData);
    newMap.set(key, data);
    setScreenData(newMap);
    return newMap;
  };

  const selectChoice = (choice: any) => {
    const updatedScreenData = updateScreenData(currentNode.name, {
      selectedChoice: choice
    });
    setCurrentNode((prev) => getNextNode(prev, updatedScreenData) ?? prev);
  };

  const selectChoiceWithChild = (choice: any, childScreenName: ContainerPlacementScreenName) => {
    const updatedScreenData = updateScreenData(currentNode.name, {
      selectedChoice: choice,
      childScreenName
    });
    setCurrentNode((prev) => getNextNode(prev, updatedScreenData) ?? prev);
  };

  const hasNextStep = () => !!getNextNode(currentNode, screenData);

  function getRootStepNumber(rootNode: ContainerPlacementNode) {
    let count = 1;
    let temp = rootNode;
    while (temp.previous != null) {
      count += 1;
      temp = temp.previous!;
    }
    return count;
  }

  const completedPercentage = () => {
    let temp = currentNode;
    let stepCount: number;

    if (temp.parent != null) {
      let depth = 0;
      while (temp.parent != null) {
        depth += 1;
        temp = temp.parent;
      }
      const rootStepNumber = getRootStepNumber(temp);
      // if depth = 1 fraction = 1/2
      // if depth = 2 fraction = 2/3
      const fraction = depth / (1 + depth);
      stepCount = rootStepNumber + fraction;
    } else {
      stepCount = getRootStepNumber(temp);
    }
    return (stepCount / totalSteps) * 100;
  };

  const nextStep = () => {
    const nextNode = getNextNode(currentNode, screenData);
    if (nextNode) setCurrentNode(nextNode);
  };

  const hasPreviousStep = () => currentNode.parent != null || currentNode.previous;

  const previousStep = () => {
    if (currentNode.parent != null) {
      setCurrentNode(currentNode.parent);
      return;
    }

    const previousNode = currentNode.previous;
    if (previousNode) {
      const leaf = findLeaf(previousNode);
      setCurrentNode(leaf);
    }
  };

  const goto = (screen: ContainerPlacementScreenName) => {
    let nodeToCheck: ContainerPlacementNode | undefined = getFirstNode(currentNode);
    while (nodeToCheck != null) {
      if (nodeToCheck.name === screen) {
        setCurrentNode(nodeToCheck);
        return;
      }
      nodeToCheck = getNextNode(nodeToCheck, screenData);
    }
  };

  const gotoSiteTypeSelectedChild = () => {
    let siteTypeNode: ContainerPlacementNode | undefined = getFirstNode(currentNode);
    while (siteTypeNode != null) {
      if (siteTypeNode.name === 'SITE_TYPE') {
        const childScreenName = screenData.get(siteTypeNode.name)?.childScreenName;
        const selectedSiteTypeChildNode = siteTypeNode.children.find(
          (it) => it.name === childScreenName
        );
        if (selectedSiteTypeChildNode) setCurrentNode(selectedSiteTypeChildNode);
        return;
      }
      siteTypeNode = getNextNode(siteTypeNode, screenData);
    }
  };

  const getFirstNode = (node: ContainerPlacementNode) => {
    let temp = node;
    while (getPreviousNode(temp) != null) {
      temp = getPreviousNode(temp)!;
    }
    return temp;
  };

  const getNextNode = (
    node: ContainerPlacementNode,
    myScreenData: Map<ContainerPlacementScreenName, ContainerPlacementScreenData>
  ) => {
    const data = myScreenData.get(node.name);
    if (data?.selectedChoice == null) return;
    if (node.children.length > 0) {
      return node.children.find((it) => it.name === data.childScreenName);
    }
    let tempNode = node;
    while (tempNode.parent != null) {
      tempNode = tempNode.parent;
    }
    return tempNode.next;
  };

  const getPreviousNode = (node: ContainerPlacementNode) => {
    if (node.parent != null) {
      return node.parent;
    }

    const previousNode = node.previous;
    if (previousNode) {
      return findLeaf(previousNode);
    }

    return null;
  };

  const findLeaf = (node: ContainerPlacementNode) => {
    let tempNode = node;
    while (tempNode.children.length > 0) {
      const childScreenName = screenData.get(tempNode.name)?.childScreenName;
      const selectedChild = tempNode.children.find((it) => it.name === childScreenName);
      if (!selectedChild) break;
      tempNode = selectedChild;
    }
    return tempNode;
  };

  return {
    currentScreen: currentNode,
    selectChoice,
    selectChoiceWithChild,
    hasNextStep,
    nextStep,
    hasPreviousStep,
    previousStep,
    goto,
    gotoSiteTypeSelectedChild,
    completedPercentage
  } as IContainerPlacementScreenManager;
};
