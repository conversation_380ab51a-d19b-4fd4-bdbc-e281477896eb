import React from 'react';
import { Grid, useMediaQuery } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { TranslationKeys } from '../../../../../../locales/TranslationKeys';
import { theme } from '../../../../../../PodsTheme';
import { Design } from '../../../../../../helpers/Design';
import { PodsAlert } from '../../../../../../components/alert/PodsAlert';
import { StretchableLoadingButton } from '../../../../../../components/buttons/StretchableLoadingButton';
import { useContainerPlacementContext } from '../context/ContainerPlacementContext';
import { ContainerPlacementHeader } from '../ContainerPlacementHeader';

const Tx = TranslationKeys.HomePage.ContainerPlacement;

export const PavedSurfaceScreen = () => {
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = pavedSurfaceScreenStyle(isMobile);
  const { t: translate } = useTranslation();
  const { setContainerPlacement, manager } = useContainerPlacementContext();

  const handleIsPavedSurfaceClick = (isPavedSurface: boolean) => {
    setContainerPlacement((prevState) => ({
      ...prevState,
      isPavedSurface
    }));
    manager.selectChoice(isPavedSurface);
  };

  return (
    <Grid container {...styles.contentContainer}>
      <ContainerPlacementHeader
        titleKey={Tx.PavedSurfaceScreen.TITLE}
        subTitleKey={Tx.PavedSurfaceScreen.SUBTITLE}
      />
      <Grid container {...styles.alertContainer}>
        <PodsAlert
          title={translate(Tx.PavedSurfaceScreen.ALERT_TITLE)}
          description={translate(Tx.PavedSurfaceScreen.ALERT_DESCRIPTION)}
        />
      </Grid>
      <Grid container {...styles.outerButtonContainer}>
        <Grid container item spacing={2} {...styles.innerButtonContainer}>
          <Grid item xs={6}>
            <StretchableLoadingButton
              isMobile={isMobile}
              label={translate(TranslationKeys.CommonComponents.YES_BUTTON)}
              onClick={() => {
                handleIsPavedSurfaceClick(true);
              }}
              variant="outlined"
            />
          </Grid>
          <Grid item xs={6}>
            <StretchableLoadingButton
              isMobile={isMobile}
              label={translate(TranslationKeys.CommonComponents.NO_BUTTON)}
              onClick={() => {
                handleIsPavedSurfaceClick(false);
              }}
              variant="outlined"
            />
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  );
};

const pavedSurfaceScreenStyle = (isMobile: boolean) => ({
  contentContainer: {
    sx: {
      flexDirection: 'column',
      gap: Design.Primitives.Spacing.md
    }
  },
  alertContainer: {},
  outerButtonContainer: {
    sx: { justifyContent: 'center' }
  },
  innerButtonContainer: {
    sx: { maxWidth: isMobile ? undefined : '400px' }
  }
});
