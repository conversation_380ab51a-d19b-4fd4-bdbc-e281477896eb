import { getVisitorId } from '../../../../config/getVisitorId';
import { GtmScheduleType } from '../../../../config/google/GoogleEntities';
import {
  CloneQuoteFromOrderResponse,
  Container,
  ContainerPlacement,
  MoveLeg,
  Order,
  ServiceAddress,
  UpdateMoveLegRequest
} from '../../../../domain/OrderEntities';
import { formatDate } from '../../../../helpers/dateHelpers';
import { UpdateMoveLegResponse } from '../../../../networkRequests/responseEntities/OrderAPIEntities';

export type UpdateMoveLegRequestProps = {
  order: Order;
  container: Container;
  moveLeg: MoveLeg;
  selectedDate: Date;
  serviceAddress: ServiceAddress;
  completedContainerPlacement: ContainerPlacement | null | undefined;
  currentlySelectedQuote: CloneQuoteFromOrderResponse | null | undefined;
  priceDifferenceResponse: UpdateMoveLegResponse | null | undefined;
  isCancelLeg: boolean;
  isUpdating: boolean;
  isIfOpenCalendar: boolean;
};

export type LegacyUpdateMoveLegRequestProps = Omit<
  UpdateMoveLegRequestProps,
  'currentlySelectedQuote'
>;

export const isMoveOrRedelivery = (moveLeg: MoveLeg): boolean =>
  moveLeg.moveLegType === 'REDELIVERY' || moveLeg.moveLegType === 'MOVE';

export const hasAddressChanged = (moveLeg: MoveLeg, request: UpdateMoveLegRequest) => {
  if (request.serviceAddress) {
    if (moveLeg.displayAddress.address1 == null) return true;
    return !(
      moveLeg.displayAddress.address1.toLowerCase() ===
      request.serviceAddress?.address1.toLowerCase()
    );
  }
  return false;
};

export const createGtmScheduleType = (
  order: Order,
  container: Container,
  moveLeg: MoveLeg,
  request: UpdateMoveLegRequest
): GtmScheduleType => ({
  transactionId: order.orderId,
  containerId: container.containerId,
  moveLegId: moveLeg.moveLegId,
  moveLegType: moveLeg.moveLegType,
  addressChanged: hasAddressChanged(moveLeg, request),
  deliverySiteType: request.containerPlacement?.siteType.toString(),
  containerPlacement: request.containerPlacement?.placement.toString(),
  pavedSurface: request.containerPlacement?.isPavedSurface,
  gated: false, // TODO: This will need to be updated once we introduce feature
  optionalNotes: !!(request.containerPlacement && request.containerPlacement.driverNotes.length > 0)
});

export const createUpdateMoveLegRequest = (
  props: UpdateMoveLegRequestProps
): UpdateMoveLegRequest => {
  const { order, moveLeg, serviceAddress, completedContainerPlacement, priceDifferenceResponse } =
    props;
  const request: UpdateMoveLegRequest = {
    orderId: order.orderId,
    quoteId: props.currentlySelectedQuote?.newQuoteIdentity!,
    salesforceQuoteId: props.currentlySelectedQuote?.newQuoteSalesforceId!,
    containerOrderId: props.container.containerOrderId,
    moveLegId: moveLeg.moveLegId,
    moveLegType: moveLeg.moveLegType,
    requestedDate: formatDate(props.selectedDate, 'yyyy-MM-dd'),
    transitDays: moveLeg.transitDays,
    isCancelLeg: props.isCancelLeg,
    isUpdating: props.isUpdating,
    locationFields: {
      zip: serviceAddress.postalCode,
      moveLegType: props.moveLeg.moveLegType,
      orderType: order.orderType,
      siteIdentity: moveLeg.siteIdentity,
      isIfOpenCalendar: props.isIfOpenCalendar,
      custTrackingId: getVisitorId()
    }
  };
  if (isMoveOrRedelivery(moveLeg)) {
    request.serviceAddress = serviceAddress;
    request.containerPlacement = completedContainerPlacement!;
  }
  if (priceDifferenceResponse) request.quoteId = priceDifferenceResponse.quoteId;
  return request;
};

export const createLegacyUpdateMoveLegRequest = (
  props: LegacyUpdateMoveLegRequestProps
): UpdateMoveLegRequest => {
  const { order, moveLeg, serviceAddress, completedContainerPlacement, priceDifferenceResponse } =
    props;
  const request: UpdateMoveLegRequest = {
    orderId: order.orderId,
    containerOrderId: props.container.containerOrderId,
    moveLegId: moveLeg.moveLegId,
    moveLegType: moveLeg.moveLegType,
    requestedDate: formatDate(props.selectedDate, 'yyyy-MM-dd'),
    transitDays: moveLeg.transitDays,
    isCancelLeg: props.isCancelLeg,
    isUpdating: props.isUpdating,
    locationFields: {
      zip: serviceAddress.postalCode,
      moveLegType: props.moveLeg.moveLegType,
      orderType: order.orderType,
      siteIdentity: moveLeg.siteIdentity,
      isIfOpenCalendar: props.isIfOpenCalendar,
      custTrackingId: getVisitorId()
    }
  };
  if (isMoveOrRedelivery(moveLeg)) {
    request.serviceAddress = serviceAddress;
    request.containerPlacement = completedContainerPlacement!;
  }
  if (priceDifferenceResponse) request.quoteId = priceDifferenceResponse.quoteId;
  return request;
};
