import React, { Suspense } from 'react';
import { Grid, useMediaQuery } from '@mui/material';
import { PageLayout } from '../../components/PageLayout';
import { theme } from '../../PodsTheme';
import { Design } from '../../helpers/Design';
import { ContainerTileWrapper } from './container/ContainerTileWrapper';
import { ContainerSkeleton } from '../../components/Loading/ContainerSkeleton';
import { DynamicHomeAlert } from './DynamicHomeAlert';
import {
  HOME_PAGE_ALERT_ENABLED,
  QUICK_LINKS,
  useFeatureFlags
} from '../../helpers/useFeatureFlags';
import { usePreloadData } from '../../networkRequests/usePreloadData';
import { MyPodsSplashScreenModal } from './container/MyPodsSplashScreenModal';
import { QuickLinks } from './components/QuickLinks';
import { HomePageSidebar } from './HomePageSidebar';

export const HomePage = ({ topOfPageRef }: { topOfPageRef?: React.RefObject<HTMLDivElement> }) => {
  const { isHomePageAlertEnabled, isQuickLinksEnabled } = useFeatureFlags([
    HOME_PAGE_ALERT_ENABLED,
    QUICK_LINKS
  ]);
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = homePageStyles(isMobile);
  usePreloadData();
  return (
    <>
      {isHomePageAlertEnabled() && <DynamicHomeAlert />}
      <PageLayout columnsLg={8}>
        <Grid {...styles.homePage}>
          <Grid {...styles.mainBody}>
            <Grid container item xs={12} sm={8} {...styles.leftBody}>
              <Suspense fallback={<ContainerSkeleton topOfPageRef={topOfPageRef} />}>
                <ContainerTileWrapper topOfPageRef={topOfPageRef} />
              </Suspense>
            </Grid>

            {isQuickLinksEnabled() ? (
              <Grid container item xs={12} sm={4}>
                <QuickLinks />
              </Grid>
            ) : (
              <HomePageSidebar />
            )}
          </Grid>
        </Grid>
        <MyPodsSplashScreenModal />
      </PageLayout>
    </>
  );
};

const homePageStyles = (isMobile: boolean) => ({
  homePage: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      gap: Design.Primitives.Spacing.md
    }
  },
  header: {
    sx: {
      display: 'flex',
      paddingBottom: Design.Primitives.Spacing.lgPlus,
      columnGap: Design.Primitives.Spacing.sm,
      rowGap: 0,
      flexDirection: isMobile ? 'column' : 'row'
    }
  },
  mainBody: {
    sx: {
      display: 'flex',
      flexDirection: isMobile ? 'column' : 'row',
      columnGap: Design.Primitives.Spacing.sm,
      rowGap: Design.Primitives.Spacing.lg
    }
  },
  leftBody: {
    sx: {
      flexDirection: 'column',
      gap: '8px'
    }
  }
});
