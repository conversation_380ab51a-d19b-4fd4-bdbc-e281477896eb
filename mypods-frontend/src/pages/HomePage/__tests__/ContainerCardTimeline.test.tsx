import { render, screen } from '@testing-library/react';
import React from 'react';
import { ContainerCardTimeline } from '../container/ContainerCardTimeline';
import { MoveLeg, MoveLegType } from '../../../domain/OrderEntities';
import { createMoveLeg } from '../../../testUtils/MyPodsFactories';
import { addDays } from 'date-fns';
import { TranslationKeys } from '../../../locales/TranslationKeys';

const Tx = TranslationKeys.HomePage.ContainerCard.Timeline.Labels;

describe('ContainerCardTimeline', () => {
  const today = new Date();
  const fiveDaysFromToday = addDays(today, 5);

  const createTestMoveLeg = (
    moveLegType: MoveLegType,
    overrides: Partial<MoveLeg> = {}
  ): MoveLeg => {
    return createMoveLeg({
      moveLegId: `${moveLegType}-${Date.now()}`,
      moveLegType,
      scheduledStatus: 'FUTURE',
      ...overrides
    });
  };

  const renderTimeline = (moveLegs: MoveLeg[], upNextMoveLegId?: string) => {
    return render(<ContainerCardTimeline moveLegs={moveLegs} upNextMoveLegId={upNextMoveLegId} />);
  };

  describe('Stage mapping and display', () => {
    test('should map delivery move leg types to delivery stage', () => {
      const moveLegs = [
        createTestMoveLeg('INITIAL_DELIVERY'),
        createTestMoveLeg('SELF_INITIAL_DELIVERY'),
        createTestMoveLeg('CITY_SERVICE_DELIVERY')
      ];

      renderTimeline(moveLegs);

      expect(screen.getAllByText(Tx.DELIVERY, { exact: true })).toHaveLength(1);

      [Tx.PICKUP, Tx.STORAGE, Tx.TRANSIT, Tx.DROPOFF, Tx.RETURN, Tx.VISIT].forEach((key) => {
        expect(screen.queryByText(key, { exact: true })).not.toBeInTheDocument();
      });
    });

    test('should map various move leg types to correct stages', () => {
      const moveLegs = [
        createTestMoveLeg('INITIAL_DELIVERY'),
        createTestMoveLeg('PICKUP'),
        createTestMoveLeg('REDELIVERY'),
        createTestMoveLeg('FINAL_PICKUP'),
        createTestMoveLeg('MOVE')
      ];

      renderTimeline(moveLegs);

      expect(screen.getByText(Tx.DELIVERY)).toBeInTheDocument();
      expect(screen.getByText(Tx.PICKUP)).toBeInTheDocument();
      expect(screen.getByText(Tx.STORAGE)).toBeInTheDocument();
      expect(screen.getByText(Tx.DROPOFF)).toBeInTheDocument();
      expect(screen.getByText(Tx.RETURN)).toBeInTheDocument();
      expect(screen.getByText(Tx.TRANSIT)).toBeInTheDocument();
    });

    test('should not display visit move legs', () => {
      const moveLegs = [
        createTestMoveLeg('INITIAL_DELIVERY'),
        createTestMoveLeg('VISIT_CONTAINER'),
        createTestMoveLeg('PICKUP')
      ];

      renderTimeline(moveLegs);

      expect(screen.queryByText(Tx.VISIT)).not.toBeInTheDocument();
      expect(screen.getByText(Tx.DELIVERY)).toBeInTheDocument();
      expect(screen.getByText(Tx.PICKUP)).toBeInTheDocument();
    });
  });

  describe('Storage stage insertion', () => {
    test('should automatically add storage stage after pickup', () => {
      const moveLegs = [createTestMoveLeg('PICKUP')];

      renderTimeline(moveLegs);

      const stages = screen.getAllByText(
        (content) => content === Tx.PICKUP || content === Tx.STORAGE
      );

      expect(stages).toHaveLength(2);
      expect(stages[0]).toHaveTextContent(Tx.PICKUP);
      expect(stages[1]).toHaveTextContent(Tx.STORAGE);
    });

    test('should not add duplicate storage stages', () => {
      const moveLegs = [createTestMoveLeg('PICKUP'), createTestMoveLeg('VISIT_CONTAINER')];

      renderTimeline(moveLegs);

      const storageStages = screen.getAllByText(Tx.STORAGE);
      expect(storageStages).toHaveLength(1);
    });

    test('storage should inherit completed status from pickup', () => {
      const moveLegs = [createTestMoveLeg('PICKUP', { scheduledStatus: 'PAST' })];

      renderTimeline(moveLegs);

      const checkmarkIcons = screen.getAllByTestId('timeline-checkmark-icon');
      expect(checkmarkIcons).toHaveLength(2);
    });

    test('storage should not inherit completed status when pickup is not completed', () => {
      const moveLegs = [
        createTestMoveLeg('PICKUP', { scheduledStatus: 'FUTURE', scheduledDate: fiveDaysFromToday })
      ];

      renderTimeline(moveLegs);

      const checkmarkIcons = screen.queryAllByTestId('timeline-checkmark-icon');
      expect(checkmarkIcons).toHaveLength(0);
    });
  });

  describe('Icon display logic', () => {
    const TRUCK_ELIGIBLE_STAGES: MoveLegType[] = [
      'PICKUP',
      'REDELIVERY',
      'FINAL_PICKUP',
      'INITIAL_DELIVERY',
      'MOVE'
    ];

    test.each(TRUCK_ELIGIBLE_STAGES)(
      'should show truck icon for active %s move leg',
      (moveLegType) => {
        const moveLegId = 'active-leg';
        const moveLegs = [
          createTestMoveLeg(moveLegType, {
            moveLegId,
            scheduledStatus: 'FUTURE',
            scheduledDate: fiveDaysFromToday
          })
        ];

        renderTimeline(moveLegs, moveLegId);

        const truckIcon = screen.getByTestId('timeline-truck-icon');
        expect(truckIcon).toBeInTheDocument();
      }
    );

    test('should not show truck icon for storage stage even when active', () => {
      const moveLegId = 'pickup-leg';
      const moveLegs = [
        createTestMoveLeg('PICKUP', {
          moveLegId,
          scheduledStatus: 'FUTURE',
          scheduledDate: fiveDaysFromToday
        })
      ];

      renderTimeline(moveLegs, moveLegId);

      const truckIcons = screen.getAllByTestId('timeline-truck-icon');
      expect(truckIcons).toHaveLength(1);
    });

    test('should show checkmark for completed stages', () => {
      const moveLegs = [
        createTestMoveLeg('INITIAL_DELIVERY', { scheduledStatus: 'PAST' }),
        createTestMoveLeg('PICKUP', { scheduledStatus: 'FUTURE', scheduledDate: fiveDaysFromToday })
      ];

      renderTimeline(moveLegs);

      const checkmarks = screen.getAllByTestId('timeline-checkmark-icon');
      expect(checkmarks).toHaveLength(1);
    });

    test('should show empty circle for unscheduled stages', () => {
      const moveLegs = [
        createTestMoveLeg('INITIAL_DELIVERY', {
          scheduledStatus: 'UNSCHEDULED',
          scheduledDate: undefined
        })
      ];

      renderTimeline(moveLegs);

      const emptyCircle = screen.getByTestId('timeline-empty-circle-icon');
      expect(emptyCircle).toBeInTheDocument();
    });
  });

  describe('Stage deduplication', () => {
    test('should only show unique stages', () => {
      const moveLegs = [
        createTestMoveLeg('INITIAL_DELIVERY'),
        createTestMoveLeg('SELF_INITIAL_DELIVERY'),
        createTestMoveLeg('CITY_SERVICE_DELIVERY'),
        createTestMoveLeg('MOVE'),
        createTestMoveLeg('PORT_TO_PORT')
      ];

      renderTimeline(moveLegs);

      const deliveryStages = screen.getAllByText(Tx.DELIVERY);
      const transitStages = screen.getAllByText(Tx.TRANSIT);

      expect(deliveryStages).toHaveLength(1);
      expect(transitStages).toHaveLength(1);
    });

    test('should use status from most relevant move leg', () => {
      const activeLegId = 'active-transit';
      const moveLegs = [
        createTestMoveLeg('MOVE', { scheduledStatus: 'FUTURE', scheduledDate: fiveDaysFromToday }),
        createTestMoveLeg('PORT_TO_PORT', {
          moveLegId: activeLegId,
          scheduledStatus: 'FUTURE',
          scheduledDate: fiveDaysFromToday
        })
      ];

      renderTimeline(moveLegs, activeLegId);

      const truckIcon = screen.getByTestId('timeline-truck-icon');
      expect(truckIcon).toBeInTheDocument();
    });

    test('completed status should override uncompleted for same stage type', () => {
      const moveLegs = [
        createTestMoveLeg('INITIAL_DELIVERY', { scheduledStatus: 'FUTURE' }),
        createTestMoveLeg('SELF_INITIAL_DELIVERY', { scheduledStatus: 'PAST' })
      ];

      renderTimeline(moveLegs);

      const checkmark = screen.getByTestId('timeline-checkmark-icon');
      expect(checkmark).toBeInTheDocument();
    });
  });

  describe('Line styling', () => {
    test('should show completed line style between completed stages', () => {
      const moveLegs = [
        createTestMoveLeg('INITIAL_DELIVERY', { scheduledStatus: 'PAST' }),
        createTestMoveLeg('PICKUP', { scheduledStatus: 'PAST' }),
        createTestMoveLeg('REDELIVERY', {
          scheduledStatus: 'FUTURE',
          scheduledDate: fiveDaysFromToday
        })
      ];

      const { container } = renderTimeline(moveLegs);
      const lines = container.querySelectorAll('[data-complete="true"]');
      expect(lines).toHaveLength(2);
    });

    test('should not show completed line style after uncompleted stages', () => {
      const moveLegs = [
        createTestMoveLeg('INITIAL_DELIVERY', {
          scheduledStatus: 'FUTURE',
          scheduledDate: fiveDaysFromToday
        }),
        createTestMoveLeg('PICKUP', { scheduledStatus: 'FUTURE', scheduledDate: fiveDaysFromToday })
      ];

      const { container } = renderTimeline(moveLegs);

      const lines = container.querySelectorAll('[data-complete="true"]');
      expect(lines).toHaveLength(0);
    });

    test('last stage should not have a line after it', () => {
      const moveLegs = [createTestMoveLeg('INITIAL_DELIVERY'), createTestMoveLeg('PICKUP')];

      const { container } = renderTimeline(moveLegs);

      const stages = screen.getAllByText(
        (content) => content === Tx.PICKUP || content === Tx.STORAGE || content === Tx.DELIVERY
      );
      expect(stages).toHaveLength(3);

      const lines = container.querySelectorAll('.line, [class*="line"]');
      expect(lines.length).toBeLessThan(stages.length);
    });
  });

  describe('Complex scenarios', () => {
    test('should handle full container journey correctly', () => {
      const moveLegs = [
        createTestMoveLeg('INITIAL_DELIVERY', { scheduledStatus: 'PAST' }),
        createTestMoveLeg('PICKUP', { scheduledStatus: 'PAST' }),
        createTestMoveLeg('STORAGE_CENTER_RELOCATION', { scheduledStatus: 'PAST' }),
        createTestMoveLeg('REDELIVERY', {
          scheduledStatus: 'FUTURE',
          scheduledDate: fiveDaysFromToday
        }),
        createTestMoveLeg('FINAL_PICKUP', {
          scheduledStatus: 'UNSCHEDULED',
          scheduledDate: undefined
        })
      ];

      renderTimeline(moveLegs);

      expect(screen.getByText(Tx.DELIVERY)).toBeInTheDocument();
      expect(screen.getByText(Tx.PICKUP)).toBeInTheDocument();
      expect(screen.getByText(Tx.STORAGE)).toBeInTheDocument();
      expect(screen.getByText(Tx.TRANSIT)).toBeInTheDocument();
      expect(screen.getByText(Tx.DROPOFF)).toBeInTheDocument();
      expect(screen.getByText(Tx.RETURN)).toBeInTheDocument();

      const checkmarks = document.querySelectorAll('[data-testid="timeline-checkmark-icon"]');
      expect(checkmarks).toHaveLength(4);
    });

    test('should handle edge case with only visit move legs', () => {
      const moveLegs = [createTestMoveLeg('VISIT_CONTAINER'), createTestMoveLeg('VISIT_CONTAINER')];

      const { container } = renderTimeline(moveLegs);

      const stages = container.querySelectorAll('[class*="stageWrapper"]');
      expect(stages).toHaveLength(0);
    });

    test('should maintain order of stages as they appear in move legs', () => {
      const moveLegs = [
        createTestMoveLeg('PICKUP'),
        createTestMoveLeg('INITIAL_DELIVERY'),
        createTestMoveLeg('FINAL_PICKUP')
      ];

      renderTimeline(moveLegs);
      const KEYS = [Tx.DELIVERY, Tx.PICKUP, Tx.STORAGE, Tx.RETURN];

      const stages = screen.getAllByText((content) => KEYS.includes(content));

      expect(stages[0]).toHaveTextContent(Tx.PICKUP);
      expect(stages[1]).toHaveTextContent(Tx.STORAGE);
      expect(stages[2]).toHaveTextContent(Tx.DELIVERY);
      expect(stages[3]).toHaveTextContent(Tx.RETURN);
    });
  });
});
