import { screen } from '@testing-library/react';
import {
  renderWithPoetProvidersAndState,
  runPendingPromises,
  testQueryClient
} from '../../../testUtils/RenderHelpers';
import { HomePage } from '../HomePage';
import {
  createContainer,
  createCustomer,
  createMoveLeg,
  createOrder,
  createRefreshSessionClaims,
  createUseFeatureFlagResult
} from '../../../testUtils/MyPodsFactories';
import React from 'react';
import { Order } from '../../../domain/OrderEntities';
import {
  mockedUseFeatureFlags,
  mockGetBillingInformation,
  mockGetCustomer,
  mockGetCustomerDocuments,
  mockGetCustomerOrders,
  mockGetOrderDocuments,
  mockGetPaymentMethods,
  mockRefreshSession
} from '../../../../setupTests';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { useGetCustomerOrders } from '../../../networkRequests/queries/useGetCustomerOrders';
import { QueryCacheKeys } from '../../../networkRequests/QueryCacheKeys';

const mockUseGetCustomerOrders = vi.hoisted(() => vi.fn<typeof useGetCustomerOrders>());

vi.mock('../../../networkRequests/queries/useGetCustomerOrders', () => {
  return {
    useGetCustomerOrders: mockUseGetCustomerOrders
  };
});

describe('HomePage', () => {
  // - constants
  let orders: Order[];
  const sixteenFootContainer = createContainer({
    containerSize: '16',
    containerId: '789456123',
    moveLegs: []
  });
  const eightFootContainer = createContainer({
    containerSize: '8',
    containerId: '321654987',
    moveLegs: [createMoveLeg({ moveLegId: '1' })]
  });

  beforeEach(() => {
    mockGetPaymentMethods.mockResolvedValue([]);
    mockGetBillingInformation.mockResolvedValue({});
    mockGetCustomerDocuments.mockResolvedValue([]);
    mockGetCustomerOrders.mockResolvedValue([]);
    mockGetOrderDocuments.mockResolvedValue([]);
  });

  function renderPage() {
    const queryClient = testQueryClient();
    testQueryClient().setQueryData([QueryCacheKeys.REFRESH_KEY], createRefreshSessionClaims());
    return renderWithPoetProvidersAndState(<HomePage />, {
      initialEntries: ['/'],
      customQueryClient: queryClient
    });
  }

  describe('when orders have loaded', () => {
    beforeEach(async () => {
      mockRefreshSession.mockResolvedValue(createRefreshSessionClaims());
      orders = [createOrder({ containers: [eightFootContainer, sixteenFootContainer] })];
      mockGetCustomer.mockResolvedValue(createCustomer());
      mockGetCustomerOrders.mockResolvedValue(orders);
      mockUseGetCustomerOrders.mockImplementation(
        (await vi.importActual('../../../networkRequests/queries/useGetCustomerOrders'))
          .useGetCustomerOrders as any
      );
    });

    it('should display multiple containers for an order', async () => {
      renderPage();
      mockRefreshSession.mockResolvedValue(createRefreshSessionClaims());
      expect(await screen.findByTestId(eightFootContainer.containerId)).toBeInTheDocument();
      expect(await screen.findByTestId(sixteenFootContainer.containerId)).toBeInTheDocument();
    });
  });

  describe('when orders are loading', () => {
    beforeEach(() => {
      mockRefreshSession.mockResolvedValue(createRefreshSessionClaims());
      mockUseGetCustomerOrders.mockImplementation(() => {
        throw new Promise(() => {});
      });
    });

    it('should display the container skeleton view', async () => {
      mockGetCustomer.mockResolvedValue(createCustomer());
      const result = renderPage();
      await runPendingPromises();

      expect(result.baseElement.querySelector(`.MuiSkeleton-root`)).toBeInTheDocument();
    });
  });

  describe('when there is an error', () => {
    const Tx = TranslationKeys.HomePage.SkeletonLoader;

    beforeEach(() => {
      mockRefreshSession.mockResolvedValue(createRefreshSessionClaims());
      // @ts-ignore
      mockUseGetCustomerOrders.mockImplementation(() => {
        return {
          data: [],
          customerOrders: [],
          error: new Error('ope, no orders'),
          isError: true,
          isLoadingError: true,
          isSuccess: false,
          status: 'error',
          refetch: () => {}
        };
      });
    });

    it('should display the error fallback & skeleton', async () => {
      mockGetCustomer.mockResolvedValue(createCustomer());
      const result = renderPage();
      expect(await screen.findByText(Tx.HEADER)).toBeInTheDocument();
      expect(await screen.findByText(Tx.SUB_HEADER)).toBeInTheDocument();
      expect(result.baseElement.querySelector(`.MuiSkeleton-root`)).toBeInTheDocument();
    });
  });

  describe('home page alert', () => {
    const mockIsHomePageAlertEnabled = vi.fn();

    beforeEach(async () => {
      mockRefreshSession.mockResolvedValue(createRefreshSessionClaims());
      mockGetCustomer.mockResolvedValue(createCustomer());
      mockedUseFeatureFlags.useFeatureFlags.mockReturnValue(
        createUseFeatureFlagResult({ isHomePageAlertEnabled: mockIsHomePageAlertEnabled })
      );
      orders = [createOrder({ containers: [eightFootContainer, sixteenFootContainer] })];
      mockGetCustomerOrders.mockResolvedValue(orders);
      mockUseGetCustomerOrders.mockImplementation(
        (await vi.importActual('../../../networkRequests/queries/useGetCustomerOrders'))
          .useGetCustomerOrders as any
      );
    });

    it('should display when isHomePageAlertEnabled returns true', async () => {
      mockIsHomePageAlertEnabled.mockReturnValue(true);

      renderPage();

      expect(await screen.findByText('pb-mypods-home-alert.title')).toBeInTheDocument();
      expect(await screen.findByText('pb-mypods-home-alert.body')).toBeInTheDocument();
    });

    it('should not display when isHomePageAlertEnabled returns false', async () => {
      mockIsHomePageAlertEnabled.mockReturnValue(false);

      renderPage();

      expect(screen.queryByText('pb-mypods-home-alert.title')).not.toBeInTheDocument();
      expect(screen.queryByText('pb-mypods-home-alert.body')).not.toBeInTheDocument();
    });
  });
});
