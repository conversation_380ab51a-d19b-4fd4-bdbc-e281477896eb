import { render, screen } from '@testing-library/react';
import {
  createContainer,
  createMoveLeg,
  createMoveLegAddress,
  createOrder,
  createUseFeatureFlagResult
} from '../../../testUtils/MyPodsFactories';
import React, { Suspense } from 'react';
import { ContainerCard } from '../container/ContainerCard';
import { ContainerProvider } from '../../../context/ContainerContext';
import { Container, Order, MoveLeg, MoveLegType } from '../../../domain/OrderEntities';
import { addDays } from 'date-fns';
import { QueryClientProvider } from '@tanstack/react-query';
import { testQueryClient } from '../../../testUtils/RenderHelpers';
import { SingleOrderProvider } from '../../../context/SingleOrderContext';
import { mockedUseFeatureFlags } from '../../../../setupTests';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { formatAddress } from '../../../networkRequests/responseEntities/CustomerEntities';
import { useTranslation } from 'react-i18next';
import { MemoryRouter } from 'react-router';

const Tx = TranslationKeys.HomePage;
const { t: translate } = useTranslation();

describe('Testing Container card status', () => {
  const today = new Date();
  const tomorrow = addDays(today, 1);
  const fiveDaysFromToday = addDays(today, 5);
  const orderId = '123456789';
  const containerId = 'CONTAINER123';
  const containerSize = '20ft';

  const baseMoveLeg = createMoveLeg({ scheduledDate: tomorrow });
  const baseOrder = createOrder({
    orderId,
    orderDate: new Date('2024-04-25T09:53:00.3731149'),
    containers: [
      createContainer({
        containerId,
        containerSize,
        moveLegs: [baseMoveLeg]
      })
    ]
  });

  const renderContainerCard = async (
    renderOrder: Order = baseOrder,
    renderContainer: Container = renderOrder.containers[0]
  ) => {
    render(
      <MemoryRouter>
        <Suspense>
          <QueryClientProvider client={testQueryClient()}>
            <SingleOrderProvider state={{ order: renderOrder }}>
              <ContainerProvider state={{ container: renderContainer, order: renderOrder }}>
                <ContainerCard />
              </ContainerProvider>
            </SingleOrderProvider>
          </QueryClientProvider>
        </Suspense>
      </MemoryRouter>
    );
  };

  const createTestMoveLeg = (overrides: Partial<MoveLeg> = {}): MoveLeg => {
    return createMoveLeg({
      eta: undefined,
      isUpNext: true,
      displayAddress: createMoveLegAddress(),
      scheduledStatus: 'FUTURE',
      ...overrides
    });
  };

  const createTestContainer = (moveLeg: MoveLeg): Container => {
    return createContainer({
      containerId,
      containerSize,
      moveLegs: [moveLeg],
      upNextMoveLegId: moveLeg.moveLegId
    });
  };

  const getExpectedTitle = (
    date: Date,
    context: string,
    eta?: string,
    useTodayTomorrow: boolean = false
  ): string => {
    let baseTitle: string;

    if (useTodayTomorrow) {
      const isToday = date.toDateString() === today.toDateString();
      const count = isToday ? 0 : 1;
      baseTitle = translate(Tx.ContainerCard.SERVICE_DATE_TODAY_TOMORROW, { count, context });
    } else {
      const day = date.getDate();
      const month = date.toLocaleDateString('en-US', { month: 'short' });
      baseTitle = translate(Tx.ContainerCard.SERVICE_DATE, { day, month, context });
    }

    return eta ? `${baseTitle}, ${eta}` : baseTitle;
  };

  const expectTitleAndSubtitle = async (expectedTitle: string, moveLeg: MoveLeg): Promise<void> => {
    expect(await screen.getByTestId('container-status-title')).toHaveTextContent(expectedTitle);
    expect(await screen.getByTestId('container-status-subtitle')).toHaveTextContent(
      formatAddress(moveLeg.displayAddress)
    );
  };

  beforeEach(() => {
    mockedUseFeatureFlags.useFeatureFlags.mockImplementation(() =>
      createUseFeatureFlagResult({ isContainerCardsEnabled: () => true })
    );
  });

  describe('Unscheduled container status', () => {
    test.each([
      {
        testName: 'should render unscheduled with address in subtitle if displayAddress is set',
        displayAddress: createMoveLegAddress(),
        expectedSubtitle: (displayAddress: any) => formatAddress(displayAddress)
      },
      {
        testName:
          'should render unscheduled with unscheduled subtitle if displayAddress is not set',
        displayAddress: undefined,
        expectedSubtitle: () => Tx.ContainerCard.Status.UNSCHEDULED_CONTAINER_SUBTITLE
      }
    ])('$testName', async ({ displayAddress, expectedSubtitle }) => {
      const unscheduledMoveLeg = createTestMoveLeg({
        scheduledDate: undefined,
        scheduledStatus: 'UNSCHEDULED',
        displayAddress
      });

      const containerWithUnscheduledLeg = createTestContainer(unscheduledMoveLeg);
      await renderContainerCard(baseOrder, containerWithUnscheduledLeg);

      expect(await screen.getByTestId('container-status-title')).toHaveTextContent(
        Tx.ContainerCard.Status.UNSCHEDULED_CONTAINER_TITLE
      );
      expect(await screen.getByTestId('container-status-subtitle')).toHaveTextContent(
        expectedSubtitle(displayAddress)
      );
    });
  });

  describe('Scheduled container status - Today/Tomorrow format', () => {
    test.each([
      {
        date: today,
        dateName: 'today',
        context: 'delivery',
        moveLegType: 'INITIAL_DELIVERY' as MoveLegType
      },
      {
        date: tomorrow,
        dateName: 'tomorrow',
        context: 'delivery',
        moveLegType: 'INITIAL_DELIVERY' as MoveLegType
      },
      {
        date: today,
        dateName: 'today',
        context: 'selfdelivery',
        moveLegType: 'SELF_INITIAL_DELIVERY' as MoveLegType
      },
      {
        date: tomorrow,
        dateName: 'tomorrow',
        context: 'selfdelivery',
        moveLegType: 'SELF_INITIAL_DELIVERY' as MoveLegType
      },
      { date: today, dateName: 'today', context: 'pickup', moveLegType: 'PICKUP' as MoveLegType },
      {
        date: tomorrow,
        dateName: 'tomorrow',
        context: 'pickup',
        moveLegType: 'PICKUP' as MoveLegType
      },
      {
        date: today,
        dateName: 'today',
        context: 'selfpickup',
        moveLegType: 'SELF_FINAL_PICKUP' as MoveLegType
      },
      {
        date: tomorrow,
        dateName: 'tomorrow',
        context: 'selfpickup',
        moveLegType: 'SELF_FINAL_PICKUP' as MoveLegType
      }
    ])(
      'should show "$context $dateName" format for $moveLegType',
      async ({ date, context, moveLegType }) => {
        const moveLeg = createTestMoveLeg({
          scheduledDate: date,
          moveLegType
        });

        const container = createTestContainer(moveLeg);
        await renderContainerCard(baseOrder, container);

        const expectedTitle = getExpectedTitle(date, context, undefined, true);
        await expectTitleAndSubtitle(expectedTitle, moveLeg);
      }
    );
  });

  describe('Scheduled container status - Regular date format', () => {
    test.each([
      { context: 'delivery', moveLegType: 'INITIAL_DELIVERY' as MoveLegType },
      { context: 'selfdelivery', moveLegType: 'SELF_INITIAL_DELIVERY' as MoveLegType },
      { context: 'pickup', moveLegType: 'PICKUP' as MoveLegType },
      { context: 'selfpickup', moveLegType: 'SELF_FINAL_PICKUP' as MoveLegType },
      { context: 'move', moveLegType: 'MOVE' as MoveLegType },
      { context: 'transit', isTransitLeg: true }
    ])(
      'should show regular date format for $context (5 days from now)',
      async ({ context, moveLegType, isTransitLeg }) => {
        const moveLeg = createTestMoveLeg({
          scheduledDate: fiveDaysFromToday,
          moveLegType,
          isTransitLeg: isTransitLeg || false
        });

        const container = createTestContainer(moveLeg);
        await renderContainerCard(baseOrder, container);

        const expectedTitle = getExpectedTitle(fiveDaysFromToday, context);
        await expectTitleAndSubtitle(expectedTitle, moveLeg);
      }
    );

    it('should show visit format when containerVisitDate is set', async () => {
      const moveLeg = createTestMoveLeg({
        scheduledDate: fiveDaysFromToday,
        containerVisitDate: fiveDaysFromToday
      });

      const container = createTestContainer(moveLeg);
      await renderContainerCard(baseOrder, container);

      const expectedTitle = getExpectedTitle(fiveDaysFromToday, 'visit');
      await expectTitleAndSubtitle(expectedTitle, moveLeg);
    });
  });

  describe('Scheduled container status - With ETA', () => {
    test.each([
      {
        testName: 'should include ETA in title when provided',
        eta: '9:00 AM - 5:00 PM',
        shouldIncludeEta: true
      },
      {
        testName: 'should not include ETA in title when not provided',
        eta: undefined,
        shouldIncludeEta: false
      }
    ])('$testName', async ({ eta, shouldIncludeEta }) => {
      const moveLeg = createTestMoveLeg({
        scheduledDate: fiveDaysFromToday,
        eta
      });

      const container = createTestContainer(moveLeg);
      await renderContainerCard(baseOrder, container);

      const expectedTitle = getExpectedTitle(fiveDaysFromToday, 'delivery', eta);

      if (shouldIncludeEta && eta) {
        expect(await screen.getByTestId('container-status-title')).toHaveTextContent(expectedTitle);
      } else {
        const titleWithoutEta = getExpectedTitle(fiveDaysFromToday, 'delivery');
        expect(await screen.getByTestId('container-status-title')).toHaveTextContent(
          titleWithoutEta
        );
        expect(await screen.getByTestId('container-status-title')).not.toHaveTextContent(
          '9:00 AM - 5:00 PM'
        );
      }

      expect(await screen.getByTestId('container-status-subtitle')).toHaveTextContent(
        formatAddress(moveLeg.displayAddress)
      );
    });

    test.each([
      { date: today, dateName: 'today', eta: '10:00 AM - 2:00 PM' },
      { date: tomorrow, dateName: 'tomorrow', eta: '1:00 PM - 6:00 PM' }
    ])('should include ETA with today/tomorrow format for $dateName', async ({ date, eta }) => {
      const moveLeg = createTestMoveLeg({
        scheduledDate: date,
        eta
      });

      const container = createTestContainer(moveLeg);
      await renderContainerCard(baseOrder, container);

      const expectedTitle = getExpectedTitle(date, 'delivery', eta, true);
      await expectTitleAndSubtitle(expectedTitle, moveLeg);
    });
  });
});
