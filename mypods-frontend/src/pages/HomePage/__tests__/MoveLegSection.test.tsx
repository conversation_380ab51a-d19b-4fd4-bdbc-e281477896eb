import { cleanup, fireEvent, render, screen, within } from '@testing-library/react';
import { ContainerProvider } from '../../../context/ContainerContext';
import { MoveLegSection } from '../container/moveleg/current/MoveLegSection';
import {
  createCloneQuoteFromOrderResponse,
  createContainer,
  createMoveLeg,
  createOrder,
  createRefreshSessionClaims,
  createUseFeatureFlagResult,
  getThirtyDaysOfAvailability
} from '../../../testUtils/MyPodsFactories';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import {
  CloneQuoteFromOrderResponse,
  Container,
  ContainerPlacement,
  MoveLeg,
  MoveLegType,
  MoveLegTypeEnum,
  Order
} from '../../../domain/OrderEntities';
import { expect, vi } from 'vitest';
import React, { Suspense } from 'react';
import { QueryClientProvider } from '@tanstack/react-query';
import { runPendingPromises, testQueryClient } from '../../../testUtils/RenderHelpers';
import { NotificationProvider } from '../../../components/notifications/NotificationContext';

import { formatETA, formatToLocale } from '../../../helpers/dateHelpers';
import userEvent from '@testing-library/user-event';
import { moveLegActions, moveLegViews } from './MoveLegViews';
import { addDays, subDays } from 'date-fns';
import { MoveLegProvider } from '../container/moveleg/MoveLegContext';
import { getDatePickerLabelKey } from '../../../locales/TranslationConstants';
import {
  mockAcceptInitialDeliveryPlacement,
  mockCloneQuoteFromOrder,
  mockedUseFeatureFlags,
  mockGetContainerAvailability,
  mockRefreshSession,
  mockUpdateMoveLeg
} from '../../../../setupTests';
import { SingleOrderProvider } from '../../../context/SingleOrderContext';
import {
  initialContainerPlacement,
  useContainerPlacementContext
} from '../container/scheduling/containerplacement/context/ContainerPlacementContext';
import { ContainerPlacementModalProps } from '../container/scheduling/containerplacement/ContainerPlacementModal';
import Button from '@mui/material/Button';
import { ContainerAvailabilityResponse } from '../../../networkRequests/responseEntities/AvailabilityAPIEntities';
import { WhichStackContext, Stack } from '../../../context/WhichStackContext';

const Tx = TranslationKeys.HomePage.MoveLegs;
const TxDateLabels = TranslationKeys.HomePage.MoveLegs.Scheduling.DateLabels;
const smsButtonText = `${Tx.Title.SMS_BUTTON}[697637]`;

let testContainerPlacement: ContainerPlacement = initialContainerPlacement;
vi.mock('../container/scheduling/containerplacement/ContainerPlacementModal', () => ({
  ContainerPlacementModal: (_: ContainerPlacementModalProps) => {
    const { handleFinish } = useContainerPlacementContext();
    return (
      <Button
        onClick={() => {
          handleFinish(testContainerPlacement);
        }}>
        {TranslationKeys.HomePage.ContainerPlacement.ReviewScreen.FINISH_BUTTON}
      </Button>
    );
  }
}));

describe('MoveLegSection', () => {
  const actions = moveLegActions(userEvent.setup({ delay: null }));
  const todayJsDate = new Date('2024-01-01');
  const todayDate = new Date('2024-01-01')!;
  const futureDate = addDays(todayDate, 7);
  const pastDate = subDays(todayDate, 7);
  const baseOrder = createOrder();

  const renderMoveLeg = async (
    moveLeg: MoveLeg,
    container: Container = createContainer({ moveLegs: [moveLeg] }),
    order: Order = { ...baseOrder, containers: [container] },
    quote: CloneQuoteFromOrderResponse = createCloneQuoteFromOrderResponse()
  ) => {
    mockCloneQuoteFromOrder.mockResolvedValue(quote);

    const result = render(
      <WhichStackContext.Provider value={Stack.POET}>
        <Suspense fallback={'Loading the refreshSession'}>
          <QueryClientProvider client={testQueryClient()}>
            <NotificationProvider>
              <SingleOrderProvider state={{ order }}>
                <ContainerProvider state={{ container, order }}>
                  <MoveLegProvider
                    moveLeg={moveLeg}
                    lastMoveLegId={''}
                    isLastRenderedMoveLeg={false}>
                    <MoveLegSection />
                  </MoveLegProvider>
                </ContainerProvider>
              </SingleOrderProvider>
            </NotificationProvider>
          </QueryClientProvider>
        </Suspense>
      </WhichStackContext.Provider>
    );
    await runPendingPromises();
    return result;
  };

  beforeEach(() => {
    vi.useFakeTimers({ now: todayJsDate });
    const thirtyDays = getThirtyDaysOfAvailability();
    mockRefreshSession.mockResolvedValue(createRefreshSessionClaims());
    mockedUseFeatureFlags.useFeatureFlags.mockImplementation(() =>
      createUseFeatureFlagResult({ isOrderModEnabled: () => true, isPoetEnabled: () => true })
    );
    const response: ContainerAvailabilityResponse = {
      eightFootAvailability: thirtyDays,
      twelveFootAvailability: thirtyDays,
      sixteenFootAvailability: thirtyDays
    };
    mockGetContainerAvailability.mockResolvedValue(response);
    mockCloneQuoteFromOrder.mockResolvedValue(createCloneQuoteFromOrderResponse());
  });

  afterEach(() => {
    vi.useRealTimers();
    vi.clearAllMocks();
    cleanup();
  });

  describe('General Behavior', () => {
    it('should display schedule button if move leg is unscheduled and orderMod is enabled', async () => {
      const moveLeg = createMoveLeg({
        scheduledDate: undefined
      });

      await renderMoveLeg(moveLeg);

      expect(moveLegViews.scheduleButton()).toBeInTheDocument();
    });

    it('should display edit button if move leg is scheduled and orderMod is enabled', async () => {
      let date = new Date();
      date.setDate(date.getDate() + 2);
      const moveLeg = createMoveLeg({ scheduledDate: date });

      await renderMoveLeg(moveLeg);

      expect(moveLegViews.editButton()).toBeInTheDocument();
    });

    it('should NOT display schedule button if move leg is unscheduled and orderMod disabled', async () => {
      const moveLeg = createMoveLeg({
        scheduledDate: undefined
      });
      mockedUseFeatureFlags.useFeatureFlags.mockImplementation(() =>
        createUseFeatureFlagResult({ isOrderModEnabled: () => false })
      );

      await renderMoveLeg(moveLeg);

      expect(moveLegViews.scheduleButton()).not.toBeInTheDocument();
    });

    it('should NOT display edit button if move leg is scheduled and orderMod disabled', async () => {
      let date = new Date();
      date.setDate(date.getDate() + 2);
      const moveLeg = createMoveLeg({ scheduledDate: date });
      mockedUseFeatureFlags.useFeatureFlags.mockImplementation(() =>
        createUseFeatureFlagResult({ isOrderModEnabled: () => false })
      );

      await renderMoveLeg(moveLeg);

      expect(moveLegViews.editButton()).not.toBeInTheDocument();
    });

    it('should display schedule button if move leg is unscheduled and poetEnabled and orderModEnabled', async () => {
      const moveLeg = createMoveLeg({
        scheduledDate: undefined
      });
      mockedUseFeatureFlags.useFeatureFlags.mockImplementation(() =>
        createUseFeatureFlagResult({ isPoetEnabled: () => true, isOrderModEnabled: () => true })
      );

      await renderMoveLeg(moveLeg);

      expect(moveLegViews.scheduleButton()).toBeInTheDocument();
    });

    it('should display edit button if move leg is scheduled and poetEnabled and orderModEnabled', async () => {
      let date = new Date();
      date.setDate(date.getDate() + 2);
      const moveLeg = createMoveLeg({ scheduledDate: date });
      mockedUseFeatureFlags.useFeatureFlags.mockImplementation(() =>
        createUseFeatureFlagResult({ isPoetEnabled: () => true, isOrderModEnabled: () => true })
      );

      await renderMoveLeg(moveLeg);

      expect(moveLegViews.editButton()).toBeInTheDocument();
    });

    it('should display transit leg component, when given type is a transit leg: ', async () => {
      const inTransitLeg = createMoveLeg({ isTransitLeg: true });

      await renderMoveLeg(inTransitLeg);

      expect(screen.getByText(Tx.Title.IN_TRANSIT)).toBeInTheDocument();
      expect(screen.getByText(Tx.IN_TRANSIT_DESCRIPTION)).toBeInTheDocument();
    });

    it('should display Chat or SMS component when transit leg is warehouse to warehouse and unscheduled', async () => {
      const unscheduledWTWLeg = createMoveLeg({
        moveLegType: 'WAREHOUSE_TO_WAREHOUSE',
        scheduledDate: undefined,
        isSchedulableOnline: false
      });

      await renderMoveLeg(unscheduledWTWLeg);

      expect(screen.getByText(Tx.Title.CHAT_BUTTON)).toBeInTheDocument();
      expect(screen.getByText(smsButtonText)).toBeInTheDocument();
    });

    it('should display arrival date and move date for a visit container leg', async () => {
      const storageMoveLeg = createMoveLeg({
        scheduledDate: pastDate,
        moveLegType: 'VISIT_CONTAINER',
        arrivalDate: todayDate,
        moveDate: futureDate
      });

      await renderMoveLeg(storageMoveLeg);

      expect(screen.getByText(TxDateLabels.LABEL_ARRIVAL)).toBeInTheDocument();
      expect(screen.getByText(formatToLocale(storageMoveLeg.arrivalDate!))).toBeInTheDocument();
      expect(screen.getByText(TxDateLabels.LABEL_MOVE)).toBeInTheDocument();
      expect(screen.getByText(formatToLocale(storageMoveLeg.moveDate!))).toBeInTheDocument();
    });

    it('should display container at warehouse title', async () => {
      const storageMoveLeg = createMoveLeg({
        scheduledDate: pastDate,
        moveLegType: 'VISIT_CONTAINER',
        arrivalDate: todayDate,
        moveDate: futureDate
      });

      await renderMoveLeg(storageMoveLeg);

      expect(
        within(screen.getByTestId('move-leg-title-container')).getByText(
          TranslationKeys.HomePage.MoveLegs.Title.CONTAINER_AT_WAREHOUSE
        )
      ).toBeInTheDocument();
    });

    it('should display arrival date and move date for a visit container move leg', async () => {
      const visitContainerMoveLeg = createMoveLeg({
        scheduledDate: todayDate,
        moveLegType: 'VISIT_CONTAINER',
        arrivalDate: pastDate,
        moveDate: futureDate
      });

      await renderMoveLeg(visitContainerMoveLeg);

      expect(screen.getByText(TxDateLabels.LABEL_ARRIVAL)).toBeInTheDocument();
      expect(
        screen.getByText(formatToLocale(visitContainerMoveLeg.arrivalDate!))
      ).toBeInTheDocument();
      expect(screen.getByText(TxDateLabels.LABEL_MOVE)).toBeInTheDocument();
      expect(screen.getByText(formatToLocale(visitContainerMoveLeg.moveDate!))).toBeInTheDocument();
    });

    it('should not display schedule container visit section by default', async () => {
      await renderMoveLeg(createMoveLeg());

      expect(screen.queryByText('Scheduled Container Visit')).not.toBeInTheDocument();
    });

    it('should display scheduled container visit section for visit container move leg', async () => {
      const visitContainerMoveLeg = createMoveLeg({
        scheduledDate: todayDate,
        moveLegType: 'VISIT_CONTAINER',
        containerVisitDate: futureDate,
        eta: '9:00 AM - 5:00 PM'
      });

      await renderMoveLeg(visitContainerMoveLeg);

      expect(screen.getByText(Tx.Title.VISIT_CONTAINER)).toBeInTheDocument();
      expect(screen.getByText(Tx.Scheduling.DateLabels.LABEL_VISIT)).toBeInTheDocument();
      expect(
        screen.getByText(formatToLocale(visitContainerMoveLeg.scheduledDate!))
      ).toBeInTheDocument();
      expect(screen.getByText(Tx.Scheduling.DateLabels.LABEL_VISIT_WINDOW)).toBeInTheDocument();
      expect(screen.getByText(formatETA(visitContainerMoveLeg.eta!))).toBeInTheDocument();
      expect(moveLegViews.containerVisitEditButton()).toBeInTheDocument();
      expect(moveLegViews.containerVisitCancelButton()).toBeInTheDocument();
    });

    it('should display edit button if poetEnabled and orderModEnabled', async () => {
      const visitContainerMoveLeg = createMoveLeg({
        scheduledDate: todayDate,
        moveLegType: 'VISIT_CONTAINER',
        containerVisitDate: futureDate,
        eta: '9:00 AM - 5:00 PM'
      });
      mockedUseFeatureFlags.useFeatureFlags.mockImplementation(() =>
        createUseFeatureFlagResult({ isPoetEnabled: () => true, isOrderModEnabled: () => true })
      );

      await renderMoveLeg(visitContainerMoveLeg);

      expect(moveLegViews.containerVisitEditButton()).toBeInTheDocument();
    });

    it('should not display edit button if poetEnabled and orderMod is disabled', async () => {
      const visitContainerMoveLeg = createMoveLeg({
        scheduledDate: todayDate,
        moveLegType: 'VISIT_CONTAINER',
        containerVisitDate: futureDate,
        eta: '9:00 AM - 5:00 PM'
      });
      mockedUseFeatureFlags.useFeatureFlags.mockImplementation(() =>
        createUseFeatureFlagResult({ isPoetEnabled: () => true, isOrderModEnabled: () => false })
      );

      await renderMoveLeg(visitContainerMoveLeg);

      expect(moveLegViews.containerVisitEditButton()).not.toBeInTheDocument();
    });

    it('should display no warehouse hours found if no eta is found on the move leg', async () => {
      const visitContainerMoveLeg = createMoveLeg({
        scheduledDate: todayDate,
        moveLegType: 'VISIT_CONTAINER',
        containerVisitDate: futureDate,
        eta: undefined
      });

      await renderMoveLeg(visitContainerMoveLeg);

      expect(screen.getByText(Tx.Title.VISIT_CONTAINER)).toBeInTheDocument();
      expect(screen.getByText(Tx.Scheduling.DateLabels.LABEL_VISIT)).toBeInTheDocument();
      expect(
        screen.getByText(formatToLocale(visitContainerMoveLeg.scheduledDate!))
      ).toBeInTheDocument();
      expect(screen.getByText(Tx.Scheduling.DateLabels.LABEL_VISIT_WINDOW)).toBeInTheDocument();
      expect(screen.getByText(Tx.Scheduling.NO_WAREHOUSE_HOURS_FOUND)).toBeInTheDocument();
      expect(moveLegViews.containerVisitEditButton()).toBeInTheDocument();
      expect(moveLegViews.containerVisitCancelButton()).toBeInTheDocument();
    });

    it('should allow for rescheduling when edit is clicked for a visit container move leg', async () => {
      const visitContainerMoveLeg = createMoveLeg({
        scheduledDate: todayDate,
        moveLegType: 'VISIT_CONTAINER',
        containerVisitDate: futureDate,
        eta: '9:00 AM - 5:00 PM'
      });

      await renderMoveLeg(visitContainerMoveLeg);

      await actions.clickContainerVisitEditButton();

      expect(screen.queryByText(Tx.Title.VISIT_CONTAINER)).not.toBeInTheDocument();
      expect(moveLegViews.saveButton()).toBeInTheDocument();
      expect(
        screen.getByRole('textbox', {
          name: getDatePickerLabelKey(visitContainerMoveLeg)
        })
      ).toBeInTheDocument();
    });

    it('should display Chat or SMS for special assistance component if the move leg is moving internationally', async () => {
      const internationalMoveLeg = createMoveLeg({
        isCrossBorder: true
      });

      await renderMoveLeg(internationalMoveLeg);

      expect(screen.getByText(Tx.Title.CHAT_BUTTON)).toBeInTheDocument();
      expect(screen.getByText(smsButtonText)).toBeInTheDocument();
    });

    it('should not display call to schedule component if the move leg is moving internationally', async () => {
      const internationalMoveLeg = createMoveLeg({
        isCrossBorder: true,
        moveLegType: MoveLegTypeEnum.WAREHOUSE_TO_WAREHOUSE,
        scheduledDate: undefined
      });

      await renderMoveLeg(internationalMoveLeg);

      expect(screen.queryByText(Tx.CALL_TO_SCHEDULE_DESCRIPTION)).not.toBeInTheDocument();
    });

    it('should not display edit button and schedule button if the move leg is moving internationally', async () => {
      const internationalMoveLeg = createMoveLeg({
        isCrossBorder: true,
        scheduledDate: undefined
      });

      await renderMoveLeg(internationalMoveLeg);

      expect(moveLegViews.editButton()).not.toBeInTheDocument();
      expect(moveLegViews.scheduleButton()).not.toBeInTheDocument();
    });

    it('should display Chat or SMS for special assistance component if the move leg for Hawaii', async () => {
      const hawaiiMoveLeg = createMoveLeg({
        isHawaii: true
      });

      await renderMoveLeg(hawaiiMoveLeg);

      expect(screen.getByText(Tx.Title.CHAT_BUTTON)).toBeInTheDocument();
      expect(screen.getByText(smsButtonText)).toBeInTheDocument();
    });

    it('should display Chat or SMS component if the move leg for Hawaii', async () => {
      const hawaiiMoveLeg = createMoveLeg({
        isHawaii: true,
        moveLegType: MoveLegTypeEnum.WAREHOUSE_TO_WAREHOUSE,
        scheduledDate: undefined
      });

      await renderMoveLeg(hawaiiMoveLeg);

      expect(screen.getByText(Tx.Title.CHAT_BUTTON)).toBeInTheDocument();
      expect(screen.getByText(smsButtonText)).toBeInTheDocument();
    });

    it('should not display edit button and schedule button if the move leg for Hawaii', async () => {
      const hawaiiMoveLeg = createMoveLeg({
        isHawaii: true,
        scheduledDate: undefined
      });

      await renderMoveLeg(hawaiiMoveLeg);

      expect(moveLegViews.editButton()).not.toBeInTheDocument();
      expect(moveLegViews.scheduleButton()).not.toBeInTheDocument();
    });

    it('should display the Chat and SMS component if the move leg is City Service', async () => {
      const cityServiceMoveLeg = createMoveLeg({
        isCityService: true,
        moveLegType: MoveLegTypeEnum.WAREHOUSE_TO_WAREHOUSE,
        scheduledDate: undefined
      });

      await renderMoveLeg(cityServiceMoveLeg);

      expect(screen.getByText(Tx.Title.CHAT_BUTTON)).toBeInTheDocument();
      expect(screen.getByText(smsButtonText)).toBeInTheDocument();
    });

    it('should not display edit button and schedule button if the move involves City Service', async () => {
      const cityServiceMoveLeg = createMoveLeg({
        isCityService: true,
        scheduledDate: undefined
      });

      await renderMoveLeg(cityServiceMoveLeg);

      expect(moveLegViews.editButton()).not.toBeInTheDocument();
      expect(moveLegViews.scheduleButton()).not.toBeInTheDocument();
    });

    it('should not display Chat and SMS component if the move leg is transit', async () => {
      const inTransitHawaiiMoveLeg = createMoveLeg({
        isHawaii: true,
        isTransitLeg: true
      });

      await renderMoveLeg(inTransitHawaiiMoveLeg);

      expect(screen.queryByText(Tx.Title.CHAT_BUTTON)).not.toBeInTheDocument();
      expect(screen.queryByText(smsButtonText)).not.toBeInTheDocument();
    });

    it('when SELF_FINAL_PICKUP move leg is scheduled, then pickup window is displayed', async () => {
      const selfFinalPickupMoveLeg = createMoveLeg({
        moveLegType: 'SELF_FINAL_PICKUP',
        scheduledDate: futureDate,
        eta: '07:30 AM - 03:00 PM'
      });

      await renderMoveLeg(selfFinalPickupMoveLeg);

      expect(screen.getByText(Tx.Scheduling.DateLabels.LABEL_PICKUP_WINDOW)).toBeInTheDocument();
      expect(screen.getByText('7:30AM to 3PM')).toBeInTheDocument();
    });

    it('when SELF_FINAL_PICKUP move leg is unscheduled, then pickup window is absent', async () => {
      const selfFinalPickupMoveLeg = createMoveLeg({
        moveLegType: 'SELF_FINAL_PICKUP',
        scheduledDate: undefined
      });

      await renderMoveLeg(selfFinalPickupMoveLeg);

      expect(
        screen.queryByText(Tx.Scheduling.DateLabels.LABEL_PICKUP_WINDOW)
      ).not.toBeInTheDocument();
    });
  });

  describe('Review Initial Container Placement', async () => {
    const initialDeliveryMoveLeg = createMoveLeg({
      moveLegType: 'INITIAL_DELIVERY'
    });
    const orderId = 'orderId1';

    const renderWithInitialDelivery = async (
      moveLeg: MoveLeg,
      initialDeliveryPlacementIsReviewed: boolean
    ) => {
      const container = createContainer({ moveLegs: [moveLeg] });
      const orderNeedingReview = createOrder({
        orderId,
        initialDeliveryPlacementIsReviewed,
        containers: [container]
      });
      await renderMoveLeg(moveLeg, container, orderNeedingReview);
    };

    it('should display alert for initial delivery when customer has not reviewed container placement for order', async () => {
      await renderWithInitialDelivery(initialDeliveryMoveLeg, false);

      expect(screen.getByText(Tx.InitialDeliveryReviewAlert.UPPER_TITLE)).toBeInTheDocument();
      expect(screen.getByText(Tx.InitialDeliveryReviewAlert.TITLE)).toBeInTheDocument();
      expect(screen.getByText(Tx.InitialDeliveryReviewAlert.SUBTITLE)).toBeInTheDocument();
      expect(
        screen.getByRole('button', { name: TranslationKeys.CommonComponents.REVIEW_BUTTON })
      ).toBeInTheDocument();
      expect(
        screen.getByRole('button', { name: TranslationKeys.CommonComponents.ACCEPT_BUTTON })
      ).toBeInTheDocument();
    });

    it('should NOT display alert for initial delivery when customer has reviewed container placement for order', async () => {
      await renderWithInitialDelivery(initialDeliveryMoveLeg, true);

      expect(screen.queryByText(Tx.InitialDeliveryReviewAlert.TITLE)).not.toBeInTheDocument();
      expect(screen.queryByText(Tx.InitialDeliveryReviewAlert.SUBTITLE)).not.toBeInTheDocument();
    });

    it('should NOT display alert for non initial delivery', async () => {
      const finalPickupMoveLeg = createMoveLeg({
        moveLegType: 'FINAL_PICKUP'
      });
      await renderWithInitialDelivery(finalPickupMoveLeg, false);

      expect(screen.queryByText(Tx.InitialDeliveryReviewAlert.TITLE)).not.toBeInTheDocument();
      expect(screen.queryByText(Tx.InitialDeliveryReviewAlert.SUBTITLE)).not.toBeInTheDocument();
    });

    it('accepts container placement when accept button is clicked', async () => {
      await renderWithInitialDelivery(initialDeliveryMoveLeg, false);

      await actions.clickAcceptDeliveryPlacement();

      expect(mockAcceptInitialDeliveryPlacement).toBeCalledWith(orderId);
    });

    it('displays the container placement modal when review is clicked', async () => {
      await renderWithInitialDelivery(initialDeliveryMoveLeg, false);
      await actions.clickReviewDeliveryPlacement();

      expect(moveLegViews.containerPlacementFinishButton()).toBeInTheDocument();
    });
  });

  describe('should display move leg icon: ', () => {
    it('empty circle when move leg is scheduled in the past', async () => {
      await renderMoveLeg(createMoveLeg({ scheduledDate: pastDate }));

      expect(screen.getByTestId('timeline-icon-empty')).toBeInTheDocument();
    });

    it('filled circle when move leg is up next and has been scheduled', async () => {
      await renderMoveLeg(createMoveLeg({ scheduledDate: futureDate, isUpNext: true }));

      expect(screen.getByTestId('timeline-icon-filled')).toBeInTheDocument();
    });

    it('empty circle when move leg is not up next and has been scheduled', async () => {
      await renderMoveLeg(createMoveLeg({ scheduledDate: futureDate, isUpNext: false }));

      expect(screen.getByTestId('timeline-icon-empty')).toBeInTheDocument();
    });

    it('faded empty circle when move leg is unscheduled', async () => {
      await renderMoveLeg(createMoveLeg({ scheduledDate: undefined }));

      expect(screen.getByTestId('timeline-icon-faded')).toBeInTheDocument();
    });
  });

  describe('when displaying classic Scheduling (redesignedScheduling disabled) & order mods are enabled', () => {
    beforeEach(() => {
      mockedUseFeatureFlags.useFeatureFlags.mockImplementation(() =>
        createUseFeatureFlagResult({
          isPoetEnabled: () => true,
          isRedesignedSchedulingEnabled: () => false,
          isRedesignedSchedulingPickupEnabled: () => false,
          isOrderModEnabled: () => true
        })
      );
    });

    test.each([
      ['PICKUP', Tx.Scheduling.DateLabels.LABEL_DEFAULT],
      ['FINAL_PICKUP', Tx.Scheduling.DateLabels.LABEL_DEFAULT],
      ['INITIAL_DELIVERY', Tx.Scheduling.DateLabels.LABEL_DEFAULT]
    ])(
      'when an unscheduled %s move leg is clicked, the view should expand with the label %s ',
      async (moveLegName, datePickerLabel) => {
        const moveLeg = createMoveLeg({
          moveLegType: moveLegName as MoveLegType,
          scheduledDate: undefined
        });
        await renderMoveLeg(moveLeg, createContainer({ moveLegs: [moveLeg] }));

        expect(moveLegViews.scheduleButton()!).toBeInTheDocument();
        fireEvent.click(moveLegViews.scheduleButton()!);
        await runPendingPromises();

        expect(screen.getByText(datePickerLabel)).toBeInTheDocument();
        expect(screen.queryByTestId('manage-pickup-panel')).not.toBeInTheDocument();
      }
    );

    test.each([
      ['PICKUP', Tx.Scheduling.DateLabels.LABEL_DEFAULT],
      ['FINAL_PICKUP', Tx.Scheduling.DateLabels.LABEL_DEFAULT],
      ['INITIAL_DELIVERY', Tx.Scheduling.DateLabels.LABEL_DEFAULT]
    ])(
      'when a scheduled %s move leg is clicked, the view should expand with the label %s',
      async (moveLegName, datePickerLabel) => {
        const moveLeg = createMoveLeg({
          moveLegType: moveLegName as MoveLegType
        });
        await renderMoveLeg(moveLeg, createContainer({ moveLegs: [moveLeg] }));

        fireEvent.click(moveLegViews.editButton()!);
        await runPendingPromises();

        expect(screen.getByText(datePickerLabel)).toBeInTheDocument();
        expect(screen.queryByTestId('manage-pickup-panel')).not.toBeInTheDocument();
      }
    );
  });

  describe('when redesignedSchedulingPickup is enabled, but redesignedScheduling is disabled', () => {
    beforeEach(() => {
      mockedUseFeatureFlags.useFeatureFlags.mockImplementation(() =>
        createUseFeatureFlagResult({
          isPoetEnabled: () => true,
          isRedesignedSchedulingEnabled: () => false,
          isOrderModEnabled: () => true,
          isRedesignedSchedulingPickupEnabled: () => true,
          isReady: true
        })
      );
    });

    test.each([
      ['PICKUP', 'pickup'],
      ['FINAL_PICKUP', 'finalPickup'],
      ['INITIAL_DELIVERY', 'initialDelivery']
    ])(
      'when an unscheduled %s move leg is clicked, the drawer should be open with the title %s ',
      async (moveLegName, moveLegContext) => {
        const moveLeg = createMoveLeg({
          moveLegType: moveLegName as MoveLegType,
          scheduledDate: undefined
        });

        await renderMoveLeg(moveLeg, createContainer({ moveLegs: [moveLeg] }));

        const schedulePickupButton = screen.getByRole('button', {
          name: `homePage.moveLegs.scheduleButton[${moveLegContext}]`
        });

        fireEvent.click(schedulePickupButton);
        await runPendingPromises();

        expect(
          screen.getByText(
            `homePage.moveLegs.scheduling.pickupContainer.title.step1[${moveLegContext}]`
          )
        ).toBeInTheDocument();
      }
    );

    test.each([
      ['PICKUP', 'pickup'],
      ['FINAL_PICKUP', 'finalPickup'],
      ['INITIAL_DELIVERY', 'initialDelivery']
    ])(
      'when a scheduled %s move leg is clicked, the drawer should be open with the title %s ',
      async (moveLegName, panelTitle) => {
        const moveLeg = createMoveLeg({
          moveLegType: moveLegName as MoveLegType
        });

        await renderMoveLeg(moveLeg, createContainer({ moveLegs: [moveLeg] }));
        expect(moveLegViews.editButton()).toBeInTheDocument();
        fireEvent.click(moveLegViews.editButton()!);
        await runPendingPromises();

        expect(screen.getByTestId('manage-pickup-panel')).toBeInTheDocument();
        expect(
          screen.getByText(
            `homePage.moveLegs.scheduling.pickupContainer.title.step1[${panelTitle}]`
          )
        ).toBeInTheDocument();
      }
    );

    it('should not open the schedule visit container panel, when schedule is clicked on a container visit move leg', async () => {
      const moveLeg = createMoveLeg({
        moveLegType: 'VISIT_CONTAINER'
      });

      await renderMoveLeg(moveLeg, createContainer({ moveLegs: [moveLeg] }));

      expect(moveLegViews.scheduleButton()).toBeInTheDocument();
      fireEvent.click(moveLegViews.scheduleButton()!);
      await runPendingPromises();

      expect(screen.queryByTestId('manage-visit-panel')).toBeNull();
    });
  });

  describe('when redesignedScheduling is enabled & redesignedSchedulingPickup disabled', () => {
    beforeEach(() => {
      mockedUseFeatureFlags.useFeatureFlags.mockImplementation(() =>
        createUseFeatureFlagResult({
          isRedesignedSchedulingPickupEnabled: () => false,
          isRedesignedSchedulingEnabled: () => true,
          isPoetEnabled: () => true,
          isOrderModEnabled: () => true
        })
      );
    });

    test.each([
      ['PICKUP', Tx.Scheduling.DateLabels.LABEL_DEFAULT],
      ['FINAL_PICKUP', Tx.Scheduling.DateLabels.LABEL_DEFAULT],
      ['INITIAL_DELIVERY', Tx.Scheduling.DateLabels.LABEL_DEFAULT]
    ])(
      'when an unscheduled %s move leg is clicked, the view should expand with the label %s ',
      async (moveLegName, datePickerLabel) => {
        const moveLeg = createMoveLeg({
          moveLegType: moveLegName as MoveLegType,
          scheduledDate: undefined
        });
        await renderMoveLeg(moveLeg, createContainer({ moveLegs: [moveLeg] }));

        expect(moveLegViews.scheduleButton()!).toBeInTheDocument();
        fireEvent.click(moveLegViews.scheduleButton()!);
        await runPendingPromises();

        expect(screen.getByText(datePickerLabel)).toBeInTheDocument();
        expect(screen.queryByTestId('manage-pickup-panel')).not.toBeInTheDocument();
      }
    );

    test.each([
      ['PICKUP', Tx.Scheduling.DateLabels.LABEL_DEFAULT],
      ['FINAL_PICKUP', Tx.Scheduling.DateLabels.LABEL_DEFAULT],
      ['INITIAL_DELIVERY', Tx.Scheduling.DateLabels.LABEL_DEFAULT]
    ])(
      'when a scheduled %s move leg is clicked, the view should expand with the label %s',
      async (moveLegName, datePickerLabel) => {
        const moveLeg = createMoveLeg({
          moveLegType: moveLegName as MoveLegType
        });
        await renderMoveLeg(moveLeg, createContainer({ moveLegs: [moveLeg] }));

        fireEvent.click(moveLegViews.editButton()!);
        await runPendingPromises();

        expect(screen.getByText(datePickerLabel)).toBeInTheDocument();
        expect(screen.queryByTestId('manage-pickup-panel')).not.toBeInTheDocument();
      }
    );
    it('should open the schedule visit container panel, when schedule is clicked on a container visit move leg', async () => {
      const moveLeg = createMoveLeg({
        moveLegType: 'VISIT_CONTAINER'
      });
      await renderMoveLeg(moveLeg, createContainer({ moveLegs: [moveLeg] }));
      fireEvent.click(moveLegViews.scheduleButton()!);
      await runPendingPromises();

      expect(screen.queryByTestId('manage-visit-panel')).toBeInTheDocument();
    });

    it('should open the schedule visit container panel, when edit is clicked on a container visit move leg', async () => {
      let date = addDays(new Date(), 2);

      const moveLeg = createMoveLeg({
        moveLegType: 'VISIT_CONTAINER',
        containerVisitDate: date,
        scheduledDate: date,
        isSchedulableOnline: true
      });

      await renderMoveLeg(moveLeg, createContainer({ moveLegs: [moveLeg] }));
      fireEvent.click(moveLegViews.containerVisitEditButton()!);
      await runPendingPromises();

      expect(screen.queryByTestId('manage-visit-panel')).toBeInTheDocument();
    });

    it('should show the classic design, when cancel is clicked on a container visit move leg', async () => {
      let date = addDays(new Date(), 2);

      const moveLeg = createMoveLeg({
        moveLegType: 'VISIT_CONTAINER',
        containerVisitDate: date,
        scheduledDate: date,
        isSchedulableOnline: true
      });
      await renderMoveLeg(moveLeg, createContainer({ moveLegs: [moveLeg] }));
      fireEvent.click(moveLegViews.cancelButton()!);
      await runPendingPromises();

      expect(screen.queryByTestId('manage-visit-panel')).not.toBeInTheDocument();
    });

    it('should open the dropoff container panel, when edit is clicked on a container dropoff move leg', async () => {
      const moveLeg = createMoveLeg({
        moveLegType: 'REDELIVERY'
      });

      renderMoveLeg(moveLeg, createContainer({ moveLegs: [moveLeg] }));
      const editButton = screen.queryByRole('button', {
        name: TranslationKeys.CommonComponents.EDIT_BUTTON
      });
      fireEvent.click(editButton!);
      await runPendingPromises();

      expect(screen.queryByTestId('manage-dropoff-panel')).toBeInTheDocument();
    });
  });
});
