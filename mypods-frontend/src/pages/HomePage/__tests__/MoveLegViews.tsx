import { fireEvent, screen } from '@testing-library/react';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { UserEvent } from '@testing-library/user-event';
import { act } from 'react';

export const moveLegViews = {
  scheduleButton: () => screen.queryByTestId('schedule-move-leg-button'),
  editButton: () => screen.queryByTestId('move-leg-edit-button'),
  containerVisitEditButton: () => screen.queryByTestId('container-visit-edit-button'),
  containerVisitCancelButton: () => screen.queryByTestId('cancel-visit-button'),
  // Don't test that the input displays value, test that saving occurs or not
  dateInput: () => screen.queryByTestId('date-input'),
  cancelButton: () =>
    screen.queryByRole('button', { name: TranslationKeys.CommonComponents.CANCEL_BUTTON }),
  saveButton: () =>
    screen.getByRole('button', { name: TranslationKeys.CommonComponents.SAVE_BUTTON }),
  nextButton: () =>
    screen.getByRole('button', { name: TranslationKeys.CommonComponents.NEXT_BUTTON }),
  backButton: () =>
    screen.getByRole('button', { name: TranslationKeys.CommonComponents.BACK_BUTTON }),
  confirmButton: () =>
    screen.getByRole('button', {
      name: TranslationKeys.CommonComponents.CONFIRM_BUTTON
    }),
  acceptDeliveryPlacementButton: () =>
    screen.queryByRole('button', { name: TranslationKeys.CommonComponents.ACCEPT_BUTTON }),
  reviewDeliveryPlacementButton: () =>
    screen.getByRole('button', { name: TranslationKeys.CommonComponents.REVIEW_BUTTON }),
  postalCodeInput: () =>
    screen.getByLabelText(TranslationKeys.AccountPage.AddressInfo.InputFields.POSTAL_CODE),
  containerPlacementButton: () =>
    screen.queryByRole('button', {
      name: TranslationKeys.HomePage.MoveLegs.Scheduling.CONTAINER_PLACEMENT_BUTTON
    }),
  isPavedSurfaceYesButton: () =>
    screen.getByRole('button', {
      name: TranslationKeys.CommonComponents.YES_BUTTON
    }),
  containerPlacementFinishButton: () =>
    screen.getByRole('button', {
      name: TranslationKeys.HomePage.ContainerPlacement.ReviewScreen.FINISH_BUTTON
    })
};

export const moveLegActions = (user: UserEvent) => ({
  clickSchedule: () => act(() => user.click(moveLegViews.scheduleButton()!)),
  clickEditButton: () => act(() => user.click(moveLegViews.editButton()!)),
  clickContainerVisitEditButton: () =>
    act(() => user.click(moveLegViews.containerVisitEditButton()!)),
  clickContainerVisitCancelButton: () =>
    act(() => user.click(moveLegViews.containerVisitCancelButton()!)),
  clickCancel: () => act(() => user.click(moveLegViews.cancelButton()!)),
  clickSave: () => act(() => user.click(moveLegViews.saveButton())),
  clickConfirm: () => act(() => user.click(moveLegViews.confirmButton())),
  clickNext: () => act(() => user.click(moveLegViews.nextButton())),
  clickBack: () => act(() => user.click(moveLegViews.backButton())),
  clickAcceptDeliveryPlacement: () =>
    act(() => user.click(moveLegViews.acceptDeliveryPlacementButton()!)),
  clickReviewDeliveryPlacement: () =>
    act(() => user.click(moveLegViews.reviewDeliveryPlacementButton()!)),
  enterDate: (date: string) =>
    act(() => fireEvent.change(moveLegViews.dateInput()!, { target: { value: date } })),
  blurDateInput: () =>
    act(async () => {
      fireEvent.blur(moveLegViews.dateInput()!);
    }),
  enterPostalCode: (postalCode: string) =>
    act(() => fireEvent.change(moveLegViews.postalCodeInput()!, { target: { value: postalCode } })),
  blurPostalCodeInput: () =>
    act(async () => {
      fireEvent.blur(moveLegViews.postalCodeInput());
    })
});
