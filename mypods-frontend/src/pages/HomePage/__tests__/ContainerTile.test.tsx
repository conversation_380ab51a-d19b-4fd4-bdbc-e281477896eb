import { render, screen, waitFor, within, act } from '@testing-library/react';
import {
  createCloneQuoteFromOrderResponse,
  createContainer,
  createMoveLeg,
  createOrder,
  createRefreshSessionClaims,
  createUseFeatureFlagResult
} from '../../../testUtils/MyPodsFactories';
import userEvent from '@testing-library/user-event';
import React, { Suspense } from 'react';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { ContainerTile } from '../container/ContainerTile';
import { ContainerProvider } from '../../../context/ContainerContext';
import { Container, MoveLeg, Order } from '../../../domain/OrderEntities';
import { addDays, subDays } from 'date-fns';
import { QueryClientProvider } from '@tanstack/react-query';
import { testQueryClient } from '../../../testUtils/RenderHelpers';
import { SingleOrderProvider } from '../../../context/SingleOrderContext';
import {
  mockCloneQuoteFromOrder,
  mockedUseFeatureFlags,
  mockRefreshSession
} from '../../../../setupTests';

const Tx = TranslationKeys.HomePage.ContainerTile;
const actions = {
  clickShowHistory: async () => {
    await waitFor(() => userEvent.click(screen.getByRole('button', { name: Tx.SHOW_HISTORY })));
  },
  clickHideHistory: async () => {
    await waitFor(() => userEvent.click(screen.getByRole('button', { name: Tx.HIDE_HISTORY })));
  }
};
const views = {
  completedSection: () => screen.getByTestId('accordion-content-history'),
  currentSection: () => screen.getByTestId('accordion-content-current'),
  moveLegSection: (moveLegId: string) => screen.getByTestId(`move-leg-${moveLegId}`),
  header: () => screen.getByTestId('container-tile-header')
};

describe('ContainerTile', () => {
  const today = new Date();
  const tomorrow = addDays(today, 1);
  const fiveDaysInFuture = addDays(today, 5);
  const orderId = '123456789';
  const baseMoveLeg = createMoveLeg({ scheduledDate: tomorrow });
  const baseOrder = createOrder({
    orderId: orderId,
    orderDate: new Date('2024-04-25T09:53:00.3731149'),
    containers: [createContainer({ moveLegs: [baseMoveLeg] })]
  });
  const baseContainer = baseOrder.containers[0];

  async function renderContainerTile(
    renderOrder: Order = baseOrder,
    renderContainer: Container = renderOrder.containers[0]
  ) {
    render(
      <Suspense>
        <QueryClientProvider client={testQueryClient()}>
          <SingleOrderProvider state={{ order: renderOrder }}>
            <ContainerProvider state={{ container: renderContainer, order: renderOrder }}>
              <ContainerTile />
            </ContainerProvider>
          </SingleOrderProvider>
        </QueryClientProvider>
      </Suspense>
    );
  }

  async function renderContainerTileAndShowHistory(
    renderOrder: Order = baseOrder,
    renderContainer: Container = renderOrder.containers[0]
  ) {
    await renderContainerTile(renderOrder, renderContainer);
    await actions.clickShowHistory();
  }

  beforeEach(() => {
    mockedUseFeatureFlags.useFeatureFlags.mockImplementation(() =>
      createUseFeatureFlagResult({ isOrderModEnabled: () => true })
    );
    mockCloneQuoteFromOrder.mockResolvedValue(createCloneQuoteFromOrderResponse());
  });

  it('render order details section', async () => {
    await renderContainerTileAndShowHistory();

    expect(screen.getByText(Tx.OrderDetails.STATUS)).toBeInTheDocument();
    expect(screen.getByText('April 25, 2024')).toBeInTheDocument();
    expect(screen.getByText(orderId)).toBeInTheDocument();
  });

  it('should toggle the visibility of the move history when top/history accordion header is clicked', async () => {
    const completedLeg1 = createMoveLeg({
      moveLegId: '1',
      scheduledDate: subDays(new Date(), 7)
    });
    const currentMoveLeg = createMoveLeg({
      moveLegId: '2',
      moveLegType: 'PICKUP',
      scheduledDate: addDays(new Date(), 7),
      isUpNext: true
    });
    const unscheduledLeg = createMoveLeg({
      moveLegId: '3',
      moveLegType: 'REDELIVERY',
      scheduledDate: undefined,
      isUpNext: false
    });

    const orderWithLegs = {
      ...baseOrder,
      containers: [
        createContainer({
          moveLegs: [completedLeg1, currentMoveLeg, unscheduledLeg]
        })
      ]
    };

    await renderContainerTile(orderWithLegs, orderWithLegs.containers[0]);

    // completed section behavior
    await actions.clickShowHistory();
    expect(
      within(views.completedSection()).queryByTestId(`move-leg-${completedLeg1.moveLegId}`)
    ).toBeInTheDocument();
    await actions.clickHideHistory();

    // current section
    expect(
      within(views.currentSection()).queryByTestId(`move-leg-${currentMoveLeg.moveLegId}`)
    ).toBeInTheDocument();
  });

  it('should display only the up next & next actionable(unscheduled) move leg, when both accordions are hidden', async () => {
    const scheduledLeg1 = createMoveLeg({
      moveLegId: '1',
      scheduledDate: subDays(new Date(), 7)
    });
    const scheduledLeg2 = createMoveLeg({
      moveLegId: '2',
      moveLegType: 'PICKUP',
      scheduledDate: fiveDaysInFuture,
      isUpNext: true
    });

    const unscheduledLeg1 = createMoveLeg({
      moveLegId: '3',
      moveLegType: 'MOVE',
      scheduledDate: undefined,
      isUpNext: false
    });
    const unscheduledLeg2 = createMoveLeg({
      moveLegId: '4',
      moveLegType: 'REDELIVERY',
      scheduledDate: undefined,
      isUpNext: false
    });
    const orderWithUnscheduledLegs = {
      ...baseOrder,
      containers: [
        createContainer({
          moveLegs: [scheduledLeg1, scheduledLeg2, unscheduledLeg1, unscheduledLeg2]
        })
      ]
    };

    await renderContainerTile(orderWithUnscheduledLegs, orderWithUnscheduledLegs.containers[0]);

    // confirm accordions are folded, to avoid false-positives
    expect(screen.queryByTestId('accordion-content-history')).toBeNull();
    expect(screen.queryByTestId('accordion-content-upcoming')).toBeNull();

    expect(screen.queryByTestId(`move-leg-${scheduledLeg1.moveLegId}`)).toBeNull();
    expect(screen.getByTestId(`move-leg-${scheduledLeg2.moveLegId}`)).toBeVisible();

    expect(screen.getByTestId(`move-leg-${unscheduledLeg1.moveLegId}`)).toBeVisible();
    expect(screen.queryByTestId(`move-leg-${unscheduledLeg2.moveLegId}`)).toBeNull();
  });

  it('given a container does not exist, displays not yet assigned', async () => {
    await renderContainerTile(baseOrder, { ...baseContainer, containerId: '' });

    expect(views.header()).toHaveTextContent(
      TranslationKeys.HomePage.ContainerTile.ContainerHeader.NO_CONTAINER_ID
    );
  });

  it('always shows last remaining leg in current section', async () => {
    const completedLeg1 = createMoveLeg({
      moveLegId: '1',
      scheduledDate: subDays(new Date(), 10)
    });
    const completedLeg2 = createMoveLeg({
      moveLegId: '2',
      moveLegType: 'PICKUP',
      scheduledDate: subDays(new Date(), 10),
      isUpNext: false
    });
    const scheduledMoveLeg = createMoveLeg({
      moveLegId: '3',
      moveLegType: 'FINAL_PICKUP',
      scheduledDate: addDays(new Date(), 3),
      isUpNext: true
    });

    const orderWithAllCompletedLegs = {
      ...baseOrder,
      containers: [
        createContainer({
          moveLegs: [completedLeg1, completedLeg2, scheduledMoveLeg]
        })
      ]
    };

    await renderContainerTileAndShowHistory(
      orderWithAllCompletedLegs,
      orderWithAllCompletedLegs.containers[0]
    );

    // completed section behavior
    expect(
      within(views.completedSection()).queryByTestId(`move-leg-${completedLeg1.moveLegId}`)
    ).toBeInTheDocument();
    expect(
      within(views.completedSection()).queryByTestId(`move-leg-${completedLeg2.moveLegId}`)
    ).toBeInTheDocument();

    // current section
    expect(
      within(views.currentSection()).queryByTestId(`move-leg-${scheduledMoveLeg.moveLegId}`)
    ).toBeInTheDocument();
  });

  it('current section displays all scheduled legs plus the next unscheduled move leg', async () => {
    const completedLeg1 = createMoveLeg({
      moveLegId: '1',
      scheduledDate: subDays(new Date(), 10)
    });
    const scheduledMoveLeg1 = createMoveLeg({
      moveLegId: '2',
      moveLegType: 'PICKUP',
      scheduledDate: addDays(new Date(), 3),
      isUpNext: true
    });
    const scheduledMoveLeg2 = createMoveLeg({
      moveLegId: '3',
      moveLegType: 'REDELIVERY',
      scheduledDate: addDays(new Date(), 7),
      isUpNext: false
    });
    const unscheduledMoveLeg = createMoveLeg({
      moveLegId: '4',
      moveLegType: 'FINAL_PICKUP',
      scheduledDate: undefined,
      isUpNext: false
    });

    const orderWithAllCompletedLegs = {
      ...baseOrder,
      containers: [
        createContainer({
          moveLegs: [completedLeg1, scheduledMoveLeg1, scheduledMoveLeg2, unscheduledMoveLeg]
        })
      ]
    };

    await renderContainerTileAndShowHistory(
      orderWithAllCompletedLegs,
      orderWithAllCompletedLegs.containers[0]
    );

    // completed section behavior
    expect(
      within(views.completedSection()).queryByTestId(`move-leg-${completedLeg1.moveLegId}`)
    ).toBeInTheDocument();

    // current section
    expect(
      within(views.currentSection()).queryByTestId(`move-leg-${scheduledMoveLeg1.moveLegId}`)
    ).toBeInTheDocument();
    expect(
      within(views.currentSection()).queryByTestId(`move-leg-${scheduledMoveLeg2.moveLegId}`)
    ).toBeInTheDocument();
    expect(
      within(views.currentSection()).queryByTestId(`move-leg-${unscheduledMoveLeg.moveLegId}`)
    ).toBeInTheDocument();
  });

  it('given an unscheduled visit container move leg in the past and redelivery in the future, should render storage move leg at the top of current', async () => {
    // it has a scheduled date, but can have another container visit scheduled
    // usually always true when in the past
    const storageLeg = createMoveLeg({
      moveLegId: '1',
      moveLegType: 'VISIT_CONTAINER',
      containerVisitDate: undefined,
      scheduledDate: subDays(new Date(), 10)
    });
    const redeliveryLeg = createMoveLeg({
      moveLegId: '2',
      moveLegType: 'REDELIVERY',
      scheduledDate: addDays(new Date(), 7)
    });

    const order = createOrder({
      ...baseOrder,
      containers: [
        createContainer({
          moveLegs: [storageLeg, redeliveryLeg]
        })
      ]
    });

    await renderContainerTileAndShowHistory(order);

    expect(
      within(views.currentSection()).queryByTestId(`move-leg-${storageLeg.moveLegId}`)
    ).toBeInTheDocument();
    expect(
      within(views.currentSection()).queryByTestId(`move-leg-${redeliveryLeg.moveLegId}`)
    ).toBeInTheDocument();
  });

  it('given a container visit move leg in the past and redelivery in the past, should render storage and redelivery in the completed legs', async () => {
    const storageLeg = createMoveLeg({
      moveLegId: '1',
      moveLegType: 'VISIT_CONTAINER',
      scheduledDate: subDays(new Date(), 10)
    });
    const redeliveryLeg = createMoveLeg({
      moveLegId: '2',
      moveLegType: 'REDELIVERY',
      scheduledDate: subDays(new Date(), 7)
    });

    const orders = {
      ...baseOrder,
      containers: [
        createContainer({
          moveLegs: [storageLeg, redeliveryLeg]
        })
      ]
    };

    await renderContainerTileAndShowHistory(orders);

    expect(
      within(views.completedSection()).queryByTestId(`move-leg-${storageLeg.moveLegId}`)
    ).toBeInTheDocument();
    expect(
      within(views.completedSection()).queryByTestId(`move-leg-${redeliveryLeg.moveLegId}`)
    ).toBeInTheDocument();
  });

  it('should close previous move leg being edited when editing a different move leg', async () => {
    const firstLeg = createMoveLeg({
      moveLegId: '1',
      moveLegType: 'INITIAL_DELIVERY',
      scheduledDate: addDays(new Date(), 2)
    });
    const secondLeg = createMoveLeg({
      moveLegId: '2',
      moveLegType: 'FINAL_PICKUP',
      scheduledDate: addDays(new Date(), 7)
    });

    const orders = {
      ...baseOrder,
      containers: [
        createContainer({
          moveLegs: [firstLeg, secondLeg]
        })
      ]
    };

    await renderContainerTile(orders);

    const editButtons = screen.getAllByRole('button', {
      name: TranslationKeys.CommonComponents.EDIT_BUTTON
    });
    expect(editButtons).toHaveLength(2);
    await act(() => userEvent.click(editButtons[0]));
    expect(
      screen.getAllByRole('button', { name: TranslationKeys.CommonComponents.EDIT_BUTTON })
    ).toHaveLength(1);
    await act(() => userEvent.click(editButtons[1]));
    expect(
      screen.getAllByRole('button', { name: TranslationKeys.CommonComponents.EDIT_BUTTON })
    ).toHaveLength(1);
  });

  it('should not show edit buttons when order mod is disabled', async () => {
    mockRefreshSession.mockResolvedValue(createRefreshSessionClaims());
    mockedUseFeatureFlags.useFeatureFlags.mockImplementation(() =>
      createUseFeatureFlagResult({ isOrderModEnabled: () => false })
    );

    const firstLeg = createMoveLeg({
      moveLegId: '1',
      moveLegType: 'INITIAL_DELIVERY',
      scheduledDate: addDays(new Date(), 2)
    });
    const secondLeg = createMoveLeg({
      moveLegId: '2',
      moveLegType: 'FINAL_PICKUP',
      scheduledDate: addDays(new Date(), 7)
    });

    const orders = {
      ...baseOrder,
      containers: [
        createContainer({
          moveLegs: [firstLeg, secondLeg]
        })
      ]
    };

    await act(async () => renderContainerTile(orders));

    const editButtons = screen.queryAllByRole('button', {
      name: TranslationKeys.CommonComponents.EDIT_BUTTON
    });

    expect(editButtons).toHaveLength(0);
  });

  describe('Service Countdown', () => {
    const createOrderWithUpNextMoveLeg = (upNextMoveLeg: MoveLeg, otherLegs: MoveLeg[] = []) => ({
      ...baseOrder,
      containers: [
        createContainer({
          moveLegs: [upNextMoveLeg, ...otherLegs],
          upNextMoveLegId: upNextMoveLeg.moveLegId
        })
      ]
    });

    describe('should display service countdown in container header and up next move leg with correct count of days', () => {
      it.each([
        {
          expected: 0,
          scheduledFor: today,
          description: 'Arriving Today'
        },
        {
          expected: 1,
          scheduledFor: tomorrow,
          description: 'Arriving Tomorrow'
        },
        {
          expected: 5,
          scheduledFor: fiveDaysInFuture,
          description: 'Arriving in 5 Days'
        }
      ])(`$description`, async ({ expected: expectedCount, scheduledFor }) => {
        const moveLeg = createMoveLeg({
          scheduledDate: scheduledFor,
          serviceCountdownType: 'DELIVERY',
          isUpNext: true
        });
        const countdownOrder = createOrderWithUpNextMoveLeg(moveLeg);

        await renderContainerTileAndShowHistory(countdownOrder, countdownOrder.containers[0]);

        const expectedText = `${Tx.SERVICE_COUNTDOWN}[${expectedCount},default]`;
        expect(views.header()).toHaveTextContent(expectedText);
        expect(views.moveLegSection(moveLeg.moveLegId)).toHaveTextContent(expectedText);
      });
    });

    it('should only display service countdown in up next move leg', async () => {
      let upNextMoveLeg = createMoveLeg({
        moveLegId: '1',
        scheduledDate: today,
        serviceCountdownType: 'DELIVERY',
        isUpNext: true
      });
      let otherMoveLeg = createMoveLeg({
        moveLegId: '2',
        scheduledDate: undefined,
        serviceCountdownType: 'PICKUP'
      });
      const order = createOrderWithUpNextMoveLeg(upNextMoveLeg, [otherMoveLeg]);

      await renderContainerTileAndShowHistory(order, order.containers[0]);

      expect(views.moveLegSection(upNextMoveLeg.moveLegId)).toHaveTextContent(
        Tx.SERVICE_COUNTDOWN_HELPER_WITHOUT_ETA
      );
      expect(views.moveLegSection(otherMoveLeg.moveLegId)).not.toHaveTextContent(
        Tx.SERVICE_COUNTDOWN_HELPER_WITHOUT_ETA
      );
    });

    it('should not display service countdown if there is no up next move leg', async () => {
      const moveLeg = createMoveLeg({ scheduledDate: tomorrow });
      const order = {
        ...baseOrder,
        containers: [
          createContainer({
            moveLegs: [moveLeg],
            upNextMoveLegId: undefined
          })
        ]
      };

      await renderContainerTileAndShowHistory(order, order.containers[0]);

      let expected = Tx.SERVICE_COUNTDOWN;
      expect(views.header()).not.toHaveTextContent(expected);
      expect(views.moveLegSection(moveLeg.moveLegId)).not.toHaveTextContent(expected);
    });

    it('should not display service countdown if there is no scheduled date', async () => {
      const unscheduledMoveLeg = createMoveLeg({ scheduledDate: undefined });
      const order = createOrderWithUpNextMoveLeg(unscheduledMoveLeg);

      await renderContainerTileAndShowHistory(order, order.containers[0]);

      let expected = Tx.SERVICE_COUNTDOWN;
      expect(views.header()).not.toHaveTextContent(expected);
      expect(views.header()).toHaveTextContent(
        `${Tx.ContainerHeader.CONTAINER_ID}${baseContainer.containerId}`
      );
      expect(views.moveLegSection(unscheduledMoveLeg.moveLegId)).not.toHaveTextContent(expected);
    });

    it('should not display service countdown if the up next move leg scheduled date is in the past', async () => {
      const moveLeg = createMoveLeg({
        scheduledDate: new Date('2000-01-01T12:00:00')
      });
      const order = createOrderWithUpNextMoveLeg(moveLeg);

      await renderContainerTileAndShowHistory(order, order.containers[0]);

      let expected = Tx.SERVICE_COUNTDOWN;
      expect(views.header()).not.toHaveTextContent(expected);
      expect(views.moveLegSection(moveLeg.moveLegId)).not.toHaveTextContent(expected);
    });

    it('should display 24 hour message in up next leg alert when eta is null', async () => {
      let moveLeg = createMoveLeg({
        scheduledDate: today,
        serviceCountdownType: 'DELIVERY',
        eta: undefined,
        isUpNext: true
      });
      const order = createOrderWithUpNextMoveLeg(moveLeg);

      await renderContainerTileAndShowHistory(order, order.containers[0]);

      expect(views.moveLegSection(moveLeg.moveLegId)).toHaveTextContent(
        Tx.SERVICE_COUNTDOWN_HELPER_WITHOUT_ETA
      );
    });

    it('should display pickup ETA message in up next move leg alert when ETA is present', async () => {
      let moveLeg = createMoveLeg({
        scheduledDate: today,
        serviceCountdownType: 'PICKUP',
        eta: '8:00am-5:00pm',
        isUpNext: true
      });
      const order = createOrderWithUpNextMoveLeg(moveLeg);

      await renderContainerTileAndShowHistory(order, order.containers[0]);

      expect(views.moveLegSection(moveLeg.moveLegId)).toHaveTextContent(
        `${Tx.SERVICE_COUNTDOWN_ETA_BETWEEN}[${moveLeg.eta}]`
      );
    });
  });
});
