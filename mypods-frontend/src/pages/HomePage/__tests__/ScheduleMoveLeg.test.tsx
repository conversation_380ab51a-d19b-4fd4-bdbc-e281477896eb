import {
  createCloneQuoteFromOrderResponse,
  createContainer,
  createMoveLeg,
  createMoveLegAddress,
  createMoveLegScheduling,
  createMoveLegUpdateResponse,
  createOrder,
  createUseFeatureFlagResult,
  getThirtyDaysOfAvailability
} from '../../../testUtils/MyPodsFactories';
import { ContainerAvailabilityResponse } from '../../../networkRequests/responseEntities/AvailabilityAPIEntities';
import {
  mockedUseFeatureFlags,
  mockGetContainerAvailability,
  mockIsSameServiceArea,
  mockUpdateMoveLeg,
  mockApplyQuoteToOrder,
  mockCloneQuoteFromOrder
} from '../../../../setupTests';
import {
  ApplyQuoteToOrderRequest,
  CloneQuoteFromOrderResponse,
  Container,
  ContainerPlacement,
  MoveLeg,
  MoveLegTypeEnum,
  Order,
  SameServiceAreaRequest,
  ServiceAddress,
  UpdateMoveLegRequest
} from '../../../domain/OrderEntities';
import { PriceDifferenceContext } from '../../../locales/TranslationConstants';
import { cleanup, render, screen, within } from '@testing-library/react';
import { expectNotificationAlertContainsTitle } from '../../../testUtils/assertions';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import React, { act } from 'react';
import { QueryClientProvider } from '@tanstack/react-query';
import { runPendingPromises, testQueryClient } from '../../../testUtils/RenderHelpers';
import { NotificationProvider } from '../../../components/notifications/NotificationContext';
import { ContainerProvider } from '../../../context/ContainerContext';
import { MoveLegSection } from '../container/moveleg/current/MoveLegSection';
import userEvent from '@testing-library/user-event';
import { moveLegActions, moveLegViews as views } from './MoveLegViews';
import { vi } from 'vitest';
import { formatDate } from '../../../helpers/dateHelpers';
import { addDays, addYears } from 'date-fns';
import { MoveLegProvider } from '../container/moveleg/MoveLegContext';
import { ContainerPlacementModalProps } from '../container/scheduling/containerplacement/ContainerPlacementModal';
import Button from '@mui/material/Button';
import {
  initialContainerPlacement,
  useContainerPlacementContext
} from '../container/scheduling/containerplacement/context/ContainerPlacementContext';
import {
  ISingleOrderContextState,
  MoveLegScheduling,
  SingleOrderProvider
} from '../../../context/SingleOrderContext';
import { toServiceAddress } from '../container/scheduling/useMoveLegAddressState';
import { getVisitorId } from '../../../config/getVisitorId';

const Tx = TranslationKeys.HomePage.MoveLegs;

let testContainerPlacement: ContainerPlacement = initialContainerPlacement;
vi.mock('../container/scheduling/containerplacement/ContainerPlacementModal', () => ({
  ContainerPlacementModal: ({}: ContainerPlacementModalProps) => {
    const { handleFinish } = useContainerPlacementContext();
    return (
      <Button
        onClick={() => {
          handleFinish(testContainerPlacement);
        }}>
        {TranslationKeys.HomePage.ContainerPlacement.ReviewScreen.FINISH_BUTTON}
      </Button>
    );
  }
}));
vi.mock('../../../config/getVisitorId', () => ({
  getVisitorId: () => '123456789'
}));

describe('Scheduling Move Leg', () => {
  const quoteId = 'quoteId';
  const salesforceQuoteId = 'salesforceQuoteId';
  const todayDate = new Date('2024-01-01');
  const futureDate = addDays(todayDate, 7);
  const user = userEvent.setup({
    delay: null
  }); // Fixes an issue with react test timing out
  const actions = moveLegActions(user);

  const baseOrder = createOrder();
  const baseContainer = baseOrder.containers[0];
  const unscheduledMoveLeg = createMoveLeg({
    moveLegType: 'FINAL_PICKUP',
    scheduledDate: undefined,
    firstAvailableDate: addDays(todayDate, 1),
    lastAvailableDate: addYears(todayDate, 1)
  });
  const unscheduledContainerVisit = createMoveLeg({
    moveLegType: 'VISIT_CONTAINER',
    scheduledDate: undefined,
    firstAvailableDate: addDays(todayDate, 1),
    lastAvailableDate: addYears(todayDate, 1)
  });
  const scheduledContainerVisit = createMoveLeg({
    moveLegType: 'VISIT_CONTAINER',
    scheduledDate: new Date(),
    firstAvailableDate: addDays(todayDate, 1),
    lastAvailableDate: addYears(todayDate, 1)
  });
  let futureMoveLeg: MoveLeg;
  let scheduledContainerVisitMoveLeg: MoveLeg;
  const mockSetSelectedOrderId = vi.fn();

  const renderMoveLeg = (
    moveLeg: MoveLeg,
    container: Container = createContainer({ moveLegs: [moveLeg] }),
    order: Order = { ...baseOrder, containers: [container] },
    moveLegScheduling: MoveLegScheduling = createMoveLegScheduling({
      setSelectedOrderId: mockSetSelectedOrderId
    }),
    currentlySelectedQuote: CloneQuoteFromOrderResponse = createCloneQuoteFromOrderResponse({
      newQuoteIdentity: quoteId,
      newQuoteSalesforceId: salesforceQuoteId
    })
  ) => {
    mockCloneQuoteFromOrder.mockResolvedValue(currentlySelectedQuote);
    const singleContext: ISingleOrderContextState = {
      order,
      currentlySelectedQuote,
      moveLegScheduling
    };
    return render(
      <QueryClientProvider client={testQueryClient()}>
        <NotificationProvider>
          <SingleOrderProvider state={singleContext}>
            <ContainerProvider state={{ container, order }}>
              <MoveLegProvider moveLeg={moveLeg} lastMoveLegId={''} isLastRenderedMoveLeg={false}>
                <MoveLegSection />
              </MoveLegProvider>
            </ContainerProvider>
          </SingleOrderProvider>
        </NotificationProvider>
      </QueryClientProvider>
    );
  };

  beforeEach(() => {
    vi.useFakeTimers({ now: todayDate });
    mockedUseFeatureFlags.useFeatureFlags.mockImplementation(() =>
      createUseFeatureFlagResult({
        isOrderModEnabled: () => true,
        isRedesignedSchedulingEnabled: () => false,
        isRedesignedSchedulingPickupEnabled: () => false
      })
    );
    mockCloneQuoteFromOrder.mockResolvedValue(createCloneQuoteFromOrderResponse());

    futureMoveLeg = createMoveLeg({
      moveLegType: 'FINAL_PICKUP',
      scheduledDate: futureDate,
      firstAvailableDate: addDays(futureDate, 1),
      lastAvailableDate: addYears(futureDate, 1)
    });

    scheduledContainerVisitMoveLeg = createMoveLeg({
      ...futureMoveLeg,
      moveLegType: MoveLegTypeEnum.VISIT_CONTAINER,
      containerVisitDate: futureDate
    });
  });

  afterEach(() => {
    vi.useRealTimers();
    vi.clearAllMocks();
    cleanup();
  });

  beforeEach(() => {
    const thirtyDays = getThirtyDaysOfAvailability();
    const response: ContainerAvailabilityResponse = {
      eightFootAvailability: thirtyDays,
      twelveFootAvailability: thirtyDays,
      sixteenFootAvailability: thirtyDays
    };
    mockGetContainerAvailability.mockResolvedValue(response);
    mockUpdateMoveLeg.mockResolvedValue(createMoveLegUpdateResponse({ quoteId: undefined }));
  });

  it('should show date input when schedule button is clicked', async () => {
    renderMoveLeg(unscheduledMoveLeg);

    expect(views.dateInput()).not.toBeInTheDocument();

    await actions.clickSchedule();

    expect(views.dateInput()).toBeInTheDocument();
    expect(views.scheduleButton()).not.toBeInTheDocument();
  });

  it('should restore schedule button when cancel button clicked', async () => {
    renderMoveLeg(unscheduledMoveLeg);

    await actions.clickSchedule();
    await actions.clickCancel();

    expect(views.dateInput()).not.toBeInTheDocument();
    expect(views.scheduleButton()).toBeInTheDocument();
  });

  it('should allow user to reschedule a scheduled move leg', async () => {
    renderMoveLeg(futureMoveLeg);
    const tenDaysInFuture = formatDate(addDays(futureDate, 2), 'MM/dd/yyyy');

    await actions.clickEditButton();
    await actions.enterDate(tenDaysInFuture);
    await actions.clickSave();

    expect(mockUpdateMoveLeg).toHaveBeenCalled();
  });

  it('should show confirm button if quote has a price difference', async () => {
    mockUpdateMoveLeg.mockResolvedValue(
      createMoveLegUpdateResponse({ priceDifference: '1', quoteId: '1234' })
    );
    renderMoveLeg(futureMoveLeg);
    const tenDaysInFuture = formatDate(addDays(futureDate, 2), 'MM/dd/yyyy');

    await actions.clickEditButton();
    await actions.enterDate(tenDaysInFuture);
    await actions.clickSave();

    expect(mockUpdateMoveLeg).toHaveBeenCalled();
    expect(views.confirmButton()).toBeInTheDocument();
  });

  it('should show confirm button if quote has a price difference and apply quote to order if user clicks confirm', async () => {
    mockUpdateMoveLeg.mockResolvedValue(
      createMoveLegUpdateResponse({ priceDifference: '1', quoteId: '1234' })
    );
    renderMoveLeg(futureMoveLeg);
    const tenDaysInFuture = formatDate(addDays(futureDate, 2), 'MM/dd/yyyy');

    await actions.clickEditButton();
    await actions.enterDate(tenDaysInFuture);
    await actions.clickSave();
    expect(mockUpdateMoveLeg).toHaveBeenCalled();
    expect(views.confirmButton()).toBeInTheDocument();

    await actions.clickConfirm();

    expect(mockApplyQuoteToOrder).toHaveBeenCalledWith({ sfQuoteId: '1234' });
  });

  it('should disable the cancel button while a schedule change is saving', async () => {
    mockUpdateMoveLeg.mockImplementation(() => new Promise(() => {}));
    renderMoveLeg(unscheduledMoveLeg);

    await actions.clickSchedule();
    await actions.enterDate(formatDate(futureDate, 'MM/dd/yyyy'));
    await actions.clickSave();
    expect(views.cancelButton()).toBeDisabled();
  });

  it('should disable the date picker while a schedule change is saving', async () => {
    mockUpdateMoveLeg.mockImplementation(() => new Promise(() => {}));
    renderMoveLeg(unscheduledMoveLeg);

    await actions.clickSchedule();
    await actions.enterDate(formatDate(futureDate, 'MM/dd/yyyy'));
    await actions.clickSave();

    expect(views.dateInput()).toBeDisabled();
    // asserting the MUI calendar button also being disabled/removed from DOM.
    const formattedFutureDate = formatDate(futureDate, 'MMM d, yyyy');
    expect(
      screen.queryByRole('button', { name: `Choose date, selected date is ${formattedFutureDate}` })
    ).not.toBeInTheDocument();
  });

  it('given a move leg scheduled 24 hours from now or sooner, do not allow editing', () => {
    const tomorrowMoveLeg = createMoveLeg({
      ...futureMoveLeg,
      scheduledDate: addDays(new Date(), 1)
    });
    renderMoveLeg(tomorrowMoveLeg);

    expect(views.editButton()).not.toBeInTheDocument();
  });
  describe('visit container move legs', () => {
    it('given a an unscheduled visit container move leg then display schedule button and dont display the edit button', () => {
      const visitContainerLeg = createMoveLeg({
        ...futureMoveLeg,
        moveLegType: 'VISIT_CONTAINER',
        containerVisitDate: undefined
      });
      renderMoveLeg(visitContainerLeg);

      expect(views.editButton()).not.toBeInTheDocument();
      expect(views.scheduleButton()).toBeEnabled();
    });

    it('given a scheduled visit container move leg show the container visit edit button', () => {
      const visitContainerLeg = createMoveLeg({
        ...futureMoveLeg,
        moveLegType: 'VISIT_CONTAINER',
        containerVisitDate: futureDate
      });
      renderMoveLeg(visitContainerLeg);

      expect(screen.getByTestId('container-visit-edit-button')).toBeInTheDocument();
      expect(views.scheduleButton()).not.toBeInTheDocument();
    });
  });

  it('when an available date is selected, update move leg date via the the backend', async () => {
    renderMoveLeg(scheduledContainerVisit);

    await actions.clickSchedule();
    await actions.enterDate('01/02/2024');
    await actions.clickSave();

    const expectedRequest: UpdateMoveLegRequest = {
      orderId: baseOrder.orderId,
      quoteId: quoteId,
      salesforceQuoteId: salesforceQuoteId,
      containerOrderId: baseContainer.containerOrderId,
      moveLegId: scheduledContainerVisit.moveLegId,
      moveLegType: scheduledContainerVisit.moveLegType,
      requestedDate: '2024-01-02',
      transitDays: scheduledContainerVisit.transitDays,
      isCancelLeg: false,
      isUpdating: true,
      locationFields: {
        zip: scheduledContainerVisit.displayAddress.postalCode,
        moveLegType: scheduledContainerVisit.moveLegType,
        orderType: baseOrder.orderType,
        siteIdentity: scheduledContainerVisit.siteIdentity,
        isIfOpenCalendar: false,
        custTrackingId: '123456789'
      }
    };
    expect(mockUpdateMoveLeg).toHaveBeenCalledWith(expectedRequest);
  });

  it('when updating a scheduled visit set isUpdating to true', async () => {
    renderMoveLeg(unscheduledContainerVisit);

    await actions.clickSchedule();
    await actions.enterDate('01/02/2024');
    await actions.clickSave();

    const expectedRequest: UpdateMoveLegRequest = {
      orderId: baseOrder.orderId,
      quoteId: quoteId,
      salesforceQuoteId: salesforceQuoteId,
      containerOrderId: baseContainer.containerOrderId,
      moveLegId: unscheduledContainerVisit.moveLegId,
      moveLegType: unscheduledContainerVisit.moveLegType,
      requestedDate: '2024-01-02',
      transitDays: unscheduledContainerVisit.transitDays,
      isCancelLeg: false,
      isUpdating: false,
      locationFields: {
        zip: unscheduledContainerVisit.displayAddress.postalCode,
        moveLegType: unscheduledContainerVisit.moveLegType,
        orderType: baseOrder.orderType,
        siteIdentity: unscheduledContainerVisit.siteIdentity,
        isIfOpenCalendar: false,
        custTrackingId: '123456789'
      }
    };
    expect(mockUpdateMoveLeg).toHaveBeenCalledWith(expectedRequest);
  });

  describe('given a price difference returned from update move leg, display the correct alert', () => {
    it.each([
      {
        priceDifference: '-12.99',
        expectedContext: PriceDifferenceContext.DECREASE,
        expectedAmount: '12.99'
      },
      {
        priceDifference: '12.99',
        expectedContext: PriceDifferenceContext.INCREASE,
        expectedAmount: '12.99'
      },
      {
        priceDifference: '12.9',
        expectedContext: PriceDifferenceContext.INCREASE,
        expectedAmount: '12.90'
      },
      {
        priceDifference: '-12',
        expectedContext: PriceDifferenceContext.DECREASE,
        expectedAmount: '12.00'
      }
    ])(
      `given $priceDifference should render [$expectedContext,$expectedAmount]`,
      async ({ priceDifference, expectedContext, expectedAmount }) => {
        let priceDiffResponse = createMoveLegUpdateResponse({
          priceDifference,
          quoteId: '123456789'
        });
        mockUpdateMoveLeg.mockResolvedValue(priceDiffResponse);
        renderMoveLeg(unscheduledMoveLeg);

        await actions.clickSchedule();
        await actions.enterDate('01/02/2024');
        await actions.clickSave();

        let alert = within(screen.getByTestId('price-difference-alert'));
        expect(alert.getByText(Tx.Scheduling.PRICE_CHANGE_TITLE)).toBeInTheDocument();
        let expectedDescription = `${Tx.Scheduling.PRICE_CHANGE_DESCRIPTION}[${expectedContext},${expectedAmount}]`;
        expect(alert.getByText(expectedDescription)).toBeInTheDocument();
      }
    );
  });

  it('when update move leg returns a non zero quote id, ask the user to confirm the schedule change', async () => {
    let priceDiffResponse = createMoveLegUpdateResponse({
      priceDifference: '-12.99',
      quoteId: '123456789'
    });
    mockUpdateMoveLeg.mockResolvedValue(priceDiffResponse);
    renderMoveLeg(unscheduledMoveLeg);

    await actions.clickSchedule();
    await actions.enterDate('01/02/2024');
    await actions.clickSave();

    expect(views.dateInput()).toBeDisabled();
    mockUpdateMoveLeg.mockResolvedValue(createMoveLegUpdateResponse());
    await actions.clickConfirm();

    let expectedRequest: ApplyQuoteToOrderRequest = {
      sfQuoteId: '123456789'
    };
    expect(mockApplyQuoteToOrder).toHaveBeenCalledWith(expectedRequest);
  });

  it('given a move leg that is already scheduled, if the date is not changed, do not allow update move leg', async () => {
    const scheduledMoveLeg = {
      ...futureMoveLeg,
      scheduledDate: new Date('01-03-2024')
    };
    renderMoveLeg(scheduledMoveLeg);

    await actions.clickEditButton();
    await actions.enterDate('01/03/2024');

    expect(views.saveButton()).toBeDisabled();
  });

  it('should autofill the date input with scheduled date when rescheduling visit container move leg', async () => {
    renderMoveLeg(scheduledContainerVisitMoveLeg);
    await runPendingPromises();

    await actions.clickContainerVisitEditButton();

    const date = formatDate(futureDate, 'MM/dd/yyyy');
    expect(views.dateInput()).toHaveValue(date);
  });

  it('should not update move leg if the container is not available on that date', async () => {
    const response: ContainerAvailabilityResponse = {
      eightFootAvailability: [{ date: '2024-01-02', isAvailable: false }],
      twelveFootAvailability: [],
      sixteenFootAvailability: []
    };
    mockGetContainerAvailability.mockResolvedValue(response);
    const container = createContainer({ containerSize: '8', moveLegs: [unscheduledMoveLeg] });
    renderMoveLeg(unscheduledMoveLeg, container);

    await actions.clickSchedule();
    await actions.enterDate('01/02/2024');

    expect(views.saveButton()).toBeDisabled();
    expect(mockUpdateMoveLeg).not.toHaveBeenCalled();
  });

  it('should not update move leg a date before the first available is selected', async () => {
    let moveLeg = {
      ...unscheduledMoveLeg,
      firstAvailableDate: new Date('2024-01-05')
    };
    renderMoveLeg(moveLeg);

    await actions.clickSchedule();
    await actions.enterDate('01/04/2024');

    expect(views.saveButton()).toBeDisabled();
    expect(mockUpdateMoveLeg).not.toHaveBeenCalled();
  });

  it('should not update move leg a date after the last available is selected', async () => {
    let moveLeg = {
      ...unscheduledMoveLeg,
      lastAvailableDate: new Date('2024-01-10')
    };
    renderMoveLeg(moveLeg);

    await actions.clickSchedule();
    await actions.enterDate('01/11/2024');

    expect(views.saveButton()).toBeDisabled();
    expect(mockUpdateMoveLeg).not.toHaveBeenCalled();
  });

  it('should display a error alert when update move leg fails', async () => {
    mockUpdateMoveLeg.mockRejectedValue({});
    renderMoveLeg(unscheduledMoveLeg);

    await actions.clickSchedule();
    await actions.enterDate('01/02/2024');
    await actions.clickSave();

    expectNotificationAlertContainsTitle(
      TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE
    );
  });

  it('should display success snackbar when update move leg is successful', async () => {
    renderMoveLeg({ ...unscheduledMoveLeg, moveLegType: 'FINAL_PICKUP' });
    await actions.clickSchedule();
    await actions.enterDate('01/02/2024');
    await actions.clickSave();

    expectNotificationAlertContainsTitle(Tx.Scheduling.SCHEDULE_UPDATE_SUCCESSFUL);
  });

  it('should display success snackbar when scheduling a container at warehouse move leg is successful', async () => {
    renderMoveLeg({
      ...unscheduledMoveLeg,
      moveLegType: 'VISIT_CONTAINER',
      containerVisitDate: undefined
    });

    await actions.clickSchedule();
    await actions.enterDate('01/02/2024');
    await actions.clickSave();

    expectNotificationAlertContainsTitle(Tx.Scheduling.SCHEDULE_VISIT_CONTAINER_SUCCESSFUL);
  });

  it('should display success snackbar when rescheduling a visit container move leg is successful', async () => {
    renderMoveLeg({
      ...unscheduledMoveLeg,
      moveLegType: 'VISIT_CONTAINER',
      containerVisitDate: futureDate
    });

    await actions.clickSchedule();
    await actions.enterDate('01/02/2024');
    await actions.clickSave();

    expectNotificationAlertContainsTitle(Tx.Scheduling.RESCHEDULE_VISIT_CONTAINER_SUCCESSFUL);
  });

  it('given move leg is not schedulable online, should not render a schedule button', async () => {
    const unschedulableOnlineMoveLeg: MoveLeg = {
      ...unscheduledMoveLeg,
      isSchedulableOnline: false
    };
    renderMoveLeg(unschedulableOnlineMoveLeg);

    expect(views.scheduleButton()).not.toBeInTheDocument();
    expect(views.editButton()).not.toBeInTheDocument();
  });

  describe('Move and Redelivery Move Legs', () => {
    const newPostalCode = '90210';
    let redeliveryMoveLeg: MoveLeg;

    beforeEach(() => {
      mockIsSameServiceArea.mockResolvedValue(true);

      redeliveryMoveLeg = {
        ...unscheduledMoveLeg,
        moveLegType: 'REDELIVERY'
      };
    });

    it('should display address form', async () => {
      renderMoveLeg(redeliveryMoveLeg);
      await actions.clickSchedule();

      expect(screen.getByTestId('move-leg-address-form')).toBeInTheDocument();
    });

    it('should not display address form if not move or redelivery', async () => {
      const pickupMoveLeg: MoveLeg = {
        ...unscheduledMoveLeg,
        moveLegType: 'PICKUP'
      };

      renderMoveLeg(pickupMoveLeg);
      await actions.clickSchedule();

      expect(screen.queryByTestId('move-leg-address-form')).not.toBeInTheDocument();
    });

    it('should disable date picker and the save button when no address is present', async () => {
      const redeliveryWithNoAddress: MoveLeg = {
        ...redeliveryMoveLeg,
        displayAddress: createMoveLegAddress({ address1: undefined })
      };

      renderMoveLeg(redeliveryWithNoAddress);
      await actions.clickSchedule();

      expect(views.dateInput()).toBeDisabled();
      expect(views.saveButton()).toBeDisabled();
    });

    it('should enable date picker when address is present', async () => {
      renderMoveLeg(redeliveryMoveLeg);
      await actions.clickSchedule();

      expect(views.dateInput()).toBeEnabled();
    });

    it('datepicker should be enabled when postal code is servicable', async () => {
      let postalCode = '90210';

      renderMoveLeg(redeliveryMoveLeg);
      await actions.clickSchedule();
      await actions.enterPostalCode(postalCode);
      await actions.blurPostalCodeInput();

      expect(views.dateInput()).toBeEnabled();
      const expectedRequest: SameServiceAreaRequest = {
        originalAddress: redeliveryMoveLeg.displayAddress as ServiceAddress,
        updatedAddress: { ...redeliveryMoveLeg.displayAddress, postalCode } as ServiceAddress
      };
      expect(mockIsSameServiceArea).toHaveBeenCalledWith(expectedRequest);
    });

    it('should display an error and disable the save button when the zipcode is not in the same service area', async () => {
      mockIsSameServiceArea.mockResolvedValue(false);

      renderMoveLeg(redeliveryMoveLeg);
      await actions.clickSchedule();
      await actions.enterPostalCode(newPostalCode);
      await actions.blurPostalCodeInput();

      let alert = within(screen.getByTestId('same-area-alert'));
      expect(alert.getByText(Tx.Scheduling.SERVICE_NOT_SAME_AREA_TITLE)).toBeInTheDocument();
      expect(alert.getByText(Tx.Scheduling.SERVICE_NOT_SAME_AREA_DESCRIPTION)).toBeInTheDocument();
      expect(views.saveButton()).toBeDisabled();
    });

    it('container placement is disabled if valid address or date is not yet entered', async () => {
      renderMoveLeg(redeliveryMoveLeg);
      await actions.clickSchedule();

      expect(views.containerPlacementButton()).toBeDisabled();

      await actions.enterDate('01/02/2024');
      expect(views.containerPlacementButton()).toBeEnabled();
    });

    it('container placement is enabled if address and scheduled date are present but unchanged', async () => {
      renderMoveLeg({
        ...redeliveryMoveLeg,
        displayAddress: createMoveLegAddress(),
        scheduledDate: futureDate
      });
      await actions.clickSchedule();

      expect(views.containerPlacementButton()).toBeEnabled();
    });

    it('save button be disabled if address and date is valid, but container placement is not yet entered', async () => {
      renderMoveLeg(redeliveryMoveLeg);
      await actions.clickSchedule();

      await actions.enterPostalCode(newPostalCode);
      await actions.blurPostalCodeInput();
      await actions.enterDate('01/02/2024');

      expect(views.saveButton()).toBeDisabled();
    });

    it('should send container placement and updated address when updating move leg', async () => {
      renderMoveLeg(redeliveryMoveLeg);
      await actions.clickSchedule();

      await actions.enterPostalCode(newPostalCode);
      await actions.blurPostalCodeInput();
      await actions.enterDate('01/02/2024');

      await act(async () => user.click(views.containerPlacementButton()!!));
      await act(async () => user.click(views.containerPlacementFinishButton()));

      // Switch from button to a notification that the container placement was completed
      expect(views.containerPlacementButton()).not.toBeInTheDocument();
      expect(screen.getByText(Tx.Scheduling.CONTAINER_PLACEMENT_COMPLETED)).toBeInTheDocument();

      await actions.clickSave();

      let expectedRequest: UpdateMoveLegRequest = {
        orderId: baseOrder.orderId,
        quoteId: quoteId,
        salesforceQuoteId: salesforceQuoteId,
        containerOrderId: baseContainer.containerOrderId,
        moveLegId: redeliveryMoveLeg.moveLegId,
        moveLegType: redeliveryMoveLeg.moveLegType,
        requestedDate: '2024-01-02',
        transitDays: redeliveryMoveLeg.transitDays,
        isCancelLeg: false,
        isUpdating: true,
        serviceAddress: {
          ...toServiceAddress(redeliveryMoveLeg.displayAddress),
          postalCode: newPostalCode
        },
        containerPlacement: testContainerPlacement,
        locationFields: {
          zip: newPostalCode,
          moveLegType: redeliveryMoveLeg.moveLegType,
          orderType: baseOrder.orderType,
          siteIdentity: redeliveryMoveLeg.siteIdentity,
          isIfOpenCalendar: false,
          custTrackingId: '123456789'
        }
      };
      expect(mockUpdateMoveLeg).toHaveBeenCalledWith(expectedRequest);
    });
  });
});
