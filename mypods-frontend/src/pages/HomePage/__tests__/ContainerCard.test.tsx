import { render, screen } from '@testing-library/react';
import {
  createContainer,
  createCustomer,
  createMoveLeg,
  createOrder,
  createRefreshSessionClaims,
  createUseFeatureFlagResult
} from '../../../testUtils/MyPodsFactories';
import React, { Suspense } from 'react';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { ContainerCard } from '../container/ContainerCard';
import { ContainerProvider } from '../../../context/ContainerContext';
import { Container, Order } from '../../../domain/OrderEntities';
import { addDays } from 'date-fns';
import { QueryClientProvider } from '@tanstack/react-query';
import { testQueryClient } from '../../../testUtils/RenderHelpers';
import { SingleOrderProvider } from '../../../context/SingleOrderContext';
import { ContainerTileWrapper } from '../container/ContainerTileWrapper';
import {
  mockedUseFeatureFlags,
  mockGetCustomer,
  mockGetCustomerOrders,
  mockRefreshSession
} from '../../../../setupTests';
import { useGetCustomerOrders } from '../../../networkRequests/queries/useGetCustomerOrders';

const Tx = TranslationKeys.HomePage.ContainerTile;

const mockUseGetCustomerOrders = vi.hoisted(() => vi.fn<typeof useGetCustomerOrders>());

vi.mock('../../../networkRequests/queries/useGetCustomerOrders', () => {
  return {
    useGetCustomerOrders: mockUseGetCustomerOrders
  };
});

describe('ContainerCard', () => {
  const today = new Date();
  const tomorrow = addDays(today, 1);
  const orderId = '123456789';
  const containerId = 'CONTAINER123';
  const containerSize = '20ft';

  const baseMoveLeg = createMoveLeg({ scheduledDate: tomorrow });
  const baseOrder = createOrder({
    orderId,
    orderDate: new Date('2024-04-25T09:53:00.3731149'),
    containers: [
      createContainer({
        containerId,
        containerSize,
        moveLegs: [baseMoveLeg]
      })
    ]
  });
  const baseContainer = baseOrder.containers[0];

  async function renderContainerCard(
    renderOrder: Order = baseOrder,
    renderContainer: Container = renderOrder.containers[0]
  ) {
    render(
      <Suspense>
        <QueryClientProvider client={testQueryClient()}>
          <SingleOrderProvider state={{ order: renderOrder }}>
            <ContainerProvider state={{ container: renderContainer, order: renderOrder }}>
              <ContainerCard />
            </ContainerProvider>
          </SingleOrderProvider>
        </QueryClientProvider>
      </Suspense>
    );
  }

  async function renderContainerTileWrapper() {
    render(
      <Suspense>
        <QueryClientProvider client={testQueryClient()}>
          <ContainerTileWrapper />
        </QueryClientProvider>
      </Suspense>
    );
  }

  beforeEach(() => {
    mockedUseFeatureFlags.useFeatureFlags.mockImplementation(() =>
      createUseFeatureFlagResult({ isContainerCardsEnabled: () => true })
    );
  });

  describe('ContainerCard Component Rendering', () => {
    it('should render container card with container type and size', async () => {
      await renderContainerCard();
      expect(
        screen.getByText(`${Tx.ContainerHeader.CONTAINER_TYPE}[${containerSize}]`)
      ).toBeInTheDocument();
    });

    it('should render container card with container ID when container has ID', async () => {
      await renderContainerCard();
      expect(screen.getByText(`# ${containerId}`)).toBeInTheDocument();
    });

    it('should not render container ID when container ID is empty', async () => {
      const containerWithoutId = { ...baseContainer, containerId: '' };
      const orderWithoutContainerId = {
        ...baseOrder,
        containers: [containerWithoutId]
      };

      await renderContainerCard(orderWithoutContainerId, containerWithoutId);

      expect(screen.queryByText(new RegExp(`# ${containerId}`))).not.toBeInTheDocument();
    });
  });

  describe('Testing ContainerTile wrapper with container card feature flag, and multiple orders', () => {
    let orders: Order[];
    const sixteenFootContainer = createContainer({
      containerSize: '16',
      containerId: '789456123',
      moveLegs: []
    });
    const eightFootContainer = createContainer({
      containerSize: '8',
      containerId: '321654987',
      moveLegs: [createMoveLeg({ moveLegId: '1' })]
    });

    beforeEach(async () => {
      mockRefreshSession.mockResolvedValue(createRefreshSessionClaims());
      orders = [createOrder({ containers: [eightFootContainer, sixteenFootContainer] })];
      mockGetCustomer.mockResolvedValue(createCustomer());
      mockGetCustomerOrders.mockResolvedValue(orders);
      mockUseGetCustomerOrders.mockImplementation(
        (await vi.importActual('../../../networkRequests/queries/useGetCustomerOrders'))
          .useGetCustomerOrders as any
      );
    });

    it('should render two container cards when container card feature flag is enabled', async () => {
      await renderContainerTileWrapper();

      expect(
        await screen.findByText(
          `${Tx.ContainerHeader.CONTAINER_TYPE}[${sixteenFootContainer.containerSize}]`
        )
      ).toBeInTheDocument();
      expect(
        await screen.findByText(
          `${Tx.ContainerHeader.CONTAINER_TYPE}[${eightFootContainer.containerSize}]`
        )
      ).toBeInTheDocument();

      expect(screen.queryAllByTestId('container-tile-header')).toHaveLength(0);
    });

    it('should render two container tiles when container card feature flag is disabled', async () => {
      mockedUseFeatureFlags.useFeatureFlags.mockImplementation(() =>
        createUseFeatureFlagResult({ isContainerCardsEnabled: () => false })
      );

      await renderContainerTileWrapper();

      const headers = await screen.findAllByTestId('container-tile-header');
      expect(headers).toHaveLength(2);

      expect(
        screen.getByText(
          `${Tx.ContainerHeader.CONTAINER_TYPE}[${sixteenFootContainer.containerSize}]`
        )
      ).toBeInTheDocument();
      expect(
        screen.getByText(
          `${Tx.ContainerHeader.CONTAINER_TYPE}[${eightFootContainer.containerSize}]`
        )
      ).toBeInTheDocument();
    });

    it('should render a container image for every container in the order', async () => {
      await renderContainerTileWrapper();

      const containerCardImages = await screen.findAllByAltText('container-image');
      expect(containerCardImages).toHaveLength(2);
    });
  });
});
