import { mockedUseFeatureFlags, mockGetCustomer, mockRefreshSession } from '../../../../setupTests';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import {
  createCustomer,
  createRefreshSessionClaims,
  createUseFeatureFlagResult
} from '../../../testUtils/MyPodsFactories';
import { renderWithPoetProvidersAndState } from '../../../testUtils/RenderHelpers';
import { QuickLinks } from '../components/QuickLinks';
import { screen, within } from '@testing-library/react';
import { ROUTES } from '../../../Routes';

describe('Quicklinks', () => {
  const Tx = TranslationKeys.HomePage.QuickLinks;
  const views = {
    resources: () => screen.getByTestId('quick-links-card-container')
  };

  beforeEach(() => {
    mockedUseFeatureFlags.useFeatureFlags.mockImplementation(() =>
      createUseFeatureFlagResult({ isQuickLinksEnabled: () => true })
    );
    const session = createRefreshSessionClaims();
    mockRefreshSession.mockResolvedValue(session);
    mockGetCustomer.mockResolvedValue(createCustomer({ ...session }));
    renderWithPoetProvidersAndState(<QuickLinks />);
  });

  it('should render the quick links container', async () => {
    expect(await screen.findByTestId('quick-links-container')).toBeInTheDocument();
  });

  it('should render the header text', async () => {
    expect(await screen.findByText(Tx.HEADER)).toBeInTheDocument();
  });

  it('should render resources links', async () => {
    expect(await screen.findByTestId('quick-links-card-container')).toBeInTheDocument();
    expect(views.resources()).toHaveTextContent(Tx.AccountDetails.TITLE);
    expect(views.resources()).toHaveTextContent(Tx.Documents.TITLE);
    expect(views.resources()).toHaveTextContent(Tx.PaymentMethods.TITLE);
    expect(views.resources()).toHaveTextContent(Tx.InvoicesAndStatements.TITLE);
    expect(views.resources()).toHaveTextContent(Tx.MakeAPayment.TITLE);
  });

  it('renders each card as a link with the correct href', async () => {
    const cardContainer = await screen.findByTestId('quick-links-card-container');
    const links = within(cardContainer).getAllByRole('link');

    const expectedRoutes = [
      ROUTES.ACCOUNT,
      ROUTES.MANAGE_PAYMENT_METHOD,
      ROUTES.BILLING,
      ROUTES.MAKE_PAYMENT,
      ROUTES.DOCUMENT
    ];

    links.forEach((link, i) => {
      expect(link).toHaveAttribute('href', expectedRoutes[i]);
    });
  });

  it('does not render anything when quick-links feature flag is off', () => {
    mockedUseFeatureFlags.useFeatureFlags.mockImplementation(() =>
      createUseFeatureFlagResult({ isQuickLinksEnabled: () => false })
    );

    renderWithPoetProvidersAndState(<QuickLinks />);

    expect(screen.queryByTestId('quick-links-container')).not.toBeInTheDocument();
    expect(screen.queryByText(Tx.HEADER)).not.toBeInTheDocument();
  });
});
