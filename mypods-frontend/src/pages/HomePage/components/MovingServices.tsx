import { Text } from 'pods-component-library';
import React, { Fragment } from 'react';
import { css } from 'pods-component-library/styled-system/css';
import { useTranslation } from 'react-i18next';
import { CarProfileIcon } from '@phosphor-icons/react/ssr';
import { HomeDepotIcon } from '../../../components/icons/quickLinks/HomeDepotIcon';
import { QuickLinkFragment } from './QuickLinkFragment';
import { QuickLinkCard } from './QuickLinkCard';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { QuickIcon } from '../../../components/icons/quickLinks/QuickIcon';

export const MovingServices = () => {
  const { t: translate } = useTranslation();
  const movingServices = [
    {
      render: () => (
        <QuickLinkFragment
          icon={HomeDepotIcon}
          title={translate(TranslationKeys.HomePage.Sidebar.HomeDepot.TITLE)}
          subtitle={translate(TranslationKeys.HomePage.Sidebar.HomeDepot.DESCRIPTION)}
        />
      ),
      link: translate(TranslationKeys.HomePage.Sidebar.HomeDepot.URL)
    },
    {
      render: () => (
        <QuickLinkFragment
          icon={() => <QuickIcon icon={CarProfileIcon} />}
          title={translate(TranslationKeys.HomePage.Sidebar.CarShipping.TEXT)}
          subtitle={translate(TranslationKeys.HomePage.Sidebar.CarShipping.DESCRIPTION)}
        />
      ),
      link: translate(TranslationKeys.HomePage.Sidebar.CarShipping.URL)
    }
  ];
  return (
    <>
      <Text i18nKey={TranslationKeys.HomePage.Sidebar.SERVICES_TITLE} css={stylesheet.title} />
      {movingServices.map((movingService, index) => (
        <Fragment key={index}>
          <QuickLinkCard link={movingService.link} render={movingService.render} />
        </Fragment>
      ))}
    </>
  );
};

const stylesheet = {
  title: css.raw({
    fontFamily: 'primary',
    fontSize: 18,
    fontWeight: 900,
    letterSpacing: '0',
    color: 'accent900',
    lineHeight: '29px',
    marginTop: '32px',
    marginBottom: '12px'
  })
};
