import { Link } from '@mui/material';
import React, { ReactNode } from 'react';
import { css } from 'pods-component-library/styled-system/css';
import { FlexBox } from 'pods-component-library/src/components/Layout/FlexBox';
import { Box } from 'pods-component-library/src/components/Layout/Box';
import { Design } from '../../../helpers/Design';
import { RightChevronIcon } from '../../../components/icons/RightChevronIcon';
import { ROUTES } from '../../../Routes';
import { BlueLink } from '../../../components/buttons/NavigationLink';

export interface QuickLinkCardProps {
  link: string;
  render: () => ReactNode;
}

export const QuickLinkCard: React.FC<QuickLinkCardProps> = ({
  link,
  render
}: QuickLinkCardProps) => {
  const renderContainer = () => (
    <FlexBox css={stylesheet.container}>
      {render()}
      <Box css={stylesheet.chevron}>
        <RightChevronIcon
          style={{
            color: Design.Alias.Color.neutral300,
            width: '20px',
            height: '20px'
          }}
        />
      </Box>
    </FlexBox>
  );

  if (Object.values(ROUTES).includes(link as ROUTES)) {
    return (
      <BlueLink
        to={link}
        style={{ width: '100%', color: 'inherit', textAlign: 'left', textDecoration: 'none' }}>
        {renderContainer()}
      </BlueLink>
    );
  }
  return (
    <Link href={link} underline="none" target="_blank" color="inherit" style={{ width: '100%' }}>
      {renderContainer()}
    </Link>
  );
};

const stylesheet = {
  container: css.raw({
    gap: 'sm',
    padding: 'sm',
    borderRadius: '16px',
    borderWidth: '1px',
    borderStyle: 'solid',
    borderColor: 'neutral200',
    backgroundColor: 'neutral100',
    alignItems: 'center',
    height: '100%',
    _hover: {
      backgroundColor: '#1DBCFC0D'
    },
    _active: {
      backgroundColor: '#B3E3F680'
    }
  }),
  chevron: css.raw({
    alignItems: 'center',
    justifyContent: 'center'
  })
};
