import React from 'react';
import { Box } from 'pods-component-library/src/components/Layout/Box';
import { css } from 'pods-component-library/styled-system/css';

interface QuickLinkFragmentProps {
  icon: React.FC;
  title: string;
  subtitle: string;
}

export const QuickLinkFragment: React.FC<QuickLinkFragmentProps> = ({
  icon: IconComponent,
  title,
  subtitle
}: QuickLinkFragmentProps) => (
  <>
    <Box css={stylesheet.icon}>
      <IconComponent />
    </Box>
    <Box css={stylesheet.textWrapper}>
      <Box css={stylesheet.title}>{title}</Box>
      <Box css={stylesheet.subtitle}>{subtitle}</Box>
    </Box>
  </>
);

const stylesheet = {
  icon: css.raw({
    alignContent: 'center',
    justifyContent: 'center',
    alignItems: 'center',
    flex: '0 0 auto'
  }),
  textWrapper: css.raw({
    gap: 'xs',
    justifyContent: 'center',
    flex: '1 1 auto'
  }),
  title: css.raw({
    color: 'accent900',
    textStyle: 'bodyUniversal.smBold',
    fontSize: 'bodyUniversal.sm'
  }),
  subtitle: css.raw({
    color: 'neutral700',
    textStyle: 'bodyUniversal.xs',
    fontSize: 'bodyUniversal.xs'
  })
};
