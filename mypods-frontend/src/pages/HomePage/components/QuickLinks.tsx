import React, { Fragment } from 'react';
import { FlexBox } from 'pods-component-library/src/components/Layout/FlexBox';
import { Box } from 'pods-component-library/src/components/Layout/Box';
import { useTranslation } from 'react-i18next';
import { css } from 'pods-component-library/styled-system/css';
import { Typography } from '@mui/material';
import {
  CreditCardIcon,
  FileIcon,
  PackageIcon,
  ReceiptIcon,
  WalletIcon
} from '@phosphor-icons/react/ssr';
import { QuickLinkCard } from './QuickLinkCard';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { ROUTES } from '../../../Routes';
import { Design } from '../../../helpers/Design';
import { QuickLinkFragment } from './QuickLinkFragment';
import { MovingServices } from './MovingServices';
import { QuickIcon } from '../../../components/icons/quickLinks/QuickIcon';

export const QuickLinks = () => {
  const { t: translate } = useTranslation();
  const Tx = TranslationKeys.HomePage.QuickLinks;

  const links = [
    {
      render: () => (
        <QuickLinkFragment
          icon={() => <QuickIcon icon={PackageIcon} />}
          title={translate(Tx.AccountDetails.TITLE)}
          subtitle={translate(Tx.AccountDetails.SUBTITLE)}
        />
      ),
      link: ROUTES.ACCOUNT
    },
    {
      render: () => (
        <QuickLinkFragment
          icon={() => <QuickIcon icon={WalletIcon} />}
          title={translate(Tx.PaymentMethods.TITLE)}
          subtitle={translate(Tx.PaymentMethods.SUBTITLE)}
        />
      ),
      link: ROUTES.MANAGE_PAYMENT_METHOD
    },
    {
      render: () => (
        <QuickLinkFragment
          icon={() => <QuickIcon icon={ReceiptIcon} />}
          title={translate(Tx.InvoicesAndStatements.TITLE)}
          subtitle={translate(Tx.InvoicesAndStatements.SUBTITLE)}
        />
      ),
      link: ROUTES.BILLING
    },
    {
      render: () => (
        <QuickLinkFragment
          icon={() => <QuickIcon icon={CreditCardIcon} />}
          title={translate(Tx.MakeAPayment.TITLE)}
          subtitle={translate(Tx.MakeAPayment.SUBTITLE)}
        />
      ),
      link: ROUTES.MAKE_PAYMENT
    },
    {
      render: () => (
        <QuickLinkFragment
          icon={() => <QuickIcon icon={FileIcon} />}
          title={translate(Tx.Documents.TITLE)}
          subtitle={translate(Tx.Documents.SUBTITLE)}
        />
      ),
      link: ROUTES.DOCUMENT
    }
  ];
  return (
    <Box css={stylesheet.container} data-testid="quick-links-container">
      <FlexBox css={stylesheet.cardContainer} data-testid="quick-links-card-container">
        <Typography {...styles.title}>{translate(Tx.HEADER)}</Typography>
        {links.map((card, idx) => (
          <Fragment key={idx}>
            <QuickLinkCard link={card.link} render={card.render} />
          </Fragment>
        ))}
        <MovingServices />
      </FlexBox>
    </Box>
  );
};

const styles = {
  title: {
    sx: {
      ...Design.Alias.Text.Heading.Desktop.Xs,
      color: Design.Alias.Color.accent900,
      lineHeight: '29px'
    }
  }
};

const stylesheet = {
  container: css.raw({
    flexDirection: 'column',
    gap: 'xs',
    backgroundColor: 'neutral100',
    width: '100%'
  }),
  cardContainer: css.raw({
    flexDirection: 'column',
    gap: 'xxs',
    width: '100%'
  })
};
