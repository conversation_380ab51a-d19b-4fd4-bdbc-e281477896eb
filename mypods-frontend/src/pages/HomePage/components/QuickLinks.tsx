import React, { Fragment } from 'react';
import { FlexBox } from 'pods-component-library/src/components/Layout/FlexBox';
import { Box } from 'pods-component-library/src/components/Layout/Box';
import { useTranslation } from 'react-i18next';
import { css } from 'pods-component-library/styled-system/css';
import { Typography } from '@mui/material';
import { QuickLinkCard } from './QuickLinkCard';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { ROUTES } from '../../../Routes';
import { Design } from '../../../helpers/Design';
import { WalletIcon } from '../../../components/icons/quickLinks/WalletIcon';
import { PackageIcon } from '../../../components/icons/quickLinks/PackageIcon';
import { ReceiptIcon } from '../../../components/icons/quickLinks/ReceiptIcon';
import { CreditCardIcon } from '../../../components/icons/quickLinks/CreditCardIcon';
import { DocumentIcon } from '../../../components/icons/quickLinks/DocumentIcon';

interface QuickLinkFragmentProps {
  icon: React.FC;
  title: string;
  subtitle: string;
}

const QuickLinkFragment: React.FC<QuickLinkFragmentProps> = ({
  icon: IconComponent,
  title,
  subtitle
}) => (
  <>
    <Box css={pandaCssStyles.icon}>
      <IconComponent />
    </Box>
    <Box css={pandaCssStyles.textWrapper}>
      <Box css={pandaCssStyles.title}>{title}</Box>
      <Box css={pandaCssStyles.subtitle}>{subtitle}</Box>
    </Box>
  </>
);

export const QuickLinks = () => {
  const { t: translate } = useTranslation();
  const Tx = TranslationKeys.HomePage.QuickLinks;

  const links = [
    {
      render: () => (
        <QuickLinkFragment
          icon={PackageIcon}
          title={translate(Tx.AccountDetails.TITLE)}
          subtitle={translate(Tx.AccountDetails.SUBTITLE)}
        />
      ),
      link: ROUTES.ACCOUNT
    },
    {
      render: () => (
        <QuickLinkFragment
          icon={WalletIcon}
          title={translate(Tx.PaymentMethods.TITLE)}
          subtitle={translate(Tx.PaymentMethods.SUBTITLE)}
        />
      ),
      link: ROUTES.MANAGE_PAYMENT_METHOD
    },
    {
      render: () => (
        <QuickLinkFragment
          icon={ReceiptIcon}
          title={translate(Tx.InvoicesAndStatements.TITLE)}
          subtitle={translate(Tx.InvoicesAndStatements.SUBTITLE)}
        />
      ),
      link: ROUTES.BILLING
    },
    {
      render: () => (
        <QuickLinkFragment
          icon={CreditCardIcon}
          title={translate(Tx.MakeAPayment.TITLE)}
          subtitle={translate(Tx.MakeAPayment.SUBTITLE)}
        />
      ),
      link: ROUTES.MAKE_PAYMENT
    },
    {
      render: () => (
        <QuickLinkFragment
          icon={DocumentIcon}
          title={translate(Tx.Documents.TITLE)}
          subtitle={translate(Tx.Documents.SUBTITLE)}
        />
      ),
      link: ROUTES.DOCUMENT
    }
  ];
  return (
    <Box css={pandaCssStyles.container} data-testid="quick-links-container">
      <FlexBox css={pandaCssStyles.cardContainer} data-testid="quick-links-card-container">
        <Typography {...styles.title}>{translate(Tx.HEADER)}</Typography>
        {links.map((card, idx) => (
          <Fragment key={idx}>
            <QuickLinkCard link={card.link} render={card.render} />
          </Fragment>
        ))}
      </FlexBox>
    </Box>
  );
};

const pandaCssStyles = {
  container: css.raw({
    flexDirection: 'column',
    gap: 'xs',
    padding: 'sm',
    borderRadius: '16px',
    borderWidth: '1px',
    borderStyle: 'solid',
    borderColor: 'neutral200',
    backgroundColor: 'neutral100'
  }),
  cardContainer: css.raw({
    flexDirection: 'column',
    gap: 'xxs',
    width: '100%'
  }),
  icon: css.raw({
    alignContent: 'center',
    justifyContent: 'center',
    alignItems: 'center',
    flex: '0 0 auto'
  }),
  textWrapper: css.raw({
    gap: 'xs',
    justifyContent: 'center',
    flex: '1 1 auto'
  }),
  title: css.raw({
    color: 'accent900',
    textStyle: 'bodyUniversal.smBold',
    fontSize: 'bodyUniversal.sm'
  }),
  subtitle: css.raw({
    color: 'neutral700',
    textStyle: 'bodyUniversal.xs',
    fontSize: 'bodyUniversal.xs'
  })
};

const styles = {
  title: {
    sx: {
      ...Design.Alias.Text.Heading.Desktop.Xs,
      color: Design.Alias.Color.accent900,
      lineHeight: '29px'
    }
  }
};
