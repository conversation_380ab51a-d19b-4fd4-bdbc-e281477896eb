import React, { act, useContext, useState } from 'react';
import { Box, Divider } from '@mui/material';
import { ArrowUpRight } from '@phosphor-icons/react';
import { useTranslation } from 'react-i18next';
import { Design } from '../../../helpers/Design';
import { NotificationContext } from '../../../components/notifications/NotificationContext';
import { getSuccessMessage } from '../container/scheduling/ScheduleMoveLeg';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import useMoveLegContext from '../container/moveleg/MoveLegContext';
import { SchedulingFooter } from './containerfields/SchedulingFooter';
import useSingleOrderContext from '../../../context/SingleOrderContext';
import { ContainerPlacement } from '../../../domain/OrderEntities';
import { ScheduleMoveLegPanel } from './SchedulingPanel/ScheduleMoveLegPanel';
import LoadingScreen from '../../../components/Loading/LoadingScreen';
import { SchedulingHeader } from './containerfields/SchedulingHeader';
import { toCamelCase } from '../../../helpers/stringHelper';
import { SchedulingAddress } from './containerfields/SchedulingAddress';
import { formatAddress } from '../../../networkRequests/responseEntities/CustomerEntities';
import { SchedulingDatePicker } from './containerfields/SchedulingDatePicker';
import { SchedulingDescription } from './containerfields/SchedulingDescription';
interface ManagePickupPanelProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (
    containerPlacement: ContainerPlacement | undefined,
    onSuccess: () => void,
    onError: () => void
  ) => Promise<void>;
}

const Tx = TranslationKeys.HomePage.MoveLegs.Scheduling;

export const ManagePickupPanel: React.FC<ManagePickupPanelProps> = ({
  isOpen,
  onClose,
  onSave
}) => {
  const { setNotification } = useContext(NotificationContext);
  const { t: translate } = useTranslation();
  const { moveLeg } = useMoveLegContext();
  const { moveLegScheduling } = useSingleOrderContext();
  const { isSaving, setIsSaving } = moveLegScheduling;
  const [isDateSelected, setIsDateSelected] = useState(false);
  const [isDatePickerOpen, setIsDatePickerOpen] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [activeStep, setActiveStep] = useState(0);

  const steps = [
    {
      label: translate(Tx.PickupContainer.Title.STEP1, {
        context: toCamelCase(moveLeg.moveLegType.toString())
      }),
      title: translate(Tx.PickupContainer.Title.STEP1, {
        context: toCamelCase(moveLeg.moveLegType.toString())
      }),
      subtitle: translate(Tx.PickupContainer.SUBTITLE)
    },
    {
      label: translate(Tx.PickupContainer.Title.STEP2, {
        context: toCamelCase(moveLeg.moveLegType.toString())
      }),
      title: translate(Tx.PickupContainer.Title.STEP2, {
        context: toCamelCase(moveLeg.moveLegType.toString())
      }),
      subtitle: translate(Tx.PickupContainer.SUBTITLE)
    }
  ];

  const isOnLastStep = activeStep === steps.length - 1;

  const handleNext = () => {
    if (selectedDate !== null) {
      setIsDateSelected(true);
    }
    if (!isOnLastStep) {
      setActiveStep(activeStep + 1);
    }
  };

  const handleBack = () => {
    setIsDateSelected(false);
    if (activeStep > 0) {
      setActiveStep(activeStep - 1);
    }
  };

  const handleClose = () => {
    onClose();
    setIsDateSelected(false);
    setSelectedDate(null);
  };

  const handleSave = () => {
    setIsSaving(true);
    onSave(
      undefined,
      () => {
        onClose();
        setIsSaving(false);
        setIsDateSelected(false);
        setSelectedDate(null);
        setNotification({
          isError: false,
          message: translate(getSuccessMessage(moveLeg))
        });
      },
      () => {
        onClose();
        setIsSaving(false);
        setIsDateSelected(false);
        setSelectedDate(null);
        setNotification({
          message: translate(TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE),
          isError: true
        });
      }
    );
  };

  return (
    <ScheduleMoveLegPanel isOpen={isOpen} onClose={handleClose} data-testid="manage-pickup-panel">
      {isSaving ? (
        <LoadingScreen loadingText={translate(Tx.PickupContainer.LOADING_TEXT)} />
      ) : (
        <>
          <SchedulingHeader
            icon={<ArrowUpRight size={40} color={Design.Primitives.Color.Blue.oasis} />}
            showStepper={false}
            steps={steps}
            activeStep={activeStep}
          />
          <Box sx={styles.addressContainer}>
            <SchedulingAddress
              showLabel
              addressValue={formatAddress(moveLeg.displayAddress)}
              title={translate(Tx.PickupContainer.DETAILS_TITLE)}
            />
            <Divider sx={styles.divider} />
            <SchedulingDatePicker
              selectedDate={selectedDate}
              setSelectedDate={setSelectedDate}
              hidePickerWhenSelected
              isDatePickerDisabled={isSaving}
              isDatePickerOpen={isDatePickerOpen}
              isDateSelected={isDateSelected}
              label={Tx.PickupContainer.DATE_LABEL}
              saveButtonIsLoading={isSaving}
              setIsDatePickerOpen={setIsDatePickerOpen}
            />
          </Box>

          {!isDateSelected && <SchedulingDescription />}
        </>
      )}
      <SchedulingFooter
        isDateSelected={isDateSelected}
        onClose={onClose}
        handleBack={handleBack}
        setIsDateSelected={setIsDateSelected}
        setSelectedDate={setSelectedDate}
        selectedDate={selectedDate}
        isSaving={isSaving}
        handleSave={handleSave}
        handleNext={handleNext}
      />
    </ScheduleMoveLegPanel>
  );
};

const styles = {
  divider: {
    width: '100%'
  },
  addressContainer: {
    display: 'flex',
    padding: Design.Primitives.Spacing.sm,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'flex-start',
    gap: Design.Primitives.Spacing.sm,
    alignSelf: 'stretch',
    border: `1px solid ${Design.Alias.Color.neutral200}`,
    borderRadius: Design.Primitives.Spacing.sm
  }
};
