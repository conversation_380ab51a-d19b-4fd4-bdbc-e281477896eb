import React, { useContext, useState } from 'react';
import { Box, Divider, useMediaQuery } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { ArrowUpRightIcon } from '@phosphor-icons/react';
import { Design } from '../../../helpers/Design';
import { NotificationContext } from '../../../components/notifications/NotificationContext';
import { getSuccessMessage } from '../container/scheduling/ScheduleMoveLeg';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import useMoveLegContext from '../container/moveleg/MoveLegContext';
import { SchedulingActionBarProps } from './containerfields/SchedulingActionBar';
import useSingleOrderContext from '../../../context/SingleOrderContext';
import {
  ScheduleMoveLegPanel,
  sharedBodyContainerStyles
} from './SchedulingPanel/ScheduleMoveLegPanel';
import LoadingScreen from '../../../components/Loading/LoadingScreen';
import { SchedulingHeader } from './containerfields/SchedulingHeader';
import { toCamelCase } from '../../../helpers/stringHelper';
import { formatAddress } from '../../../networkRequests/responseEntities/CustomerEntities';
import { SchedulingDatePicker } from './containerfields/SchedulingDatePicker';
import { SchedulingDescription } from './containerfields/SchedulingDescription';
import { theme } from '../../../PodsTheme';
import { OnMoveLegUpdateSaveProps } from './ManageVisitPanel';
import { DisplayAddress } from './containerfields/DisplayAddress';

interface ManagePickupPanelProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (callbackProps: OnMoveLegUpdateSaveProps) => void;
}

const Tx = TranslationKeys.HomePage.MoveLegs.Scheduling;

export const ManagePickupPanel: React.FC<ManagePickupPanelProps> = ({
  isOpen,
  onClose,
  onSave
}) => {
  const { setNotification } = useContext(NotificationContext);
  const { t: translate } = useTranslation();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const {
    moveLeg,
    scheduling: {
      dateState: { selectedDate }
    }
  } = useMoveLegContext();
  const { moveLegScheduling } = useSingleOrderContext();
  const { isSaving, setIsSaving } = moveLegScheduling;
  const [isDateSelected, setIsDateSelected] = useState(false);
  const [isDatePickerOpen, setIsDatePickerOpen] = useState(false);
  const [activeStep, setActiveStep] = useState(0);

  const steps = [
    {
      label: '1',
      title: translate(Tx.PickupContainer.Title.STEP1, {
        context: toCamelCase(moveLeg.moveLegType.toString())
      }),
      subtitle: translate(Tx.PickupContainer.SUBTITLE)
    },
    {
      label: '2',
      title: translate(Tx.PickupContainer.Title.STEP2, {
        context: toCamelCase(moveLeg.moveLegType.toString())
      }),
      subtitle: translate(Tx.PickupContainer.SUBTITLE)
    }
  ];

  const isOnLastStep = activeStep === steps.length - 1;

  const handleNext = () => {
    if (selectedDate !== null) {
      setIsDateSelected(true);
    }
    if (!isOnLastStep) {
      setActiveStep(activeStep + 1);
    }
  };

  const handleBack = () => {
    setIsDatePickerOpen(false);
    setIsDateSelected(false);
    if (activeStep > 0) {
      setActiveStep(activeStep - 1);
    }
  };

  const handleClose = () => {
    setIsDatePickerOpen(false);
    setIsDateSelected(false);
    onClose();
  };

  const handleSave = () => {
    setIsSaving(true);
    onSave({
      containerPlacement: undefined,
      onSuccess: () => {
        setIsSaving(false);
        setIsDateSelected(false);
        setNotification({
          isError: false,
          message: translate(getSuccessMessage(moveLeg))
        });
      },
      onError: (_error: unknown) => {
        setIsSaving(false);
        setIsDateSelected(false);
        setNotification({
          message: translate(TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE),
          isError: true
        });
      }
    });
  };

  const actionProps: SchedulingActionBarProps = {
    isDateSelected,
    onCancel: handleClose,
    handleBack,
    isSaving,
    handleSave,
    handleNext,
    currentStep: activeStep,
    isLastStep: isOnLastStep
  };

  return (
    <ScheduleMoveLegPanel isOpen={isOpen} onClose={handleClose} actionProps={actionProps}>
      {isSaving ? (
        <LoadingScreen loadingText={translate(Tx.PickupContainer.LOADING_TEXT)} />
      ) : (
        <Box data-testid="manage-pickup-panel" sx={sharedBodyContainerStyles(isMobile)}>
          <SchedulingHeader
            icon={<ArrowUpRightIcon size={40} color={Design.Alias.Color.secondary500} />}
            showStepper={false}
            steps={steps}
            activeStep={activeStep}
          />
          <Box sx={styles.addressContainer}>
            <DisplayAddress
              showLabel
              addressValue={formatAddress(moveLeg.displayAddress)}
              title={translate(Tx.PickupContainer.DETAILS_TITLE)}
            />
            <Divider sx={styles.divider} />
            <SchedulingDatePicker
              hidePickerWhenSelected
              isDatePickerDisabled={isSaving}
              isDatePickerOpen={isDatePickerOpen}
              isDateSelected={isDateSelected}
              label={Tx.PickupContainer.DATE_LABEL}
              saveButtonIsLoading={isSaving}
              setIsDatePickerOpen={setIsDatePickerOpen}
              scheduledDate={moveLeg.scheduledDate}
            />
          </Box>

          {!isDateSelected && <SchedulingDescription />}
        </Box>
      )}
    </ScheduleMoveLegPanel>
  );
};

const styles = {
  divider: {
    width: '100%'
  },
  addressContainer: {
    display: 'flex',
    padding: Design.Primitives.Spacing.sm,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'flex-start',
    gap: Design.Primitives.Spacing.sm,
    alignSelf: 'stretch',
    border: `1px solid ${Design.Alias.Color.neutral200}`,
    borderRadius: Design.Primitives.Spacing.sm
  }
};
