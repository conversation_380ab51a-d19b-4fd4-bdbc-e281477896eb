import React from 'react';
import { DesktopDatePicker, LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { Box, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { DatepickerTextField } from '../../container/scheduling/DatepickerTextField';
import { DatePickerDay } from '../../container/scheduling/DatePickerDay';
import { Design } from '../../../../helpers/Design';
import useMoveLegContext from '../../container/moveleg/MoveLegContext';

interface SchedulingDatePickerProps {
  selectedDate: Date | null;
  setSelectedDate: (date: Date | null) => void;
  hidePickerWhenSelected?: boolean;
  isDatePickerDisabled: boolean;
  isDatePickerOpen: boolean;
  isDateSelected: boolean;
  label?: string;
  saveButtonIsLoading: boolean;
  setIsDatePickerOpen: (open: boolean) => void;
}

export const SchedulingDatePicker: React.FC<SchedulingDatePickerProps> = ({
  selectedDate,
  setSelectedDate,
  isDateSelected,
  isDatePickerDisabled,
  saveButtonIsLoading,
  setIsDatePickerOpen,
  isDatePickerOpen,
  label,
  hidePickerWhenSelected = false
}) => {
  const { t: translate } = useTranslation();
  const {
    scheduling: { dateState }
  } = useMoveLegContext();

  const handleSelectDate = (pickedDate: Date | null | undefined) => {
    if (pickedDate != null) {
      setSelectedDate(pickedDate);
    }
    setIsDatePickerOpen(false);
  };

  const showCalendar = () => {
    const day = selectedDate ?? new Date();

    if (!isDatePickerDisabled) {
      setIsDatePickerOpen(true);
      dateState.addContainerAvailabilitiesFor3Months(day);
    }
  };

  const labelText = label
    ? translate(label)
    : translate('HomePage.MoveLegs.Scheduling.PickupContainer.DATE_LABEL');

  if (hidePickerWhenSelected && isDateSelected) {
    return (
      <Box>
        <Typography variant="subtitle1">{labelText}</Typography>
        <Typography variant="body1" color={Design.Alias.Color.accent900}>
          {selectedDate
            ? selectedDate.toLocaleDateString('en-US', {
                weekday: 'short',
                month: 'short',
                day: 'numeric',
                year: 'numeric'
              })
            : ''}
        </Typography>
      </Box>
    );
  }

  return (
    <div style={{ width: '100%' }}>
      {isDateSelected ? (
        <Box>
          <Typography variant="subtitle1">{labelText}</Typography>
          <Typography variant="body1" color={Design.Alias.Color.accent900}>
            {selectedDate
              ? selectedDate.toLocaleDateString('en-US', {
                  weekday: 'short',
                  month: 'short',
                  day: 'numeric',
                  year: 'numeric'
                })
              : ''}
          </Typography>
        </Box>
      ) : (
        <LocalizationProvider dateAdapter={AdapterDateFns}>
          <DesktopDatePicker
            label={labelText}
            disabled={isDatePickerDisabled}
            loading={dateState.containerAvailabilityPending}
            renderLoading={() => <div>Loading</div>}
            renderInput={(params) => (
              <DatepickerTextField
                onClick={showCalendar}
                params={params}
                data-testid="date-input"
              />
            )}
            onClose={() => setIsDatePickerOpen(false)}
            onChange={(e) => handleSelectDate(e)}
            value={selectedDate}
            open={isDatePickerOpen}
            disablePast
            disableOpenPicker={saveButtonIsLoading}
            views={['day']}
            defaultCalendarMonth={dateState.getCalendarStartDateWithAvailability()}
            onMonthChange={(day) => dateState.addContainerAvailabilitiesFor3Months(day!)}
            // Note: later when the next available day is not today's date, it could be next month, so we need to toggle the datepicker forward
            renderDay={(day, selectedDays, pickersDayProps) => (
              <DatePickerDay
                key={day.toISOString()}
                date={day}
                selectedDay={selectedDays[0]}
                pickersDayProps={pickersDayProps}
                isAvailable={dateState.isAvailable}
                onClick={handleSelectDate}
              />
            )}
          />
        </LocalizationProvider>
      )}
    </div>
  );
};
