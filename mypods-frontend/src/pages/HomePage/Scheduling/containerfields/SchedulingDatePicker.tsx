import React, { useEffect } from 'react';
import { DesktopDatePicker, LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { Box, InputAdornment, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { DatepickerTextField } from '../../container/scheduling/DatepickerTextField';
import { DatePickerDay } from '../../container/scheduling/DatePickerDay';
import { Design } from '../../../../helpers/Design';
import useMoveLegContext from '../../container/moveleg/MoveLegContext';
import { formatToLocale } from '../../../../helpers/dateHelpers';
import { CalendarDotsIcon } from '@phosphor-icons/react';

interface SchedulingDatePickerProps {
  hidePickerWhenSelected?: boolean;
  isDatePickerDisabled: boolean;
  isDatePickerOpen: boolean;
  isDateSelected: boolean;
  label?: string;
  saveButtonIsLoading: boolean;
  setIsDatePickerOpen: (open: boolean) => void;
  scheduledDate?: Date;
}

export const SchedulingDatePicker: React.FC<SchedulingDatePickerProps> = ({
  isDateSelected,
  isDatePickerDisabled,
  saveButtonIsLoading,
  setIsDatePickerOpen,
  isDatePickerOpen,
  label,
  hidePickerWhenSelected = false,
  scheduledDate
}) => {
  const { t: translate } = useTranslation();
  const {
    scheduling: {
      dateState: {
        selectedDate,
        setSelectedDate,
        addContainerAvailabilitiesFor3Months,
        getCalendarStartDateWithAvailability,
        isAvailable,
        containerAvailabilityPending,
        getCalendarStartDate
      }
    }
  } = useMoveLegContext();

  useEffect(() => {
    addContainerAvailabilitiesFor3Months(scheduledDate ?? getCalendarStartDate());
  }, []);

  const handleSelectDate = (pickedDate: Date | null | undefined) => {
    if (pickedDate != null) {
      setSelectedDate(pickedDate);
    }
    setIsDatePickerOpen(false);
  };

  const showCalendar = () => {
    const day = selectedDate ?? new Date();

    if (!isDatePickerDisabled) {
      setIsDatePickerOpen(true);
      addContainerAvailabilitiesFor3Months(day);
    }
  };

  const labelText = label
    ? translate(label)
    : translate('HomePage.MoveLegs.Scheduling.PickupContainer.DATE_LABEL');

  const selectedDateDisplay = (
    <>
      <Typography variant="subtitle1">{labelText}</Typography>
      <Typography variant="body1" color={Design.Alias.Color.accent900}>
        {selectedDate ? formatToLocale(selectedDate) : ''}
      </Typography>
    </>
  );

  if (hidePickerWhenSelected && isDateSelected) {
    return <Box>{selectedDateDisplay}</Box>;
  }

  return (
    <div style={{ width: '100%' }}>
      {isDateSelected ? (
        <Box>{selectedDateDisplay}</Box>
      ) : (
        <LocalizationProvider dateAdapter={AdapterDateFns}>
          <DesktopDatePicker
            label={labelText}
            disabled={isDatePickerDisabled}
            loading={containerAvailabilityPending}
            onClose={() => {
              setIsDatePickerOpen(false);
            }}
            onChange={(e) => {
              handleSelectDate(e);
            }}
            value={selectedDate}
            open={isDatePickerOpen}
            disablePast
            disableOpenPicker={saveButtonIsLoading}
            views={['day']}
            defaultCalendarMonth={getCalendarStartDateWithAvailability()}
            onMonthChange={(day) => {
              addContainerAvailabilitiesFor3Months(day);
            }}
            PopperProps={{ placement: 'bottom-start' }}
            renderLoading={() => <div>Loading</div>}
            renderInput={(params) => {
              const iconColor = iconColorStyle(isDatePickerDisabled, isDatePickerOpen);
              const borderColor = borderColorStyle(isDatePickerDisabled, isDatePickerOpen);

              const inputProps = {
                ...params.InputProps,
                endAdornment: null,
                startAdornment: (
                  <InputAdornment position="start" sx={{ cursor: 'pointer' }}>
                    <CalendarDotsIcon
                      onClick={showCalendar}
                      size={24}
                      style={{ color: iconColor }}
                    />
                  </InputAdornment>
                ),
                sx: {
                  // default border
                  '& .MuiOutlinedInput-notchedOutline': { borderColor: borderColor },

                  // hover border
                  '&:hover .MuiOutlinedInput-notchedOutline': { borderColor: borderColor },

                  // focused border
                  '&.Mui-focused .MuiOutlinedInput-notchedOutline': { borderColor: borderColor },

                  // disabled border
                  '&.Mui-disabled .MuiOutlinedInput-notchedOutline': { borderColor: borderColor }
                }
              } as typeof params.InputProps;

              return (
                <DatepickerTextField
                  data-testid="date-input"
                  onClick={showCalendar}
                  params={{
                    ...params,
                    InputProps: inputProps,
                    // optional: keep label color in sync
                    InputLabelProps: {
                      ...params.InputLabelProps,
                      sx: {
                        color: borderColor,
                        '&.Mui-focused': { color: borderColor }
                      }
                    }
                  }}
                />
              );
            }}
            renderDay={(day, selectedDays, pickersDayProps) => (
              <DatePickerDay
                key={day.toISOString()}
                date={day}
                selectedDay={selectedDays[0]}
                pickersDayProps={pickersDayProps}
                isAvailable={isAvailable}
                onClick={handleSelectDate}
              />
            )}
          />
        </LocalizationProvider>
      )}
    </div>
  );
};

const iconColorStyle = (isDisabled: boolean, isDatePickerOpen: boolean) => {
  if (isDisabled) {
    return Design.Alias.Color.neutral400;
  }

  return isDatePickerOpen ? Design.Alias.Color.secondary500 : Design.Alias.Color.neutral600;
};

const borderColorStyle = (isDisabled: boolean, isDatePickerOpen: boolean) => {
  if (isDisabled) {
    return Design.Alias.Color.neutral400;
  }

  return isDatePickerOpen ? Design.Alias.Color.secondary500 : Design.Alias.Color.neutral600;
};
