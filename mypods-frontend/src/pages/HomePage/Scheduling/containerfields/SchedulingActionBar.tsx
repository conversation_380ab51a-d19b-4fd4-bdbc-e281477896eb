import React from 'react';
import { Box } from '@mui/material';
import { Button } from 'pods-component-library';
import { useTranslation } from 'react-i18next';
import { Design } from '../../../../helpers/Design';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import useMoveLegContext from '../../container/moveleg/MoveLegContext';

export interface SchedulingActionBarProps {
  isDateSelected: boolean;
  onCancel: () => void;
  handleBack: () => void;
  isSaving: boolean;
  handleSave: () => void;
  handleNext: () => void;
  currentStep: number;
  isLastStep: boolean;
}

const Tx = TranslationKeys.CommonComponents;

export const SchedulingActionBar: React.FC<SchedulingActionBarProps> = ({
  handleBack,
  handleNext,
  handleSave,
  isDateSelected,
  isSaving,
  onCancel,
  currentStep,
  isLastStep
}) => {
  const { t: translate } = useTranslation();
  const {
    scheduling: {
      dateState: { selectedDate }
    }
  } = useMoveLegContext();

  const NextButton = (
    <Button
      variant="outlined"
      buttonSize="large"
      color="secondary"
      onPress={handleNext}
      isDisabled={!isDateSelected && !selectedDate}>
      {translate(Tx.NEXT_BUTTON)}
    </Button>
  );

  const ConfirmButton = (
    <Button
      variant="animated"
      buttonSize="large"
      color="secondary"
      isDisabled={(!isDateSelected && !selectedDate) || isSaving}
      onPress={handleSave}
      isLoading={isSaving}>
      {translate(Tx.CONFIRM_BUTTON)}
    </Button>
  );

  const CancelButton = (
    <Button
      buttonSize="large"
      color="secondary"
      variant="outlined"
      onPress={onCancel}
      isDisabled={isSaving}>
      {translate(Tx.CANCEL_BUTTON)}
    </Button>
  );

  const BackButton = (
    <Button
      buttonSize="large"
      color="secondary"
      variant="outlined"
      onPress={handleBack}
      isDisabled={isSaving}>
      {translate(Tx.BACK_BUTTON)}
    </Button>
  );

  return (
    <Box sx={styles.moveFooter}>
      {currentStep > 0 ? BackButton : CancelButton}
      {isLastStep ? ConfirmButton : NextButton}
    </Box>
  );
};

const styles = {
  moveFooter: {
    display: 'flex',
    padding: Design.Primitives.Spacing.sm,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    gap: Design.Primitives.Spacing.sm,
    position: 'absolute',
    bottom: 0,
    background: Design.Alias.Color.neutral100,
    boxShadow: '0px 0px 8px 0px var(--color-neutral-neutral300, #CBCBCB)',
    width: '100%',
    boxSizing: 'border-box'
  }
};
