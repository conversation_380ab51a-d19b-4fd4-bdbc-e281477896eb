import React from 'react';
import { Box, Step, StepLabel, Stepper, Typography } from '@mui/material';
import { CheckIcon } from '@phosphor-icons/react';
import { Design } from '../../../../helpers/Design';

type ScheduleMoveLegStep = {
  label: string;
  title: string;
  subtitle: string;
};

export const SchedulingHeader = ({
  icon,
  showStepper = false,
  activeStep = 0,
  steps
}: {
  icon: React.ReactNode;
  showStepper?: boolean;
  activeStep?: number;
  steps: ScheduleMoveLegStep[];
}) => {
  const currentStep = steps[activeStep];
  const isLastStep = activeStep === steps.length - 1;

  return (
    <Box sx={styles.moveHeader}>
      {showStepper && (
        <Stepper activeStep={activeStep} alternativeLabel {...styles.stepperLabel}>
          {steps.map((step) => (
            <Step key={step.label}>
              <StepLabel>{step.label}</StepLabel>
            </Step>
          ))}
        </Stepper>
      )}
      <Box {...styles.moveTitle}>
        {isLastStep ? <CheckIcon size={40} color={Design.Alias.Color.secondary500} /> : icon}
        <Typography variant="h2">{currentStep.title}</Typography>
      </Box>
      <Typography variant="subtitle1">{currentStep.subtitle}</Typography>
    </Box>
  );
};

const styles = {
  moveHeader: {
    display: 'flex',
    gap: Design.Primitives.Spacing.xxs,
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    flexDirection: 'column',
    alignSelf: 'stretch'
  },
  stepperLabel: { sx: { width: '100%', mb: 2 } },
  moveTitle: {
    display: 'flex',
    gap: Design.Primitives.Spacing.xxs,
    alignItems: 'center',
    justifyContent: 'center'
  }
};
