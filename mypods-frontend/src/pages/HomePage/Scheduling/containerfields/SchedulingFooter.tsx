import React from 'react';
import { Box, CircularProgress } from '@mui/material';
import { Button } from 'pods-component-library';
import { useTranslation } from 'react-i18next';
import { Design } from '../../../../helpers/Design';
import { TranslationKeys } from '../../../../locales/TranslationKeys';

interface SchedulingFooterProps {
  selectedDate: Date | null;
  setSelectedDate: (date: Date | null) => void;
  isDateSelected: boolean;
  onClose: () => void;
  handleBack: () => void;
  setIsDateSelected: (isDateSelected: boolean) => void;
  isSaving: boolean;
  handleSave: () => void;
  handleNext: () => void;
}

const Tx = TranslationKeys.CommonComponents;

export const SchedulingFooter: React.FC<SchedulingFooterProps> = ({
  selectedDate,
  setSelectedDate,
  isDateSelected,
  onClose,
  handleBack,
  setIsDateSelected,
  isSaving,
  handleSave,
  handleNext
}) => {
  const { t: translate } = useTranslation();

  return (
    <Box sx={styles.moveFooter}>
      <Button
        variant="outlined"
        onPress={() => {
          if (!isDateSelected) {
            onClose();
          } else {
            handleBack();
          }
          setIsDateSelected(false);
          setSelectedDate(null);
        }}
        buttonSize="large"
        color="secondary"
        isDisabled={isSaving}>
        {translate(!isDateSelected ? Tx.CANCEL_BUTTON : Tx.BACK_BUTTON)}
      </Button>
      <Button
        variant="animated"
        isDisabled={!isDateSelected && !selectedDate}
        buttonSize="large"
        color="secondary"
        onPress={isDateSelected ? handleSave : handleNext}>
        {(() => {
          if (isSaving) {
            return (
              <div>
                <CircularProgress size={20} color="inherit" sx={{ mr: 1 }} /> Saving...
              </div>
            );
          }
          if (isDateSelected) {
            return translate(Tx.CONFIRM_BUTTON);
          }
          return translate(Tx.NEXT_BUTTON);
        })()}
      </Button>
    </Box>
  );
};

const styles = {
  moveFooter: {
    display: 'flex',
    padding: Design.Primitives.Spacing.sm,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    gap: Design.Primitives.Spacing.sm,
    position: 'absolute',
    bottom: 0,
    background: Design.Alias.Color.neutral100,
    boxShadow: '0px 0px 8px 0px var(--color-neutral-neutral300, #CBCBCB)',
    width: '100%',
    boxSizing: 'border-box'
  }
};
