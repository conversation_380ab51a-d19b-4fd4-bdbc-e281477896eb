import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState
} from 'react';
import { FormControl, Grid, InputAdornment, InputLabel, OutlinedInput } from '@mui/material';
import { MapPinLineIcon } from '@phosphor-icons/react/dist/ssr/MapPinLine';
import { Design } from '../../../../helpers/Design';
import { useMapsLibrary } from '@vis.gl/react-google-maps';
import {
  getAddressFromPlace,
  PlaceAddress
} from '../../../../helpers/googlePlace/getAddressFromPlace';
import { formatServiceAddress } from '../../../../networkRequests/responseEntities/CustomerEntities';
import { ServiceAddress } from '../../../../domain/OrderEntities';
import { PodsAlert, PodsAlertIcon, PodsAlertType } from '../../../../components/alert/PodsAlert';
import { useTranslation } from 'react-i18next';
import { TranslationKeys } from '../../../../locales/TranslationKeys';

export type SchedulingAddressHandle = {
  validate: () => boolean;
  getAddressData: () => ServiceAddress;
};

interface SchedulingAddressProps {
  originalAddress: string | null;
  validateAddress: (address: ServiceAddress) => Promise<boolean>;
}

export const SchedulingAddress = forwardRef<SchedulingAddressHandle, SchedulingAddressProps>(
  ({ originalAddress, validateAddress }: SchedulingAddressProps, ref) => {
    const [addressBoxValue, setAddressBoxValue] = useState('');
    const [address, setAddress] = useState<ServiceAddress>({
      address1: '',
      city: '',
      state: '',
      postalCode: '',
      country: ''
    });
    const [placeAutocomplete, setPlaceAutocomplete] =
      useState<google.maps.places.Autocomplete | null>(null);
    const [validAddress, setValidAddress] = useState<boolean>(true);
    const inputRef = useRef<HTMLInputElement>(null);
    const placesLibrary = useMapsLibrary('places');
    const { t: translate } = useTranslation();
    useImperativeHandle(ref, () => ({
      validate: () => true,
      getAddressData: () => address
    }));

    const getValueFromPlaceAddress = (placeAddress: PlaceAddress) => {
      return {
        ...placeAddress,
        address1: placeAddress.streetAddress
      } as ServiceAddress;
    };

    const handleGooglePlaceChanged = useCallback(async () => {
      if (!placeAutocomplete) return;
      const placeAddress = getAddressFromPlace(placeAutocomplete.getPlace());
      const serviceAddress = getValueFromPlaceAddress(placeAddress);
      setAddress(serviceAddress);
      setAddressBoxValue(formatServiceAddress(serviceAddress));
      setValidAddress(await validateAddress(serviceAddress));
    }, [placeAutocomplete]);

    useEffect(() => {
      if (!placesLibrary || !inputRef.current) return;

      const options = {
        componentRestrictions: { country: ['us', 'ca'] },
        fields: ['address_components'],
        types: ['address']
      };
      setPlaceAutocomplete(new placesLibrary.Autocomplete(inputRef.current, options));
    }, [placesLibrary]);

    useEffect(() => {
      if (!placeAutocomplete) return;
      placeAutocomplete.addListener('place_changed', handleGooglePlaceChanged);
    }, [placeAutocomplete]);

    useEffect(() => {
      setAddressBoxValue(originalAddress ? originalAddress : '');
    }, [originalAddress]);
    const addressTranslation = translate(
      TranslationKeys.HomePage.MoveLegs.Scheduling.DropOffContainer.ADDRESS_LABEL
    );
    return (
      <>
        <FormControl sx={{ width: '100%' }}>
          <InputLabel htmlFor={addressTranslation}>{addressTranslation}</InputLabel>
          <OutlinedInput
            id={addressTranslation}
            label={addressTranslation}
            startAdornment={
              <InputAdornment position="start">
                <MapPinLineIcon size={24} />
              </InputAdornment>
            }
            inputRef={inputRef}
            onChange={(e) => {
              setAddressBoxValue(e.target.value);
            }}
            value={addressBoxValue}
            sx={styles.addressInput}
          />
        </FormControl>
        {!validAddress && (
          <Grid {...styles.addressAlert} data-testid="same-area-alert">
            <PodsAlert
              title={translate(
                TranslationKeys.HomePage.MoveLegs.Scheduling.SERVICE_NOT_SAME_AREA_TITLE
              )}
              description={translate(
                TranslationKeys.HomePage.MoveLegs.Scheduling.SERVICE_NOT_SAME_AREA_DESCRIPTION
              )}
              icon={PodsAlertIcon.INFO}
              alertType={PodsAlertType.ERROR}
            />
          </Grid>
        )}
      </>
    );
  }
);

const styles = {
  addressInput: {
    borderColor: Design.Alias.Color.neutral600,
    '&:hover .MuiOutlinedInput-notchedOutline': {
      borderColor: Design.Alias.Color.secondary500
    },
    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
      borderColor: Design.Alias.Color.secondary500,
      borderWidth: 2
    },
    '&:hover .MuiInputAdornment-root svg': {
      borderColor: Design.Alias.Color.secondary500
    },
    '&.Mui-focused .MuiInputAdornment-root svg': {
      color: Design.Alias.Color.secondary500
    }
  },
  addressAlert: {
    sx: {
      paddingBottom: Design.Primitives.Spacing.md
    }
  }
};
