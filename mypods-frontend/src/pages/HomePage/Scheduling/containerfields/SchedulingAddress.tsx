import React from 'react';
import { Box, IconButton, Typography } from '@mui/material';
import { MapPinLine } from '@phosphor-icons/react';
import { useTranslation } from 'react-i18next';
import { Design } from '../../../../helpers/Design';
import { TranslationKeys } from '../../../../locales/TranslationKeys';

const Tx = TranslationKeys.HomePage.MoveLegs;

interface SchedulingAddressProps {
  showLabel?: boolean;
  addressValue: string;
  title?: string;
}

export const SchedulingAddress = ({
  showLabel = true,
  addressValue,
  title
}: SchedulingAddressProps) => {
  const { t: translate } = useTranslation();

  const displayTitle = title || translate(Tx.Scheduling.PickupContainer.DETAILS_TITLE);

  return (
    <>
      <Typography variant="h6">{displayTitle}</Typography>
      <Box sx={styles.addressDetails}>
        {showLabel && (
          <Typography variant="subtitle1">
            {translate(Tx.Scheduling.PickupContainer.ADDRESS_LABEL)}
          </Typography>
        )}
        <Box sx={styles.addressDetailsItem}>
          <IconButton>
            <MapPinLine size={24} color={Design.Alias.Color.accent900} />
          </IconButton>
          <Typography variant="body1" color={Design.Alias.Color.accent900}>
            {addressValue}
          </Typography>
        </Box>
      </Box>
    </>
  );
};

const styles = {
  addressContainer: {
    display: 'flex',
    padding: Design.Primitives.Spacing.sm,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'flex-start',
    gap: Design.Primitives.Spacing.sm,
    alignSelf: 'stretch',
    border: `1px solid ${Design.Alias.Color.neutral200}`,
    borderRadius: Design.Primitives.Spacing.sm
  },
  addressDetails: {
    gap: Design.Primitives.Spacing.xxs
  },
  addressDetailsItem: {
    gap: Design.Primitives.Spacing.xxs,
    display: 'flex',
    alignItems: 'center',
    alignSelf: 'stretch'
  }
};
