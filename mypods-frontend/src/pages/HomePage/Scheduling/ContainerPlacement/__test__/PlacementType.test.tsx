import { screen, waitFor } from '@testing-library/react';
import {
  renderWithPoetProvidersAndState,
  runPendingPromises
} from '../../../../../testUtils/RenderHelpers';
import { PlacementType } from '../PlacementType';
import {
  createMoveLegAddress,
  createRefreshSessionClaims
} from '../../../../../testUtils/MyPodsFactories';
import { TranslationKeys } from '../../../../../locales/TranslationKeys';
import { mockRefreshSession } from '../../../../../../setupTests';
import userEvent from '@testing-library/user-event';

describe('PlacementType', () => {
  const ContainerPlacementKeys = TranslationKeys.HomePage.ContainerPlacement;
  const user = userEvent.setup();
  const address = createMoveLegAddress({
    address1: '1234 Test Address',
    address2: '',
    city: 'Test City',
    state: 'AZ',
    postalCode: '85281'
  });
  const mockSetPlacement = vi.fn();

  beforeEach(() => {
    mockRefreshSession.mockResolvedValue(createRefreshSessionClaims());
  });

  const render = async (placement = '') => {
    const result = renderWithPoetProvidersAndState(
      <PlacementType placement={placement} setPlacement={mockSetPlacement} address={address} />
    );
    await runPendingPromises();
    return result;
  };

  describe('should render', () => {
    beforeEach(async () => {
      await render('street');
    });

    it('title', async () => {
      expect(
        await screen.findByText(ContainerPlacementKeys.SiteTypeScreen.TITLE)
      ).toBeInTheDocument();
    });

    it('selected address', async () => {
      expect(
        await screen.findByText(
          ContainerPlacementKeys.Address.TITLE + '[1234 Test Address, Test City, AZ 85281]'
        )
      ).toBeInTheDocument();
    });

    it('types', async () => {
      expect(
        await screen.findByText(ContainerPlacementKeys.SiteTypeScreen.Buttons.DRIVEWAY)
      ).toBeInTheDocument();
      expect(
        await screen.findByText(ContainerPlacementKeys.SiteTypeScreen.Buttons.STREET)
      ).toBeInTheDocument();
      expect(
        await screen.findByText(ContainerPlacementKeys.SiteTypeScreen.Buttons.PARKING_LOT)
      ).toBeInTheDocument();
    });

    it('selected type', async () => {
      expect(
        await screen.findByRole('radio', {
          name: ContainerPlacementKeys.SiteTypeScreen.Buttons.DRIVEWAY
        })
      ).not.toBeChecked();
      expect(
        await screen.findByRole('radio', {
          name: ContainerPlacementKeys.SiteTypeScreen.Buttons.STREET
        })
      ).toBeChecked();
      expect(
        await screen.findByRole('radio', {
          name: ContainerPlacementKeys.SiteTypeScreen.Buttons.PARKING_LOT
        })
      ).not.toBeChecked();
    });
  });

  it('selecting a type calls setPlacement with the value', async () => {
    await render();

    const optionLabel = await screen.findByText(
      ContainerPlacementKeys.SiteTypeScreen.Buttons.DRIVEWAY
    );
    await waitFor(async () => {
      await user.click(optionLabel);
    });

    expect(mockSetPlacement).toHaveBeenCalledWith('driveway');
  });
});
