import React from 'react';
import { Box, FormControl, FormControlLabel, Radio, RadioGroup, Typography } from '@mui/material';
import { Design } from '../../../../helpers/Design';
import { useTranslation } from 'react-i18next';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import { MoveLegAddress } from '../../../../domain/OrderEntities';
import { formatAddress } from '../../../../networkRequests/responseEntities/CustomerEntities';

type PlacementTypeProps = {
  placement: string;
  setPlacement: (placement: string) => void;
  address: MoveLegAddress;
};

const ContainerPlacementKeys = TranslationKeys.HomePage.ContainerPlacement;

export const PlacementType = ({ placement, setPlacement, address }: PlacementTypeProps) => {
  const { t: translate } = useTranslation();
  const placementOptions = [
    { value: 'driveway', label: translate(ContainerPlacementKeys.SiteTypeScreen.Buttons.DRIVEWAY) },
    { value: 'street', label: translate(ContainerPlacementKeys.SiteTypeScreen.Buttons.STREET) },
    {
      value: 'parking_lot',
      label: translate(ContainerPlacementKeys.SiteTypeScreen.Buttons.PARKING_LOT)
    }
  ];

  return (
    <Box {...styles.container}>
      <Typography {...styles.address}>
        {translate(ContainerPlacementKeys.Address.TITLE, { address: formatAddress(address) })}
      </Typography>
      <Typography {...styles.title}>
        {translate(ContainerPlacementKeys.SiteTypeScreen.TITLE)}
      </Typography>
      <Typography {...styles.tip}>{translate(ContainerPlacementKeys.PLACEMENT_TIP)}</Typography>
      <FormControl component="fieldset">
        <RadioGroup
          aria-label="placement"
          name="placement"
          value={placement}
          onChange={(e) => {
            setPlacement(e.target.value);
          }}>
          {placementOptions.map((opt) => (
            <FormControlLabel
              key={opt.value}
              value={opt.value}
              control={<Radio />}
              label={opt.label}
              {...styles.types}
            />
          ))}
        </RadioGroup>
      </FormControl>
    </Box>
  );
};

const styles = {
  container: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'flex-start',
      gap: Design.Primitives.Spacing.sm,
      alignSelf: 'stretch'
    }
  },
  address: {
    sx: {
      display: 'flex',
      alignItems: 'center',
      width: '100%',
      marginTop: 2,
      marginBottom: 1,
      justifyContent: 'center',
      ...Design.Alias.Text.BodyUniversal.Xs
    }
  },
  tip: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.Sm
    }
  },
  title: {
    sx: {
      color: Design.Alias.Color.accent900,
      ...Design.Alias.Text.Heading.Desktop.Md
    }
  },
  types: {
    sx: {
      '& .MuiFormControlLabel-label': {
        color: Design.Alias.Color.accent900,
        ...Design.Alias.Text.BodyUniversal.Md
      }
    }
  }
};
