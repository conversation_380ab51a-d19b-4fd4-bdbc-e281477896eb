import React, { forwardRef, useImperativeHandle, useRef } from 'react';
import { SchedulingAddress, SchedulingAddressHandle } from './containerfields/SchedulingAddress';
import { useTranslation } from 'react-i18next';
import { Box, Typography } from '@mui/material';
import { formatAddress } from '../../../networkRequests/responseEntities/CustomerEntities';
import { SchedulingDatePicker } from './containerfields/SchedulingDatePicker';
import { SchedulingDescription } from './containerfields/SchedulingDescription';
import { MoveLegAddress, ServiceAddress } from '../../../domain/OrderEntities';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { Design } from '../../../helpers/Design';

const Tx = TranslationKeys.HomePage.MoveLegs;

export type DropOffDetailsStepHandle = {
  validate: () => boolean;
  getDropOffDetailsData: () => { address: ServiceAddress; date: string };
};

type DropOffDetailsStepProps = {
  dropOffAddress: MoveLegAddress;
  isDateSelected: boolean;
  setIsDatePickerOpen: (open: boolean) => void;
  isDatePickerOpen: boolean;
  validateAddress: (address: ServiceAddress) => Promise<boolean>;
};

export const DropOffDetailsStep = forwardRef<DropOffDetailsStepHandle, DropOffDetailsStepProps>(
  (
    {
      dropOffAddress,
      validateAddress,
      isDateSelected,
      isDatePickerOpen,
      setIsDatePickerOpen
    }: DropOffDetailsStepProps,
    ref
  ) => {
    const schedulingAddressRef = useRef<SchedulingAddressHandle>(null);
    const { t: translate } = useTranslation();
    const displayTitle = translate(Tx.Scheduling.DropOffContainer.DETAILS_TITLE);

    useImperativeHandle(ref, () => ({
      validate: () => true,
      getDropOffDetailsData: () => {
        return { address: schedulingAddressRef.current!.getAddressData(), date: '' };
      }
    }));

    return (
      <>
        <Box sx={styles.container}>
          <Typography variant="h6">{displayTitle}</Typography>
          <SchedulingAddress
            originalAddress={formatAddress(dropOffAddress)}
            ref={schedulingAddressRef}
            validateAddress={validateAddress}
          />
          <Box {...styles.dateContainer}>
            <SchedulingDatePicker
              isDateSelected={isDateSelected}
              setIsDatePickerOpen={setIsDatePickerOpen}
              isDatePickerOpen={isDatePickerOpen}
              label="Date"
              hidePickerWhenSelected={false}
              isDatePickerDisabled={false}
              saveButtonIsLoading={false}
            />
          </Box>
        </Box>
        <SchedulingDescription />
      </>
    );
  }
);

const styles = {
  container: {
    display: 'flex',
    padding: Design.Primitives.Spacing.sm,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'flex-start',
    gap: Design.Primitives.Spacing.sm,
    alignSelf: 'stretch',
    border: `1px solid ${Design.Alias.Color.neutral200}`,
    borderRadius: Design.Primitives.Spacing.sm
  },
  dateContainer: {
    sx: {
      width: '100%'
    }
  }
};
