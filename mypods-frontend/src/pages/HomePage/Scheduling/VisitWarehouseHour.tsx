import React from 'react';
import { ClockIcon } from '@phosphor-icons/react';
import { Box, Typography } from '@mui/material';
import { Design } from '../../../helpers/Design';
import { formatFullWarehouseHours } from '../../../helpers/dateHelpers';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { useTranslation } from 'react-i18next';

interface VisitWarehouseRowProps {
  isEditingExistingVisit: boolean;
  moveLegEta: string | undefined;
}

export const VisitWarehouseHour: React.FC<VisitWarehouseRowProps> = ({
  isEditingExistingVisit,
  moveLegEta
}) => {
  const { t: translate } = useTranslation();

  const fullWarehouseHours = formatFullWarehouseHours(moveLegEta);

  return (
    <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
      <ClockIcon size={24} color={Design.Alias.Color.neutral900} />
      <Box>
        {moveLegEta == null && (
          <Typography variant="body1" color={Design.Alias.Color.neutral700}>
            {translate(TranslationKeys.HomePage.MoveLegs.Scheduling.NO_WAREHOUSE_HOURS_FOUND)}
          </Typography>
        )}
        {fullWarehouseHours &&
          fullWarehouseHours.map((h, i) => (
            <Box key={`warehouseHours-${i}`} {...styles.timing}>
              <Typography key={`days-${i}`} variant="body1" {...styles.day}>
                {h.day}
              </Typography>
              <Typography key={`hours-${i}`} variant="body1" {...styles.hours}>
                {h.hoursRange}
              </Typography>
            </Box>
          ))}
        {!!moveLegEta && isEditingExistingVisit && (
          <Typography variant="body1" color={Design.Alias.Color.neutral700}>
            {moveLegEta}
          </Typography>
        )}
      </Box>
    </Box>
  );
};

const styles = {
  timing: {
    sx: {
      display: 'flex',
      flexDirection: 'row',
      gap: Design.Primitives.Spacing.xxs
    }
  },
  day: {
    sx: {
      fontWeight: 600,
      color: Design.Alias.Color.neutral900
    }
  },
  hours: {
    sx: {
      fontWeight: 400,
      color: Design.Alias.Color.neutral700
    }
  }
};
