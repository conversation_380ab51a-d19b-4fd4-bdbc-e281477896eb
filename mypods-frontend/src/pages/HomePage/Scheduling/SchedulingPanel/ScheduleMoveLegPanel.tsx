import React from 'react';
import { Drawer, Box, IconButton, useMediaQuery } from '@mui/material';
import { X } from '@phosphor-icons/react';
import { theme } from '../../../../PodsTheme';
import { Design } from '../../../../helpers/Design';

interface ScheduleMoveLegPanelProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
}

export const ScheduleMoveLegPanel: React.FC<ScheduleMoveLegPanelProps> = ({
  isOpen,
  onClose,
  children
}) => {
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = panelStyles(isMobile);

  return (
    <Drawer
      data-testid="schedule-move-leg-panel"
      anchor={isMobile ? 'bottom' : 'right'}
      open={isOpen}
      onClose={onClose}
      PaperProps={styles.paperProps}>
      <Box sx={styles.drawerHeader}>
        <IconButton
          onClick={() => {
            onClose();
          }}
          sx={{ padding: 0 }}>
          <X size={24} color={Design.Alias.Color.secondary900} />
        </IconButton>
      </Box>
      <Box sx={styles.moveContainer}>{children}</Box>
    </Drawer>
  );
};

const panelStyles = (isMobile: boolean) => ({
  drawerHeader: {
    display: 'flex',
    padding: Design.Primitives.Spacing.sm,
    justifyContent: 'flex-end',
    alignItems: 'center',
    gap: Design.Primitives.Spacing.md,
    alignSelf: 'stretch'
  },
  paperProps: {
    sx: {
      height: isMobile ? `calc(100vh - 36px)` : '100%',
      display: 'flex',
      width: isMobile ? '100%' : '30.625rem',
      flexDirection: 'column',
      alignItems: 'center',
      gap: Design.Primitives.Spacing.sm,
      flexShrink: 0,
      borderRadius: isMobile ? '1rem 1rem 0px 0px' : '0px'
    }
  },
  moveContainer: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: Design.Primitives.Spacing.sm,
    alignSelf: 'stretch',
    paddingX: Design.Primitives.Spacing.sm
  }
});
