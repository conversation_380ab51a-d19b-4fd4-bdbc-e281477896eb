import React from 'react';
import { <PERSON>er, Box, IconButton, useMediaQuery } from '@mui/material';
import { XIcon } from '@phosphor-icons/react';
import { theme } from '../../../../PodsTheme';
import { Design } from '../../../../helpers/Design';
import {
  SchedulingActionBar,
  SchedulingActionBarProps
} from '../containerfields/SchedulingActionBar';

interface ScheduleMoveLegPanelProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  actionProps: SchedulingActionBarProps;
}

export const ScheduleMoveLegPanel: React.FC<ScheduleMoveLegPanelProps> = ({
  isOpen,
  onClose,
  children,
  actionProps
}) => {
  const {
    isDateSelected,
    handleBack,
    handleNext,
    handleSave,
    isSaving,
    currentStep,
    isLastStep,
    onCancel
  } = actionProps;
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = panelStyles(isMobile);
  // NOTE: aria-hidden={!isOpen} is called prevent bug where the drawer closes before the animation ends on sub components
  // see: https://github.com/mui/material-ui/issues/43106

  return (
    <Drawer
      data-testid="schedule-move-leg-panel"
      closeAfterTransition={true}
      anchor={isMobile ? 'bottom' : 'right'}
      open={isOpen}
      aria-hidden={!isOpen}
      onClose={onClose}
      PaperProps={styles.paperProps}
      sx={{ zIndex: Design.Alias.ZIndex.rightSchedulingDrawer }}>
      <Box {...styles.drawerHeader}>
        <IconButton
          onClick={() => {
            actionProps.onCancel();
          }}
          sx={{ padding: 0 }}>
          <XIcon size={24} color={Design.Alias.Color.secondary900} />
        </IconButton>
      </Box>
      <Box {...styles.moveContainer}>{children}</Box>
      <SchedulingActionBar
        isDateSelected={isDateSelected}
        onCancel={onCancel}
        handleBack={handleBack}
        isSaving={isSaving}
        handleSave={handleSave}
        handleNext={handleNext}
        currentStep={currentStep}
        isLastStep={isLastStep}
      />
    </Drawer>
  );
};

const panelStyles = (isMobile: boolean) => ({
  drawerHeader: {
    sx: {
      display: 'flex',
      padding: Design.Primitives.Spacing.sm,
      justifyContent: 'flex-end',
      alignItems: 'center',
      alignSelf: 'stretch',
      gap: Design.Primitives.Spacing.md,
      boxShadow: '0px 0px 6px 0px rgba(0, 0, 0, 0.10)'
    }
  },
  paperProps: {
    sx: {
      display: 'flex',
      width: isMobile ? '100%' : '30.625rem',
      height: isMobile ? 'calc(100% - 36px)' : '100%',
      flexDirection: 'column',
      alignItems: 'center',
      gap: Design.Primitives.Spacing.sm,
      borderRadius: isMobile ? '1rem 1rem 0px 0px' : '0px',
      overflow: 'hidden'
    }
  },
  moveContainer: {
    sx: {
      display: 'flex',
      flex: 1,
      flexDirection: 'column',
      alignItems: 'center',
      alignSelf: 'stretch',
      position: 'relative',
      paddingX: Design.Primitives.Spacing.sm,
      overflow: 'hidden'
    }
  }
});

export const sharedBodyContainerStyles = (isMobile: boolean) => ({
  display: 'flex',
  flexDirection: 'column',
  maxHeight: isMobile ? 'calc(100% - 80px)' : '100%', // 60px is the height of the footer
  overflow: isMobile ? 'scroll' : 'auto',
  gap: Design.Primitives.Spacing.sm,
  width: '100%'
});
