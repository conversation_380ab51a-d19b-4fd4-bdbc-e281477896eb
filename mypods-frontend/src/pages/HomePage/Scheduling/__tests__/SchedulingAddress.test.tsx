import { screen, waitFor } from '@testing-library/react';
import {
  renderWithPoetProvidersAndState,
  runPendingPromises
} from '../../../../testUtils/RenderHelpers';
import { SchedulingAddress } from '../containerfields/SchedulingAddress';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import { mockAddListener, mockRefreshSession } from '../../../../../setupTests';
import { createRefreshSessionClaims } from '../../../../testUtils/MyPodsFactories';

describe('SchedulingAddress', () => {
  const validateAddress = vi.fn();
  const originalAddress = '1234 Test Address, Test City, AZ 85281';
  const dropOffTranslationKeys = TranslationKeys.HomePage.MoveLegs.Scheduling.DropOffContainer;

  const render = async () => {
    const result = renderWithPoetProvidersAndState(
      <SchedulingAddress originalAddress={originalAddress} validateAddress={validateAddress} />
    );
    await runPendingPromises();
    return result;
  };

  beforeEach(() => {
    mockRefreshSession.mockResolvedValue(createRefreshSessionClaims());
  });

  it('should render the given address', async () => {
    await render();

    expect(await screen.findAllByText(dropOffTranslationKeys.ADDRESS_LABEL)).toHaveLength(2);
    expect(await screen.findByDisplayValue(originalAddress)).toBeInTheDocument();
    expect(
      screen.queryByText(TranslationKeys.HomePage.MoveLegs.Scheduling.SERVICE_NOT_SAME_AREA_TITLE)
    ).not.toBeInTheDocument();
  });

  describe('when handleGooglePlaceChanged is called and validateAddress returns', () => {
    it('false should show error message', async () => {
      validateAddress.mockResolvedValue(false);
      await render();

      const handleGooglePlaceChanged = mockAddListener.mock.calls[0][1];
      await waitFor(async () => {
        handleGooglePlaceChanged();
      });

      expect(
        await screen.findByText(
          TranslationKeys.HomePage.MoveLegs.Scheduling.SERVICE_NOT_SAME_AREA_TITLE
        )
      ).toBeInTheDocument();
    });

    it('true should not show error message', async () => {
      validateAddress.mockResolvedValue(true);
      await render();

      const handleGooglePlaceChanged = mockAddListener.mock.calls[0][1];
      await waitFor(async () => {
        handleGooglePlaceChanged();
      });

      expect(
        screen.queryByText(TranslationKeys.HomePage.MoveLegs.Scheduling.SERVICE_NOT_SAME_AREA_TITLE)
      ).not.toBeInTheDocument();
    });
  });
});
