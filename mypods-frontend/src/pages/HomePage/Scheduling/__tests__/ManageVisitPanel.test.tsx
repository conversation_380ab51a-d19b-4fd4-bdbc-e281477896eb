import { cleanup, getSuggestedQuery, render, screen, waitFor } from '@testing-library/react';
import {
  Container,
  MoveLeg,
  MoveLegType,
  MoveLegTypeEnum,
  Order
} from '../../../../domain/OrderEntities';
import {
  createMoveLeg,
  createMoveLegAddress,
  createMoveLegUpdateResponse,
  createOrder,
  createUseFeatureFlagResult,
  getThirtyDaysOfAvailability
} from '../../../../testUtils/MyPodsFactories';
import { testQueryClient } from '../../../../testUtils/RenderHelpers';
import { ManagePickupPanel } from '../ManagePickupPanel';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import { formatAddress } from '../../../../networkRequests/responseEntities/CustomerEntities';
import { it } from 'vitest';
import { addDays, addYears, subDays } from 'date-fns';
import userEvent from '@testing-library/user-event';
import { moveLegActions, moveLegViews as views } from '../../__tests__/MoveLegViews';
import {
  mockedUseFeatureFlags,
  mockGetContainerAvailability,
  mockUpdateMoveLeg
} from '../../../../../setupTests';
import { formatDate } from '../../../../helpers/dateHelpers';
import { ContainerAvailabilityResponse } from '../../../../networkRequests/responseEntities/AvailabilityAPIEntities';
import { MoveLegProvider } from '../../container/moveleg/MoveLegContext';
import { QueryClientProvider } from '@tanstack/react-query';
import { Stack, WhichStackContext } from '../../../../context/WhichStackContext';
import { ManageVisitPanel } from '../ManageVisitPanel';

const Tx = TranslationKeys.HomePage.MoveLegs.Scheduling;

describe('ManageVisitPanel', () => {
  let baseOrder: Order;
  let baseContainer: Container;
  let unscheduledMoveLeg: MoveLeg;
  let futureMoveLeg: MoveLeg;
  const user = userEvent.setup({
    delay: null
  }); // Fixes an issue with react test timing out
  const actions = moveLegActions(user);
  const mockOnClose = vi.fn();
  const mockOnSave = vi.fn(() => Promise.resolve());

  const renderPanel = async (
    moveLeg: MoveLeg = createMoveLeg(),
    onClose: () => void = mockOnClose
  ) => {
    return render(
      <WhichStackContext.Provider value={Stack.POET}>
        <QueryClientProvider client={testQueryClient()}>
          <MoveLegProvider moveLeg={moveLeg} lastMoveLegId={''} isLastRenderedMoveLeg={false}>
            <ManageVisitPanel isOpen={true} onClose={onClose} onSave={mockOnSave} />
          </MoveLegProvider>
        </QueryClientProvider>
      </WhichStackContext.Provider>
    );
  };

  describe('General Behavior', () => {
    var moveLeg: MoveLeg;
    let date = addDays(new Date(), 2);
    const scheduledMoveLeg = createMoveLeg({
      moveLegType: 'VISIT_CONTAINER',
      containerVisitDate: date,
      scheduledDate: date,
      isSchedulableOnline: true
    });

    beforeEach(() => {
      moveLeg = createMoveLeg({
        moveLegType: 'VISIT_CONTAINER',
        isSchedulableOnline: true
      });
    });
    it('should display the correct title for %s', async () => {
      await renderPanel(moveLeg);
      expect(
        screen.getByText(`homePage.moveLegs.scheduling.visitContainer.title.step1`)
      ).toBeInTheDocument();
    });

    it('should display the correct subtitle', async () => {
      await renderPanel(moveLeg);
      expect(screen.getByText(Tx.VisitContainer.SUBTITLE)).toBeInTheDocument();
    });

    it("should be dismissed when the drawer's cancel button is clicked", async () => {
      await renderPanel();
      await actions.clickCancel();

      expect(mockOnClose).toHaveBeenCalled();
    });
  });

  describe('Address', () => {
    it('should display the correct address', async () => {
      const moveLeg = createMoveLeg({
        moveLegType: 'VISIT_CONTAINER'
      });
      await renderPanel(moveLeg);
      expect(screen.getByText(formatAddress(moveLeg.displayAddress))).toBeInTheDocument();
    });
  });

  describe('Date Scheduling', () => {
    let scheduledContainerVisitMoveLeg: MoveLeg;
    const todayDate = new Date('2024-01-01');
    const futureDate = addDays(todayDate, 7);

    beforeEach(() => {
      vi.useFakeTimers({ now: todayDate });
      const thirtyDays = getThirtyDaysOfAvailability(todayDate);
      const response: ContainerAvailabilityResponse = {
        eightFootAvailability: thirtyDays,
        twelveFootAvailability: thirtyDays,
        sixteenFootAvailability: thirtyDays
      };

      mockGetContainerAvailability
        .mockResolvedValue(response)
        .mockName('PoetContainerAvailability');
      mockUpdateMoveLeg.mockResolvedValue(createMoveLegUpdateResponse({ quoteId: undefined }));
      mockedUseFeatureFlags.useFeatureFlags.mockImplementation(() =>
        createUseFeatureFlagResult({
          isOrderModEnabled: () => true,
          isRedesignedSchedulingEnabled: () => true
        })
      );

      baseOrder = createOrder();
      baseContainer = baseOrder.containers[0];
      unscheduledMoveLeg = createMoveLeg({
        moveLegType: 'VISIT_CONTAINER',
        scheduledDate: undefined,
        firstAvailableDate: addDays(todayDate, 1),
        lastAvailableDate: addYears(todayDate, 1)
      });

      futureMoveLeg = createMoveLeg({
        moveLegType: 'VISIT_CONTAINER',
        scheduledDate: futureDate,
        containerVisitDate: futureDate,
        firstAvailableDate: addDays(futureDate, 1),
        lastAvailableDate: addYears(futureDate, 1),
        isSchedulableOnline: true
      });

      scheduledContainerVisitMoveLeg = createMoveLeg({
        ...futureMoveLeg,
        moveLegType: MoveLegTypeEnum.MOVE,
        containerVisitDate: futureDate
      });
    });

    afterEach(() => {
      vi.useRealTimers();
      vi.clearAllMocks();
      cleanup();
    });

    it('should display the date picker ', async () => {
      const moveLeg = createMoveLeg({
        moveLegType: 'VISIT_CONTAINER',
        scheduledDate: undefined
      });

      await renderPanel(moveLeg);
      expect(views.dateInput()).toBeInTheDocument();
    });

    it('should not allow editing when a move leg is scheduled 24 hours from now or sooner,', async () => {
      const tomorrowMoveLeg = createMoveLeg({
        ...futureMoveLeg,
        scheduledDate: addDays(new Date(), 1)
      });
      await renderPanel(tomorrowMoveLeg);

      expect(views.editButton()).not.toBeInTheDocument();
    });

    it(async () => {
      const moveLeg = createMoveLeg({
        ...futureMoveLeg,
        moveLegType: 'VISIT_CONTAINER',
        firstAvailableDate: subDays(futureDate, 7),
        lastAvailableDate: addYears(futureDate, 1)
      });

      await renderPanel(moveLeg);
      await actions.clickSchedule();
      await actions.enterDate(formatDate(futureDate, 'MM/dd/yyyy'));
      expect(
        screen.getByText(`homePage.moveLegs.scheduling.visitContainer.title.step1`)
      ).toBeInTheDocument();

      expect(views.nextButton()).toBeInTheDocument();
      expect(views.nextButton()).not.toBeDisabled();
      await actions.clickNext();

      expect(
        screen.getByText(`homePage.moveLegs.scheduling.visitContainer.title.step2`)
      ).toBeInTheDocument();
    });

    it('should navigate to manage visit screen when the back button is clicked for a %s leg', async () => {
      const moveLeg = createMoveLeg({
        ...futureMoveLeg,
        moveLegType: 'VISIT_CONTAINER'
      });

      await renderPanel(moveLeg);
      await actions.clickSchedule();
      await actions.enterDate(formatDate(futureDate, 'MM/dd/yyyy'));
      await actions.clickNext();
      await actions.clickBack();

      expect(
        screen.getByText(`homePage.moveLegs.scheduling.visitContainer.title.step1`)
      ).toBeInTheDocument();
    });

    // TODO: these are tests for existing behavior,
    // confirm with design whether these are expected.
    describe('saving changes to the date', async () => {
      it.skip('should disable the cancel button while a schedule change is saving', async () => {
        mockUpdateMoveLeg.mockImplementation(() => new Promise(() => {}));
        await renderPanel(unscheduledMoveLeg);

        await actions.clickSchedule();
        await actions.enterDate(formatDate(futureDate, 'MM/dd/yyyy'));
        await actions.clickNext();
        // TODO:
        expect(views.cancelButton()).toBeDisabled();
      });
    });

    describe('when the date is already scheduled', () => {
      it('should allow user to reschedule a scheduled visit', async () => {
        await renderPanel(futureMoveLeg);
        const tenDaysInFuture = formatDate(addDays(futureDate, 2), 'MM/dd/yyyy');

        await actions.clickEditButton();
        waitFor(async () => {
          await userEvent.type(views.dateInput()!, tenDaysInFuture);
        });

        await actions.clickNext();
        expect(
          screen.getByText(`homePage.moveLegs.scheduling.visitContainer.title.step2`)
        ).toBeInTheDocument();

        await actions.clickConfirm();
        expect(mockOnSave).toHaveBeenCalled();
      });
    });
  });
});
