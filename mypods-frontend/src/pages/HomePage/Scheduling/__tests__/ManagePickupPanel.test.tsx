import { cleanup, render, screen } from '@testing-library/react';
import {
  Container,
  MoveLeg,
  MoveLegType,
  MoveLegTypeEnum,
  Order
} from '../../../../domain/OrderEntities';
import {
  createMoveLeg,
  createMoveLegAddress,
  createMoveLegUpdateResponse,
  createOrder,
  createUseFeatureFlagResult,
  getThirtyDaysOfAvailability
} from '../../../../testUtils/MyPodsFactories';
import { testQueryClient } from '../../../../testUtils/RenderHelpers';
import { ManagePickupPanel } from '../ManagePickupPanel';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import { formatAddress } from '../../../../networkRequests/responseEntities/CustomerEntities';
import { it } from 'vitest';
import { addDays, addYears, subDays } from 'date-fns';
import userEvent from '@testing-library/user-event';
import { moveLegActions, moveLegViews as views } from '../../__tests__/MoveLegViews';
import {
  mockedUseFeatureFlags,
  mockGetContainerAvailability,
  mockUpdateMoveLeg
} from '../../../../../setupTests';
import { formatDate } from '../../../../helpers/dateHelpers';
import { ContainerAvailabilityResponse } from '../../../../networkRequests/responseEntities/AvailabilityAPIEntities';
import { MoveLegProvider } from '../../container/moveleg/MoveLegContext';
import { QueryClientProvider } from '@tanstack/react-query';
import { Stack, WhichStackContext } from '../../../../context/WhichStackContext';

const Tx = TranslationKeys.HomePage.MoveLegs.Scheduling;

describe('ManagePickupPanel', () => {
  let baseOrder: Order;
  let baseContainer: Container;
  let unscheduledMoveLeg: MoveLeg;
  let futureMoveLeg: MoveLeg;
  const user = userEvent.setup({
    delay: null
  }); // Fixes an issue with react test timing out
  const actions = moveLegActions(user);
  const mockOnClose = vi.fn();
  const mockOnSave = vi.fn(() => Promise.resolve());

  const renderPanel = async (
    moveLeg: MoveLeg = createMoveLeg(),
    onClose: () => void = mockOnClose
  ) => {
    return render(
      <WhichStackContext.Provider value={Stack.POET}>
        <QueryClientProvider client={testQueryClient()}>
          <MoveLegProvider moveLeg={moveLeg} lastMoveLegId={''} isLastRenderedMoveLeg={false}>
            <ManagePickupPanel isOpen={true} onClose={onClose} onSave={mockOnSave} />
          </MoveLegProvider>
        </QueryClientProvider>
      </WhichStackContext.Provider>
    );
  };

  describe('General Behavior', () => {
    test.each([
      ['PICKUP', 'pickup'],
      ['FINAL_PICKUP', 'finalPickup'],
      ['INITIAL_DELIVERY', 'initialDelivery']
    ])('should display the correct title for %s', async (moveLegName, translationContext) => {
      const moveLeg = createMoveLeg({
        moveLegType: moveLegName as MoveLegType
      });
      await renderPanel(moveLeg);
      expect(
        screen.getByText(
          `homePage.moveLegs.scheduling.pickupContainer.title.step1[${translationContext}]`
        )
      ).toBeInTheDocument();
    });

    test.each(['PICKUP', 'FINAL_PICKUP', 'INITIAL_DELIVERY'])(
      'should display the correct subtitle',
      async (moveLegName) => {
        const moveLeg = createMoveLeg({
          moveLegType: moveLegName as MoveLegType
        });
        await renderPanel(moveLeg);

        expect(screen.getByText(Tx.PickupContainer.SUBTITLE)).toBeInTheDocument();
      }
    );

    it('should be dismissed when cancel button is clicked', async () => {
      await renderPanel();
      await actions.clickCancel();

      expect(mockOnClose).toHaveBeenCalled();
    });
  });

  describe('Address', () => {
    it('should display the correct address', async () => {
      const moveLeg = createMoveLeg({
        moveLegType: 'PICKUP'
      });
      await renderPanel(moveLeg);
      expect(screen.getByText(formatAddress(moveLeg.displayAddress))).toBeInTheDocument();
    });
  });

  describe('Date Scheduling', () => {
    let scheduledContainerVisitMoveLeg: MoveLeg;
    const todayDate = new Date('2024-01-01');
    const futureDate = addDays(todayDate, 7);

    beforeEach(() => {
      vi.useFakeTimers({ now: todayDate });
      const thirtyDays = getThirtyDaysOfAvailability(todayDate);
      const response: ContainerAvailabilityResponse = {
        eightFootAvailability: thirtyDays,
        twelveFootAvailability: thirtyDays,
        sixteenFootAvailability: thirtyDays
      };

      mockGetContainerAvailability
        .mockResolvedValue(response)
        .mockName('PoetContainerAvailability');
      mockUpdateMoveLeg.mockResolvedValue(createMoveLegUpdateResponse({ quoteId: undefined }));
      mockedUseFeatureFlags.useFeatureFlags.mockImplementation(() =>
        createUseFeatureFlagResult({
          isOrderModEnabled: () => true,
          isRedesignedSchedulingEnabled: () => true
        })
      );

      baseOrder = createOrder();
      baseContainer = baseOrder.containers[0];
      unscheduledMoveLeg = createMoveLeg({
        moveLegType: 'FINAL_PICKUP',
        scheduledDate: undefined,
        firstAvailableDate: addDays(todayDate, 1),
        lastAvailableDate: addYears(todayDate, 1)
      });

      futureMoveLeg = createMoveLeg({
        moveLegType: 'FINAL_PICKUP',
        scheduledDate: futureDate,
        firstAvailableDate: addDays(futureDate, 1),
        lastAvailableDate: addYears(futureDate, 1)
      });

      scheduledContainerVisitMoveLeg = createMoveLeg({
        ...futureMoveLeg,
        moveLegType: MoveLegTypeEnum.VISIT_CONTAINER,
        containerVisitDate: futureDate
      });
    });

    afterEach(() => {
      vi.useRealTimers();
      vi.clearAllMocks();
      cleanup();
    });

    it('should display the date picker ', async () => {
      const moveLeg = createMoveLeg({
        moveLegType: 'PICKUP',
        scheduledDate: undefined
      });

      await renderPanel(moveLeg);
      expect(views.dateInput()).toBeInTheDocument();
    });

    it('should not allow editing when a move leg scheduled 24 hours from now or sooner,', async () => {
      const tomorrowMoveLeg = createMoveLeg({
        ...futureMoveLeg,
        scheduledDate: addDays(new Date(), 1)
      });
      await renderPanel(tomorrowMoveLeg);

      expect(views.editButton()).not.toBeInTheDocument();
    });

    test.each([
      ['PICKUP', 'pickup'],
      ['FINAL_PICKUP', 'finalPickup'],
      ['INITIAL_DELIVERY', 'initialDelivery']
    ])(
      'should navigate to the confirmation screen when the next button is clicked for the %s leg',
      async (moveLegName, translationContext) => {
        const moveLeg = createMoveLeg({
          ...futureMoveLeg,
          moveLegType: moveLegName as MoveLegType,
          firstAvailableDate: subDays(futureDate, 7),
          lastAvailableDate: addYears(futureDate, 1)
        });

        await renderPanel(moveLeg);
        await actions.clickSchedule();
        await actions.enterDate(formatDate(futureDate, 'MM/dd/yyyy'));
        expect(
          screen.getByText(
            `homePage.moveLegs.scheduling.pickupContainer.title.step1[${translationContext}]`
          )
        ).toBeInTheDocument();

        expect(views.nextButton()).toBeInTheDocument();
        expect(views.nextButton()).not.toBeDisabled();
        await actions.clickNext();

        expect(
          screen.getByText(
            `homePage.moveLegs.scheduling.pickupContainer.title.step2[${translationContext}]`
          )
        ).toBeInTheDocument();
      }
    );

    test.each([
      ['PICKUP', 'pickup'],
      ['FINAL_PICKUP', 'finalPickup'],
      ['INITIAL_DELIVERY', 'initialDelivery']
    ])(
      'should navigate to manage pickup screen when the back button is clicked for a %s leg',
      async (moveLegName, translationContext) => {
        const moveLeg = createMoveLeg({
          ...futureMoveLeg,
          moveLegType: moveLegName as MoveLegType
        });

        await renderPanel(moveLeg);
        await actions.clickSchedule();
        await actions.enterDate(formatDate(futureDate, 'MM/dd/yyyy'));
        await actions.clickNext();
        await actions.clickBack();

        expect(
          screen.getByText(
            `homePage.moveLegs.scheduling.pickupContainer.title.step1[${translationContext}]`
          )
        ).toBeInTheDocument();
      }
    );

    // TODO: these are tests for existing behavior,
    // confirm with design whether these are expected.
    describe.skip('saving changes to the date', async () => {
      it('should disable the cancel button while a schedule change is saving', async () => {
        mockUpdateMoveLeg.mockImplementation(() => new Promise(() => {}));
        await renderPanel(unscheduledMoveLeg);

        await actions.clickSchedule();
        await actions.enterDate(formatDate(futureDate, 'MM/dd/yyyy'));
        await actions.clickNext();
        mockUpdateMoveLeg.mockImplementation(() => {
          throw new Promise(() => {});
        });

        expect(views.cancelButton()).toBeDisabled();
      });

      it('should disable the date picker while a schedule change is saving', async () => {
        mockUpdateMoveLeg.mockImplementation(() => new Promise(() => {}));
        await renderPanel(unscheduledMoveLeg);

        await actions.clickSchedule();
        await actions.enterDate(formatDate(futureDate, 'MM/dd/yyyy'));
        await actions.clickNext();

        expect(views.dateInput()).toBeDisabled();
      });
    });

    describe.skip('when the date is already scheduled', () => {
      it('should allow user to reschedule a scheduled move leg', async () => {
        await renderPanel(futureMoveLeg);
        const tenDaysInFuture = formatDate(addDays(futureDate, 2), 'MM/dd/yyyy');

        await actions.clickEditButton();
        await actions.enterDate(tenDaysInFuture);
        await actions.clickNext(); //submit
        expect(mockOnSave).toHaveBeenCalled();
      });
    });
  });
});
