import React, { useContext, useState } from 'react';
import { Drawer, Box, IconButton, Typography } from '@mui/material';
import { X, Clock, Warehouse } from '@phosphor-icons/react';
import { useTranslation } from 'react-i18next';
import { Design } from '../../../helpers/Design';
import LoadingScreen from '../../../components/Loading/LoadingScreen';
import { NotificationContext } from '../../../components/notifications/NotificationContext';
import { getSuccessMessage } from '../container/scheduling/ScheduleMoveLeg';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { SchedulingHeader } from './containerfields/SchedulingHeader';
import { SchedulingFooter } from './containerfields/SchedulingFooter';
import { SchedulingDatePicker } from './containerfields/SchedulingDatePicker';
import { SchedulingAddress } from './containerfields/SchedulingAddress';
import { ScheduleMoveLegPanel } from './SchedulingPanel/ScheduleMoveLegPanel';

interface ManageVisitContainerProps {
  isOpen: boolean;
  onClose: () => void;
  moveLeg?: any;
}

const Tx = TranslationKeys.HomePage.MoveLegs;

export const ManageVisitContainer: React.FC<ManageVisitContainerProps> = ({
  isOpen,
  onClose,
  moveLeg
}) => {
  const { setNotification } = useContext(NotificationContext);
  const { t: translate } = useTranslation();
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [isDateSelected, setIsDateSelected] = useState(false);
  const [isDatePickerOpen, setIsDatePickerOpen] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  const handleNext = () => {
    if (selectedDate) {
      setIsDateSelected(true);
    }
  };

  const handleBack = () => {
    setIsDateSelected(false);
  };

  const staticHours = [
    { days: 'Mon-Sat', hours: '10-5 EST', bold: true },
    { days: 'Sun', hours: '9-6:30 PM EST', bold: false }
  ];

  const handleSave = () => {
    setIsSaving(true);
    setTimeout(() => {
      setIsSaving(false);
      onClose();
      setNotification({
        isError: false,
        message: translate(getSuccessMessage(moveLeg))
      });
      setIsDateSelected(false);
      setSelectedDate(null);
    }, 100);
  };

  return (
    <ScheduleMoveLegPanel isOpen={isOpen} onClose={onClose}>
      {isSaving ? (
        <LoadingScreen loadingText={translate(Tx.Scheduling.VisitContainer.LOADING_TEXT)} />
      ) : (
        <>
          <SchedulingHeader
            isDateSelected={isDateSelected}
            title={translate(Tx.Scheduling.VisitContainer.Title.STEP1)}
            title2={translate(Tx.Scheduling.VisitContainer.Title.STEP2)}
            subtitle={translate(Tx.Scheduling.VisitContainer.SUBTITLE)}
            icon={<Warehouse size={40} color={Design.Primitives.Color.Blue.oasis} />}
          />
          <>
            <Box sx={{ ...styles.addressContainer }}>
              <SchedulingAddress
                showLabel={isDateSelected}
                addressValue={translate(Tx.Scheduling.VisitContainer.ADDRESS_VALUE)}
                title={translate(Tx.Scheduling.VisitContainer.DETAILS_TITLE)}
              />
              {isDateSelected ? (
                <Box>
                  <Typography variant="subtitle1">
                    {translate(Tx.Scheduling.PickupContainer.DATE_LABEL)}
                  </Typography>
                  <Typography variant="body1" color={Design.Alias.Color.accent900}>
                    {selectedDate
                      ? selectedDate.toLocaleDateString('en-US', {
                          weekday: 'short',
                          month: 'short',
                          day: 'numeric',
                          year: 'numeric'
                        })
                      : ''}
                  </Typography>
                </Box>
              ) : (
                <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                  <IconButton>
                    <Clock size={24} color={Design.Alias.Color.neutral900} />
                  </IconButton>
                  <Box>
                    {staticHours.map((h, i) => (
                      <Typography
                        key={i}
                        variant="body2"
                        sx={{
                          fontWeight: h.bold ? 700 : 400,
                          color: h.bold
                            ? Design.Alias.Color.neutral900
                            : Design.Alias.Color.neutral700
                        }}>
                        {h.days} {h.hours}
                      </Typography>
                    ))}
                  </Box>
                </Box>
              )}
            </Box>
            {!isDateSelected && (
              <Box sx={{ ...styles.addressContainer, mb: 2 }}>
                <Typography variant="h6" sx={{ fontWeight: 700 }}>
                  {translate(Tx.Scheduling.VisitContainer.DATE_LABEL)}
                </Typography>
                <SchedulingDatePicker
                  isDateSelected={isDateSelected}
                  selectedDate={selectedDate}
                  isDatePickerDisabled={false}
                  saveButtonIsLoading={false}
                  setIsDatePickerOpen={setIsDatePickerOpen}
                  isDatePickerOpen={isDatePickerOpen}
                  setSelectedDate={setSelectedDate}
                  label={Tx.Scheduling.VisitContainer.DATE_LABEL}
                  hidePickerWhenSelected
                />
              </Box>
            )}
          </>
        </>
      )}
      <SchedulingFooter
        isDateSelected={isDateSelected}
        onClose={onClose}
        handleBack={handleBack}
        setIsDateSelected={setIsDateSelected}
        setSelectedDate={setSelectedDate}
        selectedDate={selectedDate}
        isSaving={isSaving}
        handleSave={handleSave}
        handleNext={handleNext}
      />
    </ScheduleMoveLegPanel>
  );
};

const styles = {
  moveHeader: {
    display: 'flex',
    gap: Design.Primitives.Spacing.xxs,
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    flexDirection: 'column',
    alignSelf: 'stretch'
  },
  moveTitle: {
    display: 'flex',
    gap: Design.Primitives.Spacing.xxs,
    alignItems: 'center',
    justifyContent: 'center'
  },
  addressContainer: {
    display: 'flex',
    padding: Design.Primitives.Spacing.sm,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'flex-start',
    gap: Design.Primitives.Spacing.sm,
    alignSelf: 'stretch',
    border: `1px solid ${Design.Alias.Color.neutral200}`,
    borderRadius: Design.Primitives.Spacing.sm
  },
  addressDetails: {
    gap: Design.Primitives.Spacing.xxs
  },
  addressDetailsItem: {
    gap: Design.Primitives.Spacing.xxs,
    display: 'flex',
    paddingLeft: Design.Primitives.Spacing.xs,
    alignItems: 'center',
    alignSelf: 'stretch'
  },
  divider: {
    width: '100%'
  },
  infoContainer: {
    display: 'flex',
    padding: Design.Primitives.Spacing.sm,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'flex-start',
    gap: Design.Primitives.Spacing.xs,
    alignSelf: 'stretch',
    border: `1px solid ${Design.Alias.Color.neutral200}`,
    borderRadius: Design.Primitives.Spacing.sm
  },
  infoDetails: {
    gap: Design.Primitives.Spacing.xxs
  },
  infoTitle: {
    fontWeight: Design.Alias.Text.BodyUniversal.SmSemi.fontWeight,
    color: Design.Alias.Color.accent900
  },
  moveFooter: {
    display: 'flex',
    padding: Design.Primitives.Spacing.sm,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    gap: Design.Primitives.Spacing.sm,
    position: 'absolute',
    bottom: 0,
    background: Design.Alias.Color.neutral100,
    boxShadow: '0px 0px 8px 0px var(--color-neutral-neutral300, #CBCBCB)',
    width: '100%',
    boxSizing: 'border-box'
  }
};
