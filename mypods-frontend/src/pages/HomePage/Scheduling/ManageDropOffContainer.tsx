import React, { useContext, useRef, useState } from 'react';
import { Box, useMediaQuery } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { Design } from '../../../helpers/Design';
import { NotificationContext } from '../../../components/notifications/NotificationContext';
import { getSuccessMessage } from '../container/scheduling/ScheduleMoveLeg';
import useMoveLegContext from '../container/moveleg/MoveLegContext';
import {
  SchedulingActionBar,
  SchedulingActionBarProps
} from './containerfields/SchedulingActionBar';
import HorizontalStepper from '../../../components/stepper/HorizontalStepper';
import { ServiceAddress } from '../../../domain/OrderEntities';
import {
  ScheduleMoveLegPanel,
  sharedBodyContainerStyles
} from './SchedulingPanel/ScheduleMoveLegPanel';
import { theme } from '../../../PodsTheme';
import { useIsSameServiceArea } from '../../../networkRequests/mutations/useIsSameServiceArea';
import { DropOffDetailsStep } from './DropOffDetailsStep';
import { PlacementStep } from './ContainerPlacement/PlacementStep';

type DropOffDetailsStepHandle = {
  validate: () => boolean;
  getDropOffDetailsData: () => { address: ServiceAddress; date: string };
};

const ConfirmStep = () => <div>Confirm Step</div>;

interface ManageDropOffContainerProps {
  isOpen: boolean;
  onClose: () => void;
}

export const ManageDropOffContainer: React.FC<ManageDropOffContainerProps> = ({
  isOpen,
  onClose
}) => {
  const dropOffDetailsRef = useRef<DropOffDetailsStepHandle>(null);
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isSameServiceAreaCall = useIsSameServiceArea();
  const {
    moveLeg,
    scheduling: {
      addressState: { isInSameServiceArea }
    }
  } = useMoveLegContext();
  const { setNotification } = useContext(NotificationContext);
  const { t: translate } = useTranslation();
  const [_selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [isDateSelected, setIsDateSelected] = useState(false);
  const [isDatePickerOpen, setIsDatePickerOpen] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const steps = ['Location', 'Placement', 'Confirm'];
  const isOnLastStep = currentStep === steps.length - 1;
  const [placement, setPlacement] = useState('');
  const dropOffAddress = moveLeg.destinationAddress;

  const handleNextStep = () => {
    setCurrentStep((prev) => Math.min(prev + 1, steps.length - 1));
  };
  const handleBackStep = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 0));
  };

  const handleSave = () => {
    setIsSaving(true);
    setTimeout(() => {
      setIsSaving(false);
      onClose();
      setNotification({
        isError: false,
        message: translate(getSuccessMessage(moveLeg))
      });
      setIsDateSelected(false);
      setSelectedDate(null);
    }, 100);
  };

  const actionProps: SchedulingActionBarProps = {
    isDateSelected,
    onCancel: onClose,
    handleBack: handleBackStep,
    isSaving: isSaving,
    handleSave: handleSave,
    handleNext: handleNextStep,
    currentStep: currentStep,
    isLastStep: isOnLastStep
  };

  const validateAddress = (address: ServiceAddress): Promise<boolean> => {
    return new Promise<boolean>((resolve) => {
      if (isInSameServiceArea(address.postalCode)) {
        const request = {
          originalAddress: moveLeg.displayAddress as ServiceAddress,
          updatedAddress: address
        };
        isSameServiceAreaCall.mutate(request, {
          onSuccess: (isValid: boolean) => {
            resolve(isValid);
          },
          onError: () => {
            resolve(false);
          }
        });
      } else {
        resolve(false);
      }
    });
  };

  return (
    <ScheduleMoveLegPanel isOpen={isOpen} onClose={onClose} actionProps={actionProps}>
      <Box sx={sharedBodyContainerStyles(isMobile)} data-testid="manage-dropoff-panel">
        <Box sx={{ width: '100%' }}>
          <HorizontalStepper steps={steps} activeStep={currentStep} connectorProgress={0.35} />
        </Box>
        <Box sx={styles.mainContentArea}>
          {currentStep === 0 && (
            <DropOffDetailsStep
              dropOffAddress={dropOffAddress}
              validateAddress={validateAddress}
              isDateSelected={isDateSelected}
              setIsDatePickerOpen={setIsDatePickerOpen}
              isDatePickerOpen={isDatePickerOpen}
              ref={dropOffDetailsRef}
            />
          )}
          {currentStep === 1 && (
            <PlacementStep
              placement={placement}
              setPlacement={setPlacement}
              address={dropOffAddress}
            />
          )}
          {currentStep === 2 && <ConfirmStep />}
        </Box>
        <SchedulingActionBar
          isDateSelected={currentStep > 0}
          onCancel={onClose}
          isSaving={isSaving}
          isLastStep={isOnLastStep}
          currentStep={currentStep}
          handleBack={handleBackStep}
          handleSave={handleSave}
          handleNext={handleNextStep}
        />
      </Box>
    </ScheduleMoveLegPanel>
  );
};

const styles = {
  mainContentArea: {
    flex: 1,
    gap: '16px',
    display: 'flex',
    flexDirection: 'column'
  },
  drawerHeader: {
    display: 'flex',
    padding: Design.Primitives.Spacing.sm,
    justifyContent: 'flex-end',
    alignItems: 'center',
    gap: Design.Primitives.Spacing.md,
    alignSelf: 'stretch'
  },
  loadingContainer: {
    paddingX: Design.Primitives.Spacing.lg,
    paddingY: Design.Primitives.Spacing.sm
  },
  moveContainer: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: Design.Primitives.Spacing.sm,
    alignSelf: 'stretch',
    paddingX: Design.Primitives.Spacing.sm
  },
  moveHeader: {
    display: 'flex',
    gap: Design.Primitives.Spacing.xxs,
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    flexDirection: 'column',
    alignSelf: 'stretch'
  },
  moveTitle: {
    display: 'flex',
    gap: Design.Primitives.Spacing.xxs,
    alignItems: 'center',
    justifyContent: 'center'
  },
  addressContainer: {
    display: 'flex',
    padding: Design.Primitives.Spacing.sm,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'flex-start',
    gap: Design.Primitives.Spacing.sm,
    alignSelf: 'stretch',
    border: `1px solid ${Design.Alias.Color.neutral200}`,
    borderRadius: Design.Primitives.Spacing.sm
  },
  addressDetails: {
    gap: Design.Primitives.Spacing.xxs
  },
  addressDetailsItem: {
    gap: Design.Primitives.Spacing.xxs,
    display: 'flex',
    paddingLeft: Design.Primitives.Spacing.xs,
    alignItems: 'center',
    alignSelf: 'stretch'
  },
  divider: {
    width: '100%'
  },
  infoContainer: {
    display: 'flex',
    padding: Design.Primitives.Spacing.sm,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'flex-start',
    gap: Design.Primitives.Spacing.xs,
    alignSelf: 'stretch',
    border: `1px solid ${Design.Alias.Color.neutral200}`,
    borderRadius: Design.Primitives.Spacing.sm
  },
  infoDetails: {
    gap: Design.Primitives.Spacing.xxs
  },
  infoTitle: {
    fontWeight: Design.Alias.Text.BodyUniversal.SmSemi.fontWeight,
    color: Design.Alias.Color.accent900
  },
  moveFooter: {
    display: 'flex',
    padding: Design.Primitives.Spacing.sm,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    gap: Design.Primitives.Spacing.sm,
    position: 'absolute',
    bottom: 0,
    background: Design.Alias.Color.neutral100,
    boxShadow: '0px 0px 8px 0px var(--color-neutral-neutral300, #CBCBCB)',
    width: '100%',
    boxSizing: 'border-box'
  }
};
