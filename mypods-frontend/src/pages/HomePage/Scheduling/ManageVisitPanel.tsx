import React, { useContext, useState } from 'react';
import { Box, Typography, useMediaQuery } from '@mui/material';
import { WarehouseIcon } from '@phosphor-icons/react';
import { useTranslation } from 'react-i18next';
import { Design } from '../../../helpers/Design';
import LoadingScreen from '../../../components/Loading/LoadingScreen';
import { NotificationContext } from '../../../components/notifications/NotificationContext';
import { getSuccessMessage } from '../container/scheduling/ScheduleMoveLeg';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { SchedulingHeader } from './containerfields/SchedulingHeader';
import { SchedulingActionBarProps } from './containerfields/SchedulingActionBar';
import { SchedulingDatePicker } from './containerfields/SchedulingDatePicker';
import {
  ScheduleMoveLegPanel,
  sharedBodyContainerStyles
} from './SchedulingPanel/ScheduleMoveLegPanel';
import { ContainerPlacement } from '../../../domain/OrderEntities';
import useMoveLegContext from '../container/moveleg/MoveLegContext';
import { formatToLocale } from '../../../helpers/dateHelpers';
import { theme } from '../../../PodsTheme';
import { formatAddress } from '../../../networkRequests/responseEntities/CustomerEntities';
import { VisitWarehouseHour } from './VisitWarehouseHour';
import { DisplayAddress } from './containerfields/DisplayAddress';
import useOrdersContext from '../../../context/OrdersContext';
import { isStaleDataError } from '../utils';
import useSingleOrderContext from '../../../context/SingleOrderContext';

export type OnMoveLegUpdateSaveProps = {
  containerPlacement: ContainerPlacement | undefined;
  onSuccess: () => void;
  onError: (error: Error | unknown) => void;
};
interface ManageVisitPanelProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (callbackProps: OnMoveLegUpdateSaveProps) => void;
}

const Tx = TranslationKeys.HomePage.MoveLegs.Scheduling;

export const ManageVisitPanel: React.FC<ManageVisitPanelProps> = ({ isOpen, onClose, onSave }) => {
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const {
    moveLegScheduling: { clearOrderIdAndQuote, stopMoveLegScheduling, setIsCancelling }
  } = useSingleOrderContext();
  const { refetch: refetchOrders, refetchOnFailure: refetchOrdersOnFailure } = useOrdersContext();
  const { setNotification } = useContext(NotificationContext);
  const { t: translate } = useTranslation();
  const { moveLeg } = useMoveLegContext();
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [isDateSelected, setIsDateSelected] = useState(false);
  const [isDatePickerOpen, setIsDatePickerOpen] = useState(false);
  const [activeStep, setActiveStep] = useState(0);
  const [isSaving, setIsSaving] = useState(false);
  const isEditingExistingVisit = moveLeg.containerVisitDate != null;

  const steps = [
    {
      label: '1',
      title: translate(Tx.VisitContainer.Title.STEP1),
      subtitle: translate(Tx.VisitContainer.SUBTITLE)
    },
    {
      label: '2',
      title: translate(Tx.VisitContainer.Title.STEP2),
      subtitle: translate(Tx.VisitContainer.SUBTITLE)
    }
  ];

  const isOnLastStep = activeStep === steps.length - 1;

  const handleNext = () => {
    if (selectedDate !== null) {
      setIsDateSelected(true);
    }
    if (!isOnLastStep) {
      setActiveStep(activeStep + 1);
    }
  };

  const handleBack = () => {
    setIsDateSelected(false);
    if (activeStep > 0) {
      setActiveStep(activeStep - 1);
    }
  };

  const handleClose = () => {
    setIsDatePickerOpen(false);
    setIsDateSelected(false);
    onClose();
  };

  const handleSuccess = () => {
    clearOrderIdAndQuote();
    stopMoveLegScheduling();
    setIsCancelling(false);
    setIsSaving(false);
    refetchOrders();
    onClose();
    setNotification({
      isError: false,
      message: translate(getSuccessMessage(moveLeg))
    });
  };

  const handleSave = () => {
    setIsSaving(true);
    onSave({
      containerPlacement: undefined,
      onSuccess: () => {
        handleSuccess();
      },
      onError: (error: any) => {
        setIsSaving(false);
        if (isStaleDataError(error)) {
          refetchOrdersOnFailure();
        } else {
          setNotification({
            message: translate(TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE),
            isError: true
          });
        }
        setSelectedDate(null);
      }
    });
    setIsDateSelected(false);
    setSelectedDate(null);
  };

  const actionProps: SchedulingActionBarProps = {
    isDateSelected,
    onCancel: handleClose,
    handleBack,
    isSaving,
    handleSave,
    handleNext,
    currentStep: activeStep,
    isLastStep: isOnLastStep
  };

  return (
    <ScheduleMoveLegPanel isOpen={isOpen} onClose={handleClose} actionProps={actionProps}>
      {isSaving ? (
        <LoadingScreen loadingText={translate(Tx.VisitContainer.LOADING_TEXT)} />
      ) : (
        <Box data-testid="manage-visit-panel" sx={sharedBodyContainerStyles(isMobile)}>
          <SchedulingHeader
            icon={<WarehouseIcon size={40} color={Design.Alias.Color.secondary500} />}
            showStepper={false}
            steps={steps}
            activeStep={activeStep}
          />
          <Box {...styles.sectionContainer}>
            <DisplayAddress
              showLabel={isDateSelected}
              addressValue={formatAddress(moveLeg.destinationAddress)}
              title={translate(Tx.VisitContainer.DETAILS_TITLE)}
            />

            {isDateSelected ? (
              <Box {...styles.sectionContainer}>
                <Typography variant="subtitle1">
                  {translate(Tx.VisitContainer.DATE_LABEL)}
                </Typography>
                <Typography variant="body1" color={Design.Alias.Color.accent900}>
                  {selectedDate ? formatToLocale(selectedDate) : ''}
                </Typography>
              </Box>
            ) : (
              <VisitWarehouseHour
                isEditingExistingVisit={isEditingExistingVisit}
                moveLegEta={moveLeg.eta}
              />
            )}
          </Box>
          {!isDateSelected && (
            <Box {...styles.sectionContainer}>
              <Typography variant="h6" sx={{ fontWeight: 700 }}>
                {translate(Tx.VisitContainer.DATE_LABEL)}
              </Typography>
              <SchedulingDatePicker
                isDateSelected={isDateSelected}
                isDatePickerDisabled={isSaving}
                saveButtonIsLoading={isSaving}
                setIsDatePickerOpen={setIsDatePickerOpen}
                isDatePickerOpen={isDatePickerOpen && isOpen}
                label={Tx.VisitContainer.DATE_LABEL}
                hidePickerWhenSelected
                scheduledDate={moveLeg.scheduledDate}
              />
            </Box>
          )}
        </Box>
      )}
    </ScheduleMoveLegPanel>
  );
};

const styles = {
  moveTitle: {
    display: 'flex',
    gap: Design.Primitives.Spacing.xxs,
    alignItems: 'center',
    justifyContent: 'center'
  },
  sectionContainer: {
    sx: {
      display: 'flex',
      padding: Design.Primitives.Spacing.sm,
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'flex-start',
      gap: Design.Primitives.Spacing.sm,
      alignSelf: 'stretch',
      border: `1px solid ${Design.Alias.Color.neutral200}`,
      borderRadius: Design.Primitives.Spacing.sm
    }
  },
  timing: {
    sx: {
      display: 'flex',
      flexDirection: 'row',
      gap: Design.Primitives.Spacing.xxs
    }
  }
};
