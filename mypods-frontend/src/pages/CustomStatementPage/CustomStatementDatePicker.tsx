import React, { useState } from 'react';
import { Grid } from '@mui/material';
import { DesktopDatePicker } from '@mui/x-date-pickers';
import { Design } from '../../helpers/Design';
import { DatepickerTextField } from '../HomePage/container/scheduling/DatepickerTextField';
import { DatePickerDay } from '../HomePage/container/scheduling/DatePickerDay';

// -- types --
interface Props {
  selectedDate: Date | null;
  label: string;
  onChange: (value: Date | null) => void;
}

// -- impls --
export const CustomStatementDatePicker: React.FC<Props> = ({
  label,
  selectedDate,
  onChange
}: Props) => {
  const styles = datePickerStyles();
  const [isOpen, setIsOpen] = useState(false);

  const showCalendar = () => {
    setIsOpen(!isOpen);
  };

  const handleSelectDate = (date: Date | null) => {
    setIsOpen(false);
    onChange(date);
  };

  return (
    <Grid item {...styles.datePickerContainer}>
      <DesktopDatePicker
        label={label}
        renderInput={(params) => <DatepickerTextField onClick={showCalendar} params={params} />}
        onClose={() => {
          setIsOpen(false);
        }}
        onChange={handleSelectDate}
        value={selectedDate}
        // Note: later when the next available day is not todays date, it could be next month, so we need to toggle the datepicker forward
        renderDay={(day, selectedDays, pickersDayProps) => (
          <DatePickerDay
            key={day.toISOString()}
            date={day}
            selectedDay={selectedDays[0]}
            pickersDayProps={pickersDayProps}
            isAvailable={() => true}
            onClick={handleSelectDate}
          />
        )}
        open={isOpen}
        views={['day']}
      />
    </Grid>
  );
};

// -- styles --
const datePickerStyles = () => ({
  datePickerContainer: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      flex: 1,
      gap: Design.Primitives.Spacing.sm,
      label: {
        color: Design.Alias.Color.neutral700,
        ...Design.Alias.Text.BodyUniversal.Md
      }
    }
  }
});

// TODO: style:where the calendar view is anchored. - eh
