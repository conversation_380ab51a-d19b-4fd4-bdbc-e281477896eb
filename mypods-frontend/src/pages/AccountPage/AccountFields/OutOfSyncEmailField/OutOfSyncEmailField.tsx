import React, { useContext, useEffect, useState } from 'react';
import { FormControlLabel, Grid, Radio, RadioGroup, TextField, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import { AccountFieldAlert, AccountFieldAlertProps } from '../AccountFieldAlert';
import { AccountField } from '../AccountField';
import { Design } from '../../../../helpers/Design';
import { CancelButton } from '../../../../components/buttons/CancelButton';
import { SaveButton } from '../../../../components/buttons/SaveButton';
import { NotificationContext } from '../../../../components/notifications/NotificationContext';
import { useEmailValidationState } from '../useEmailValidationState';
import { useGetCustomer } from '../../../../networkRequests/queries/useGetCustomer';
import { EmailChallengeOTPDialog } from '../ChallengeEmailField/EmailChallengeOTPDialog';
import { useRefreshSession } from '../../../../networkRequests/queries/useRefreshSession';
import { useAccountUpdateEvents } from '../../../../config/useAccountUpdateEvents';
import { callOnEnterPress } from '../../../../helpers/eventHelpers';

type RadioChoices = 'OKTA_USERNAME' | 'OTHER';
const Tx = TranslationKeys.AccountPage.AccountInfo.Email;

export const OutOfSyncEmailField = () => {
  const { sendStartEditingAccountDetailsEvent, sendEditAccountDetailsSuccessEvent } =
    useAccountUpdateEvents();
  const { t: translate } = useTranslation();
  const { customer } = useGetCustomer();
  const { refetch: refetchSession } = useRefreshSession();
  const { fieldProps } = useEmailValidationState();
  const { setNotification } = useContext(NotificationContext);

  const [value, setValue] = useState<string>(fieldProps.displayValue);
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [error, setError] = useState<string | undefined>();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [inputGroupAlert, setInputGroupAlert] = useState<AccountFieldAlertProps | undefined>();
  const [radioChoice, setRadioChoice] = useState<RadioChoices>('OKTA_USERNAME');
  const label = translate(fieldProps.labelKey);

  useEffect(() => {
    setError(undefined);
  }, [isEditing]);

  const inputIsInvalid = !fieldProps.isInputValid(value);
  const valueIsSalesforceEmail = value === customer.email?.address;

  const radioIsOktaUsername = () => radioChoice === 'OKTA_USERNAME';

  const setErrorIfInvalid = () => {
    if (!fieldProps.isInputValid(value)) {
      setError(translate(fieldProps.validationErrorKey));
    } else {
      setError(undefined);
    }
  };

  const handleValueChange = (newValue: string) => {
    setValue(newValue);
    setError(undefined);
    setInputGroupAlert(undefined);
  };

  const handleCancel = () => {
    setValue(fieldProps.displayValue);
    setIsEditing(false);
    setError(undefined);
    setInputGroupAlert(undefined);
  };

  const handleEditClick = () => {
    sendStartEditingAccountDetailsEvent(fieldProps.gtmDetailType);
    setIsEditing(true);
  };

  const handleSave = () => {
    setErrorIfInvalid();

    if (radioIsOktaUsername() && customer.username != null) {
      fieldProps.update(customer.username, onUpdateEmailSuccess, onUpdateEmailError);
    } else {
      if (inputIsInvalid) return;

      if (valueIsSalesforceEmail) {
        fieldProps.submitChallenge(value, onEmailChallengeSuccess, onEmailChallengeError);
        return;
      }

      fieldProps.update(value, onUpdateEmailSuccess, onUpdateEmailError);
    }
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
  };

  const handleServerError = (errorHelperText: string) => {
    if (radioIsOktaUsername()) {
      setInputGroupAlert({
        title: translate(TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE),
        message: errorHelperText
      });
      return;
    }
    setError(errorHelperText);
  };

  const handleServerAlert = (alertKey: AccountFieldAlertProps) => {
    setInputGroupAlert({
      title: alertKey.title,
      message: alertKey.message
    });
  };

  // update email request handling
  const onUpdateEmailSuccess = () => {
    setNotification({
      message: translate(fieldProps.successNotificationKey)
    });
    setIsEditing(false);
  };

  const onUpdateEmailError = (errorHelperText?: string, alertProps?: AccountFieldAlertProps) => {
    if (errorHelperText) handleServerError(errorHelperText);
    if (alertProps) handleServerAlert(alertProps);
    if (!alertProps && !errorHelperText) {
      setNotification({
        message: translate(TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE),
        isError: true
      });
    }
  };

  // email challenge request handling
  const onEmailChallengeError = (errorHelperText?: string, alertProps?: AccountFieldAlertProps) => {
    if (errorHelperText) handleServerError(errorHelperText);
    if (alertProps) handleServerAlert(alertProps);
    if (!alertProps && !errorHelperText) {
      setNotification({
        message: translate(TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE),
        isError: true
      });
    }
  };

  const onEmailChallengeSuccess = () => {
    setIsModalOpen(true);
  };

  // -- Validate match/freshness of 4-digit OTP Code --
  const handleEmailVerificationSuccess = async () => {
    setIsModalOpen(false);
    setIsEditing(false);
    setNotification({
      message: translate(fieldProps.successNotificationKey)
    });
    if (value != null) fieldProps.update(value, onUpdateEmailSuccess, onUpdateEmailError);
    sendEditAccountDetailsSuccessEvent(fieldProps.gtmDetailType);
    await refetchSession();
  };

  const handleEmailVerificationError = () => {
    setNotification({
      message: translate(TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE),
      isError: true
    });
  };

  return (
    <>
      <Grid data-testid="out-of-sync-email-field">
        {!isEditing ? (
          <AccountField
            label={label}
            value={fieldProps.displayValue}
            onEditClick={handleEditClick}
            isWarning
          />
        ) : (
          <Grid
            container
            flexDirection="column"
            gap={Design.Primitives.Spacing.xxs}
            sx={{
              paddingY: Design.Primitives.Spacing.sm,
              borderBottom: `1px solid ${Design.Alias.Color.neutral300}`
            }}>
            <Grid container flexDirection="column" gap={Design.Primitives.Spacing.xxs}>
              <Grid container flexDirection="row" justifyContent="space-between">
                <Typography
                  sx={{
                    ...Design.Alias.Text.BodyUniversal.Md,
                    color: Design.Alias.Color.neutral700
                  }}>
                  {label}
                </Typography>
              </Grid>
              <Grid container flexDirection="row" justifyContent="space-between">
                <Typography sx={{ ...Design.Alias.Text.BodyUniversal.Lg }}>
                  {fieldProps.displayValue}
                </Typography>
              </Grid>
              <Grid container flexDirection="row" justifyContent="space-between">
                <Typography
                  sx={{
                    ...Design.Alias.Text.BodyUniversal.Md,
                    color: Design.Alias.Color.neutral700
                  }}>
                  {translate(Tx.OutOfSync.LABEL)}
                </Typography>
              </Grid>
              {inputGroupAlert && (
                <AccountFieldAlert
                  title={inputGroupAlert.title}
                  message={inputGroupAlert.message}
                />
              )}
              <RadioGroup
                value={radioChoice}
                onChange={(event) => {
                  setRadioChoice(event.target.value as any);
                }}
                {...styles.emailRadioGroup}>
                <FormControlLabel
                  value="OKTA_USERNAME"
                  control={<Radio color="secondary" />}
                  label={customer.username}
                />
                <FormControlLabel
                  value="OTHER"
                  control={<Radio color="secondary" />}
                  label={translate(Tx.OutOfSync.OTHER)}
                />
              </RadioGroup>
              {radioChoice === 'OTHER' && (
                <TextField
                  autoFocus
                  fullWidth
                  color="secondary"
                  inputRef={(input) => input && input.focus()}
                  label={label}
                  value={value}
                  onBlur={setErrorIfInvalid}
                  helperText={error}
                  error={!!error}
                  onChange={(event) => {
                    handleValueChange(event.target.value);
                  }}
                  onKeyDown={callOnEnterPress(handleSave)}
                  sx={{ marginBottom: Design.Primitives.Spacing.xxs }}
                  {...{ type: 'email' }}
                />
              )}
            </Grid>
            <Grid container columnGap={Design.Primitives.Spacing.xxs} justifyContent="flex-end">
              <CancelButton onClick={handleCancel} disabled={fieldProps.isPendingUpdate} />
              <SaveButton
                onClick={handleSave}
                isLoading={fieldProps.isPendingUpdate}
                disabled={!radioIsOktaUsername() && inputIsInvalid}
              />
            </Grid>
          </Grid>
        )}
      </Grid>
      {isModalOpen && (
        <EmailChallengeOTPDialog
          isOpen={isModalOpen}
          onCloseClicked={handleModalClose}
          newEmail={value}
          onSuccess={handleEmailVerificationSuccess}
          onError={handleEmailVerificationError}
          data-testid="otp-challenge-dialog"
        />
      )}
    </>
  );
};

const styles = {
  emailRadioGroup: {
    sx: {
      paddingLeft: '9px'
    }
  }
};
