import React, { useContext, useEffect, useState } from 'react';
import { Grid, TextField } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { TextFieldProps } from '@mui/material/TextField/TextField';
import { Design } from '../../../../helpers/Design';
import { Txt } from '../../../../components/Txt';
import { CancelButton } from '../../../../components/buttons/CancelButton';
import { SaveButton } from '../../../../components/buttons/SaveButton';
import { AccountField } from '../AccountField';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import { AccountFieldAlert, AccountFieldAlertProps } from '../AccountFieldAlert';
import { NotificationContext } from '../../../../components/notifications/NotificationContext';
import { GtmAccountDetailType } from '../../../../config/google/GoogleEntities';
import { useAccountUpdateEvents } from '../../../../config/useAccountUpdateEvents';
import { callOnEnterPress } from '../../../../helpers/eventHelpers';

export interface EditableAccountFieldProps {
  displayValue: string;
  isInputValid: (value: string) => boolean;
  update: (
    value: string,
    onSuccess: () => void,
    onError: (errorHelperText?: string, alertProps?: AccountFieldAlertProps) => void
  ) => void;
  isPendingUpdate: boolean;
  labelKey: string;
  gtmDetailType: GtmAccountDetailType;
  validationErrorKey: string;
  successNotificationKey: string;
  textFieldOverrides?: TextFieldProps;
  submitButtonTranslationKey?: string;
}

export const EditableAccountField: React.FC<EditableAccountFieldProps> = ({
  displayValue,
  isInputValid,
  update,
  isPendingUpdate,
  labelKey,
  gtmDetailType,
  validationErrorKey,
  successNotificationKey,
  textFieldOverrides,
  submitButtonTranslationKey = TranslationKeys.CommonComponents.SAVE_BUTTON
}) => {
  const { setNotification } = useContext(NotificationContext);
  const { sendStartEditingAccountDetailsEvent } = useAccountUpdateEvents();
  const { t: translate } = useTranslation();
  const [value, setValue] = useState<string>(displayValue);
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [error, setError] = useState<string | undefined>();
  const [inputGroupAlert, setInputGroupAlert] = useState<AccountFieldAlertProps | undefined>();
  const label = translate(labelKey);

  useEffect(() => {
    setError(undefined);
  }, [isEditing]);

  const inputIsInvalidOrUnchanged = () => {
    if (value === displayValue) return true;
    return !isInputValid(value);
  };

  const setErrorIfInvalid = () => {
    if (!isInputValid(value)) {
      setError(translate(validationErrorKey));
    } else {
      setError(undefined);
    }
  };

  const handleValueChange = (newValue: string) => {
    setValue(newValue);
    setError(undefined);
    setInputGroupAlert(undefined);
  };

  const handleCancel = () => {
    setValue(displayValue);
    setIsEditing(false);
    setError(undefined);
    setInputGroupAlert(undefined);
  };

  const handleEditClick = () => {
    sendStartEditingAccountDetailsEvent(gtmDetailType);
    setIsEditing(true);
  };

  const handleServerError = (errorHelperText: string) => {
    setError(errorHelperText);
  };
  const handleServerAlert = (alertKey: AccountFieldAlertProps) => {
    setInputGroupAlert({
      title: alertKey.title,
      message: alertKey.message
    });
  };

  const handleSave = () => {
    setErrorIfInvalid();
    if (inputIsInvalidOrUnchanged()) return;

    update(
      value,
      () => {
        setNotification({
          message: translate(successNotificationKey)
        });
        setIsEditing(false);
      },
      (errorHelperText, alertProps) => {
        if (errorHelperText) handleServerError(errorHelperText);
        if (alertProps) handleServerAlert(alertProps);
        if (!alertProps && !errorHelperText) {
          setNotification({
            message: translate(TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE),
            isError: true
          });
        }
      }
    );
  };

  return (
    <Grid data-testid="editable-account-field">
      {!isEditing ? (
        <AccountField label={label} value={displayValue} onEditClick={handleEditClick} />
      ) : (
        <Grid
          container
          flexDirection="column"
          gap={Design.Primitives.Spacing.xxs}
          sx={{
            paddingY: Design.Primitives.Spacing.sm,
            borderBottom: `1px solid ${Design.Alias.Color.neutral300}`
          }}>
          <Grid container flexDirection="column" gap={Design.Primitives.Spacing.sm}>
            <Grid container flexDirection="row" justifyContent="space-between">
              <Txt
                style={Design.Alias.Text.BodyUniversal.Md}
                sx={{ color: Design.Alias.Color.neutral700 }}>
                {label}
              </Txt>
            </Grid>
            {inputGroupAlert && (
              <AccountFieldAlert title={inputGroupAlert.title} message={inputGroupAlert.message} />
            )}
            <TextField
              autoFocus
              fullWidth
              color="secondary"
              inputRef={(input) => input && input.focus()}
              label={label}
              value={value}
              onBlur={setErrorIfInvalid}
              helperText={error}
              error={!!error}
              onChange={(event) => {
                handleValueChange(event.target.value);
              }}
              onKeyDown={callOnEnterPress(handleSave)}
              {...textFieldOverrides}
            />
          </Grid>

          <Grid
            sx={{ marginTop: Design.Primitives.Spacing.xxs }}
            container
            columnGap={Design.Primitives.Spacing.xxs}
            justifyContent="flex-end">
            <CancelButton onClick={handleCancel} disabled={isPendingUpdate} />
            <SaveButton
              onClick={handleSave}
              isLoading={isPendingUpdate}
              label={submitButtonTranslationKey}
              disabled={inputIsInvalidOrUnchanged()}
            />
          </Grid>
        </Grid>
      )}
    </Grid>
  );
};
