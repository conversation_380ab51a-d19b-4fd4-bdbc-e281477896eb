import React, { act } from 'react';
import { AxiosError, AxiosResponse } from 'axios';
import userEvent from '@testing-library/user-event';
import { screen, waitFor, within } from '@testing-library/react';
import { EditableEmailField } from '../EditableAccountField/EditableEmailField';
import {
  UpdateEmailErrorStatus,
  UpdateEmailRequest
} from '../../../../networkRequests/responseEntities/CustomerEntities';
import { mockGetCustomer, mockRefreshSession, mockUpdateEmail } from '../../../../../setupTests';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import {
  createCustomer,
  createEmail,
  createRefreshSessionClaims
} from '../../../../testUtils/MyPodsFactories';
import {
  renderWithPoetProvidersAndState,
  runPendingPromises
} from '../../../../testUtils/RenderHelpers';
import {
  accountPageActions as actions,
  accountPageViews as views
} from '../../__tests__/AccountPageViews';
import { expectNotificationAlertContainsTitle } from '../../../../testUtils/assertions';

const Tx = TranslationKeys.AccountPage.AccountInfo.Email;

describe('EditableEmailField', () => {
  const customerId = '*********';
  const customer = createCustomer({
    id: customerId,
    email: createEmail({ id: 42, address: '<EMAIL>' }),
    username: '<EMAIL>'
  });
  let newEmail = '<EMAIL>';

  beforeEach(() => {
    mockRefreshSession.mockResolvedValue(createRefreshSessionClaims());
    mockGetCustomer.mockResolvedValue(customer);
  });

  async function renderEmailField() {
    const result = renderWithPoetProvidersAndState(<EditableEmailField />);
    await runPendingPromises();
    return result;
  }

  async function renderAndUpdateEmail() {
    await renderEmailField();
    await actions.updateEmail(newEmail);
    await act(async () => {
      await userEvent.click(views.email.saveButton());
    });
  }

  it('should update email and show success snack bar when save is clicked', async () => {
    await renderAndUpdateEmail();

    const expectedRequest: UpdateEmailRequest = {
      email: { id: customer.email!.id, address: newEmail }
    };
    expect(views.email.accountField()).toBeInTheDocument(); // Restore the display view
    expect(mockUpdateEmail).toHaveBeenCalledWith(expectedRequest);
    expect(screen.getByText(newEmail)).toBeInTheDocument();
    expectNotificationAlertContainsTitle(
      TranslationKeys.CommonComponents.Notification.EMAIL_SAVE_SUCCEEDED
    );
  });

  describe('given the user receives a server error for the email, they will see helper text upon save', async () => {
    test.each([
      [UpdateEmailErrorStatus.EMAIL_ALREADY_IN_USE, Tx.HelperText.EMAIL_ALREADY_IN_USE],
      [UpdateEmailErrorStatus.INVALID_EMAIL, Tx.HelperText.INVALID_EMAIL]
    ])('when %s response code should show %s message', async (responseCode, displayText) => {
      const response: AxiosResponse = {
        data: { status: responseCode },
        status: 500
      } as AxiosResponse;

      mockUpdateEmail.mockRejectedValue(
        new AxiosError('message', '500', undefined, undefined, response)
      );

      await renderAndUpdateEmail();

      expect(within(views.email.baseField()).getByText(displayText)).toBeInTheDocument();
    });
  });

  describe('displays alert notifications when the updateEmail call fails with an error', () => {
    test.each([
      [
        UpdateEmailErrorStatus.ACCOUNT_UNDER_MAINTENANCE,
        Tx.Notifications.Title.ACCOUNT_UNDER_MAINTENANCE,
        Tx.Notifications.Message.ACCOUNT_UNDER_MAINTENANCE
      ],
      [UpdateEmailErrorStatus.ERROR, Tx.Notifications.Title.ERROR, Tx.Notifications.Message.ERROR],
      [
        UpdateEmailErrorStatus.NO_ACCOUNT_FOUND,
        Tx.Notifications.Title.NO_ACCOUNT_FOUND,
        Tx.Notifications.Message.NO_ACCOUNT_FOUND
      ],
      [
        UpdateEmailErrorStatus.TOKEN_EXPIRED,
        Tx.Notifications.Title.TOKEN_EXPIRED,
        Tx.Notifications.Message.TOKEN_EXPIRED
      ],
      [
        UpdateEmailErrorStatus.TOKEN_INVALID,
        Tx.Notifications.Title.TOKEN_INVALID,
        Tx.Notifications.Message.TOKEN_INVALID
      ]
    ])('when %s occurs display %s and %s', async (responseCode, title, message) => {
      const response: AxiosResponse = {
        data: { status: responseCode },
        status: 500
      } as AxiosResponse;

      mockUpdateEmail.mockRejectedValue(
        new AxiosError('message', '500', undefined, undefined, response)
      );

      await renderAndUpdateEmail();

      await waitFor(() => {
        expect(screen.getByRole('alert')).toHaveTextContent(title);
        expect(screen.getByRole('alert')).toHaveTextContent(message);
      });
    });
  });

  it('given an error occurs, displays generic failure', async () => {
    mockUpdateEmail.mockRejectedValue('Something went wrong.');

    await renderAndUpdateEmail();

    expectNotificationAlertContainsTitle(
      TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE
    );
  });
});
