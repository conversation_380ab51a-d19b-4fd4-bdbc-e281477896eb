import React, { act } from 'react';
import { screen } from '@testing-library/react';
import { BillingCard, BillingCardData } from '../BillingCard';
import { CHECKMARK_ICON_ID } from '../../../../components/icons/CheckmarkIcon';
import { EXCLAMATION_ICON_ID } from '../../../../components/icons/ExclamationIcon';
import { DOCUMENT_ICON_ID } from '../../../../components/icons/DocumentIcon';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import userEvent from '@testing-library/user-event';
import { mockGetFile, mockGetSasUrl, mockRefreshSession } from '../../../../../setupTests';
import {
  renderWithPoetProvidersAndState,
  runPendingPromises
} from '../../../../testUtils/RenderHelpers';
import { AxiosError, AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import { createSession } from 'react-router';

describe('BillingCard', () => {
  const id = 'ABC12399';
  const documentId = '**********';
  const invoiceNumber = 'PODS111222333';
  const amount = 1909.95;
  const totalAmount = 1939.95;
  const expectedTotal = '$1,909.95';
  const createBillingCardWithDefaults = (options?: Partial<BillingCardData>): BillingCardData => ({
    id,
    documentId,
    invoiceNumber,
    amount,
    totalAmount,
    date: new Date().toISOString(),
    type: 'unpaid-invoice',
    currencyType: 'USD',
    ...options
  });

  const renderBillingCard = async (billingCardData = createBillingCardWithDefaults()) => {
    const result = renderWithPoetProvidersAndState(<BillingCard {...billingCardData} />);
    await runPendingPromises();
    return result;
  };

  beforeEach(() => {
    mockRefreshSession.mockResolvedValue(createSession());
  });

  describe('when Poet invoice', () => {
    it('opens file when clicked', async () => {
      const expectedUrl = 'https://document.pdf';
      mockGetSasUrl.mockResolvedValue(expectedUrl);
      await renderBillingCard(createBillingCardWithDefaults());

      const button = screen.getByRole('button');

      expect(button).toBeEnabled();
      await act(async () => {
        await userEvent.click(button);
      });

      expect(mockGetSasUrl).toHaveBeenCalledWith(documentId, true);
      expect(window.location.href).toBe(expectedUrl);
    });
  });

  it('should display an error notification if a billing document fails to open', async () => {
    mockGetSasUrl.mockRejectedValue({});
    await renderBillingCard();

    const button = screen.getByRole('button');

    await act(async () => {
      await userEvent.click(button);
    });
    expect(screen.getByRole('alert')).toHaveTextContent(
      TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE
    );
  });

  it('renders future unpaid invoice', async () => {
    const expectedDueDate = '1/1/30';
    const invoiceNumber = 'PODS123456789';
    await renderBillingCard(
      createBillingCardWithDefaults({
        date: '2030-01-01',
        type: 'unpaid-invoice',
        invoiceNumber: invoiceNumber
      })
    );

    expect(
      screen.getByText(`${TranslationKeys.BillingPage.INVOICE} #${invoiceNumber}`)
    ).toBeInTheDocument();
    expect(screen.getByTestId(DOCUMENT_ICON_ID)).toBeInTheDocument();
    expect(screen.getByText(expectedTotal)).toBeInTheDocument();
    expect(
      screen.getByText(`${TranslationKeys.BillingPage.STATUS}[${expectedDueDate}]`)
    ).toBeInTheDocument();
  });

  it('renders future unpaid invoice with partial payment', async () => {
    const invoiceNumber = 'PODS123456789';
    const totalAmount = 1939.95;
    await renderBillingCard(
      createBillingCardWithDefaults({
        date: '2030-01-01',
        type: 'unpaid-invoice',
        totalAmount: totalAmount,
        invoiceNumber: invoiceNumber
      })
    );

    expect(
      screen.getByText(`${TranslationKeys.BillingPage.INVOICE} #${invoiceNumber}`)
    ).toBeInTheDocument();
    expect(screen.getByTestId(DOCUMENT_ICON_ID)).toBeInTheDocument();
    expect(screen.getByText(expectedTotal)).toBeInTheDocument();
    expect(screen.getByText('$1,939.95')).toBeInTheDocument();
  });

  it('renders unpaid invoice past due', async () => {
    const expectedDueDate = '1/1/20';
    const invoiceNumber = 'PODS123456789';
    await renderBillingCard(
      createBillingCardWithDefaults({
        date: '2020-01-01',
        type: 'unpaid-invoice',
        invoiceNumber: invoiceNumber
      })
    );

    expect(
      screen.getByText(`${TranslationKeys.BillingPage.INVOICE} #${invoiceNumber}`)
    ).toBeInTheDocument();
    expect(screen.getByTestId(EXCLAMATION_ICON_ID)).toBeInTheDocument();
    expect(screen.getByText(expectedTotal)).toBeInTheDocument();
    screen.getByText(`${TranslationKeys.BillingPage.STATUS}[${expectedDueDate}]`);
  });

  it('renders paid invoice', async () => {
    const expectedDueDate = '1/1/20';
    const invoiceNumber = 'PODS123456789';
    await renderBillingCard(
      createBillingCardWithDefaults({
        date: '2020-01-01',
        type: 'paid-invoice',
        invoiceNumber: invoiceNumber
      })
    );

    expect(
      screen.getByText(`${TranslationKeys.BillingPage.INVOICE} #${invoiceNumber}`)
    ).toBeInTheDocument();
    expect(screen.getByTestId(CHECKMARK_ICON_ID)).toBeInTheDocument();
    expect(screen.getByText(expectedTotal)).toBeInTheDocument();
    expect(
      screen.getByText(`${TranslationKeys.BillingPage.STATUS}[${expectedDueDate}]`)
    ).toBeInTheDocument();
  });

  it('renders monthly statement', async () => {
    const expectedDueDate = '04/24';
    await renderBillingCard(
      createBillingCardWithDefaults({
        date: '2024-04-01',
        type: 'statement'
      })
    );

    expect(
      screen.getByText(`April ${TranslationKeys.BillingPage.Statements.Statement.TITLE}`)
    ).toBeInTheDocument();
    expect(screen.getByTestId(DOCUMENT_ICON_ID)).toBeInTheDocument();
    expect(screen.queryByText('$', { exact: false })).not.toBeInTheDocument();
    expect(screen.getByText(expectedDueDate)).toBeInTheDocument();
    expect(screen.getByText(`#${id}`)).toBeInTheDocument();
  });

  it('should display error when documentId is zero', async () => {
    await renderBillingCard(createBillingCardWithDefaults({ documentId: '0' }));

    const button = screen.getByRole('button');

    await act(async () => {
      await userEvent.click(button);
    });
    expect(mockGetFile).not.toHaveBeenCalled();
    expect(screen.getByRole('alert')).toHaveTextContent(
      TranslationKeys.CommonComponents.Notification.FILE_NOT_READY_ERROR
    );
  });

  it('should display NOT_FOUND error when documentId is not found', async () => {
    const mockAxiosResponse: AxiosResponse<unknown, any> = {
      data: 'Resource not found', // or use undefined if you want no data
      status: 404,
      statusText: 'Not Found',
      headers: {},
      config: {} as InternalAxiosRequestConfig<any>
    };
    mockGetSasUrl.mockRejectedValue(
      new AxiosError(
        'Request failed with status code 404',
        '404',
        undefined,
        undefined,
        mockAxiosResponse
      )
    );
    await renderBillingCard();

    const button = screen.getByRole('button');

    await act(async () => {
      await userEvent.click(button);
    });
    expect(screen.getByRole('alert')).toHaveTextContent(
      TranslationKeys.CommonComponents.Notification.DOCUMENT_NOT_FOUND
    );
  });
});
