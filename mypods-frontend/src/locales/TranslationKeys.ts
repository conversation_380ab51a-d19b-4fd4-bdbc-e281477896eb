// generated by `npm run i18n:build`; do not edit directly
// add desired design changes to en_us.json
export const TranslationKeys = {
  CommonComponents: {
    EDIT_BUTTON: 'commonComponents.editButton',
    SAVE_BUTTON: 'commonComponents.saveButton',
    CANCEL_BUTTON: 'commonComponents.cancelButton',
    CONFIRM_CANCEL_BUTTON: 'commonComponents.confirmCancelButton',
    CONFIRM_BUTTON: 'commonComponents.confirmButton',
    CONTINUE_BUTTON: 'commonComponents.continueButton',
    BACK_BUTTON: 'commonComponents.backButton',
    NEVERMIND_BUTTON: 'commonComponents.nevermindButton',
    NEXT_BUTTON: 'commonComponents.nextButton',
    PREVIOUS_BUTTON: 'commonComponents.previousButton',
    YES_BUTTON: 'commonComponents.yesButton',
    NO_BUTTON: 'commonComponents.noButton',
    REVIEW_BUTTON: 'commonComponents.reviewButton',
    ACCEPT_BUTTON: 'commonComponents.acceptButton',
    PROGRESS_COUNTER: 'commonComponents.progressCounter',
    PERCENT: 'commonComponents.percent',
    AddSignature: {
      BUTTON_TEXT: 'commonComponents.addSignature.buttonText',
      ELECTRONICALLY_SIGNED: 'commonComponents.addSignature.electronicallySigned',
      SIGNED_DATE_LABEL: 'commonComponents.addSignature.signedDateLabel',
      CUSTOMER_ID: 'commonComponents.addSignature.customerId'
    },
    Notification: {
      BILLING_ADDRESS_SAVE_SUCCEEDED: 'commonComponents.notification.billingAddressSaveSucceeded',
      EMAIL_SAVE_SUCCEEDED: 'commonComponents.notification.emailSaveSucceeded',
      NEW_PASSWORD_SAVE_SUCCEEDED: 'commonComponents.notification.newPasswordSaveSucceeded',
      PIN_SAVE_SUCCEEDED: 'commonComponents.notification.pinSaveSucceeded',
      PRIMARY_PHONE_NUMBER_SAVE_SUCCEEDED:
        'commonComponents.notification.primaryPhoneNumberSaveSucceeded',
      SECONDARY_PHONE_NUMBER_SAVE_SUCCEEDED:
        'commonComponents.notification.secondaryPhoneNumberSaveSucceeded',
      SHIPPING_ADDRESS_SAVE_SUCCEEDED: 'commonComponents.notification.shippingAddressSaveSucceeded',
      SMS_PREFERENCES_SAVE_SUCCEEDED: 'commonComponents.notification.smsPreferencesSaveSucceeded',
      PASSWORD_NOT_SAVED: 'commonComponents.notification.passwordNotSaved',
      CONTAINER_VISIT_CANCELED: 'commonComponents.notification.containerVisitCanceled',
      GENERIC_FAILURE: 'commonComponents.notification.genericFailure',
      PAYMENT_METHOD_ADDED_SUCCEEDED: 'commonComponents.notification.paymentMethodAddedSucceeded',
      PAYMENT_METHOD_ADD_FAILED: 'commonComponents.notification.paymentMethodAddFailed',
      PAYMENT_METHOD_ADD_VALIDATION_ERROR:
        'commonComponents.notification.paymentMethodAddValidationError',
      BILLING_ADDRESS_SAVE_FAILED: 'commonComponents.notification.billingAddressSaveFailed',
      SIGN_FNPS_WAIVER_SUCCEEDED: 'commonComponents.notification.signFnpsWaiverSucceeded',
      CONTAINER_PLACEMENT_SUCCEEDED: 'commonComponents.notification.containerPlacementSucceeded',
      FILE_NOT_READY_ERROR: 'commonComponents.notification.fileNotReadyError',
      DOCUMENT_NOT_FOUND: 'commonComponents.notification.documentNotFound',
      INVALID_PASSWORD: 'commonComponents.notification.invalidPassword'
    },
    Input: {
      Error: {
        INVALID_PHONE: 'commonComponents.input.error.invalidPhone',
        INVALID_EMAIL: 'commonComponents.input.error.invalidEmail',
        INVALID_POSTAL_CODE: 'commonComponents.input.error.invalidPostalCode',
        Validation: {
          BASE: 'commonComponents.input.error.validation.base',
          PIN: 'commonComponents.input.error.validation.pin',
          Password: {
            LENGTH: 'commonComponents.input.error.validation.password.length',
            LOWERCASE: 'commonComponents.input.error.validation.password.lowercase',
            UPPERCASE: 'commonComponents.input.error.validation.password.uppercase',
            SPECIAL_CHARACTER: 'commonComponents.input.error.validation.password.specialCharacter'
          }
        },
        REQUIRED: 'commonComponents.input.error.required',
        STATE_LENGTH: 'commonComponents.input.error.stateLength'
      }
    },
    Banner: {
      TITLE: 'commonComponents.banner.title',
      MESSAGE: 'commonComponents.banner.message',
      TITLE_ONLINE_SCHEDULING_UNAVAILABLE:
        'commonComponents.banner.title_onlineSchedulingUnavailable',
      MESSAGE_ONLINE_SCHEDULING_UNAVAILABLE:
        'commonComponents.banner.message_onlineSchedulingUnavailable',
      CTA_LABEL: 'commonComponents.banner.ctaLabel',
      TITLE_DESYNCED_EMAILS: 'commonComponents.banner.title_desyncedEmails',
      MESSAGE_DESYNCED_EMAILS: 'commonComponents.banner.message_desyncedEmails',
      CTA_LABEL_DESYNCED_EMAILS: 'commonComponents.banner.ctaLabel_desyncedEmails'
    }
  },
  Navigation: {
    HOME: 'navigation.home',
    BILLING: 'navigation.billing',
    ACCOUNT: 'navigation.account',
    SUPPORT_PHONE_NUMBER_MOBILE_LABEL: 'navigation.supportPhoneNumberMobileLabel',
    SUPPORT_PHONE_NUMBER: 'navigation.supportPhoneNumber',
    Dropdown: {
      LOGOUT: 'navigation.dropdown.logout',
      ACCOUNT: 'navigation.dropdown.account',
      DOCUMENTS: 'navigation.dropdown.documents'
    }
  },
  AccountPage: {
    AccountInfo: {
      HEADER: 'accountPage.accountInfo.header',
      Email: {
        LABEL: 'accountPage.accountInfo.email.label',
        OutOfSync: {
          LABEL: 'accountPage.accountInfo.email.outOfSync.label',
          OTHER: 'accountPage.accountInfo.email.outOfSync.other'
        },
        Notifications: {
          Title: {
            ACCOUNT_UNDER_MAINTENANCE:
              'accountPage.accountInfo.email.notifications.title.accountUnderMaintenance',
            NO_ACCOUNT_FOUND: 'accountPage.accountInfo.email.notifications.title.noAccountFound',
            SUCCESS: 'accountPage.accountInfo.email.notifications.title.success',
            TOKEN_EXPIRED: 'accountPage.accountInfo.email.notifications.title.tokenExpired',
            TOKEN_INVALID: 'accountPage.accountInfo.email.notifications.title.tokenInvalid',
            ERROR: 'accountPage.accountInfo.email.notifications.title.error'
          },
          Message: {
            ACCOUNT_UNDER_MAINTENANCE:
              'accountPage.accountInfo.email.notifications.message.accountUnderMaintenance',
            NO_ACCOUNT_FOUND: 'accountPage.accountInfo.email.notifications.message.noAccountFound',
            SUCCESS: 'accountPage.accountInfo.email.notifications.message.success',
            TOKEN_EXPIRED: 'accountPage.accountInfo.email.notifications.message.tokenExpired',
            TOKEN_INVALID: 'accountPage.accountInfo.email.notifications.message.tokenInvalid',
            ERROR: 'accountPage.accountInfo.email.notifications.message.error'
          }
        },
        HelperText: {
          EMAIL_ALREADY_IN_USE: 'accountPage.accountInfo.email.helperText.emailAlreadyInUse',
          INVALID_EMAIL: 'accountPage.accountInfo.email.helperText.invalidEmail'
        },
        OtpVerification: {
          Dialog: {
            TITLE: 'accountPage.accountInfo.email.otpVerification.dialog.title',
            SUBTITLE: 'accountPage.accountInfo.email.otpVerification.dialog.subtitle',
            MISSING_CODE: 'accountPage.accountInfo.email.otpVerification.dialog.missingCode',
            RESEND_CODE_COUNTDOWN:
              'accountPage.accountInfo.email.otpVerification.dialog.resendCodeCountdown',
            RESEND_CODE_BUTTON:
              'accountPage.accountInfo.email.otpVerification.dialog.resendCodeButton',
            HELPER_TEXT: 'accountPage.accountInfo.email.otpVerification.dialog.helperText'
          }
        }
      },
      Password: {
        HelperText: {
          INVALID_CREDENTIALS: 'accountPage.accountInfo.password.helperText.invalidCredentials',
          NEW_PASSWORD_FAILED_REQUIREMENTS:
            'accountPage.accountInfo.password.helperText.newPasswordFailedRequirements',
          NEW_PASSWORD_IS_COMMON: 'accountPage.accountInfo.password.helperText.newPasswordIsCommon',
          NEW_PASSWORD_IS_REUSED: 'accountPage.accountInfo.password.helperText.newPasswordIsReused',
          NEW_PASSWORD_IS_OLD_PASSWORD:
            'accountPage.accountInfo.password.helperText.newPasswordIsOldPassword',
          PASSWORD_FAILED_REQUIREMENTS:
            'accountPage.accountInfo.password.helperText.passwordFailedRequirements'
        },
        Notifications: {
          Title: {
            ACCOUNT_NOT_CONVERTED:
              'accountPage.accountInfo.password.notifications.title.accountNotConverted',
            NO_ACCOUNT_FOUND: 'accountPage.accountInfo.password.notifications.title.noAccountFound'
          },
          Message: {
            ACCOUNT_NOT_CONVERTED:
              'accountPage.accountInfo.password.notifications.message.accountNotConverted',
            NO_ACCOUNT_FOUND:
              'accountPage.accountInfo.password.notifications.message.noAccountFound'
          }
        },
        Labels: {
          VIEW_PASSWORD: 'accountPage.accountInfo.password.labels.viewPassword',
          CURRENT_PASSWORD: 'accountPage.accountInfo.password.labels.currentPassword',
          NEW_PASSWORD: 'accountPage.accountInfo.password.labels.newPassword'
        },
        VALIDATION_HEADER: 'accountPage.accountInfo.password.validationHeader',
        Rules: {
          LENGTH: 'accountPage.accountInfo.password.rules.length',
          LOWERCASE: 'accountPage.accountInfo.password.rules.lowercase',
          UPPERCASE: 'accountPage.accountInfo.password.rules.uppercase',
          SPECIAL_CHARACTER: 'accountPage.accountInfo.password.rules.specialCharacter'
        }
      }
    },
    Communication: {
      HEADER: 'accountPage.communication.header',
      SmsPreferences: {
        LABEL: 'accountPage.communication.smsPreferences.label',
        DESCRIPTION: 'accountPage.communication.smsPreferences.description'
      }
    },
    ContactInfo: {
      HEADER: 'accountPage.contactInfo.header',
      Labels: {
        PRIMARY_PHONE: 'accountPage.contactInfo.labels.primaryPhone',
        SECONDARY_PHONE: 'accountPage.contactInfo.labels.secondaryPhone'
      }
    },
    AddressInfo: {
      HEADER: 'accountPage.addressInfo.header',
      Labels: {
        BILLING_ADDRESS: 'accountPage.addressInfo.labels.billingAddress',
        SHIPPING_ADDRESS: 'accountPage.addressInfo.labels.shippingAddress'
      },
      InputFields: {
        ADDRESS1: 'accountPage.addressInfo.inputFields.address1',
        ADDRESS2: 'accountPage.addressInfo.inputFields.address2',
        POSTAL_CODE: 'accountPage.addressInfo.inputFields.postalCode',
        CITY: 'accountPage.addressInfo.inputFields.city',
        STATE: 'accountPage.addressInfo.inputFields.state'
      }
    },
    SupportInfo: {
      HEADER: 'accountPage.supportInfo.header',
      Labels: {
        SUPPORT_PIN: 'accountPage.supportInfo.labels.supportPin',
        SECURITY_QUESTION: 'accountPage.supportInfo.labels.securityQuestion',
        SECURITY_ANSWER: 'accountPage.supportInfo.labels.securityAnswer'
      }
    }
  },
  HomePage: {
    HEADER: 'homePage.header',
    WELCOME_MESSAGE: 'homePage.welcomeMessage',
    SkeletonLoader: {
      HEADER: 'homePage.skeletonLoader.header',
      SUB_HEADER: 'homePage.skeletonLoader.subHeader'
    },
    Disclosure: {
      Summary: {
        ORDER_NUMBER: 'homePage.disclosure.summary.order_number',
        CITY_STATE: 'homePage.disclosure.summary.cityState'
      }
    },
    Map: {
      ContainerDialog: {
        PAST: 'homePage.map.containerDialog.past',
        UNSCHEDULED: 'homePage.map.containerDialog.unscheduled',
        FUTURE: 'homePage.map.containerDialog.future'
      }
    },
    ContainerTile: {
      ContainerHeader: {
        CONTAINER_ID: 'homePage.containerTile.containerHeader.containerId',
        CONTAINER_TYPE: 'homePage.containerTile.containerHeader.containerType',
        NO_CONTAINER_ID: 'homePage.containerTile.containerHeader.noContainerId'
      },
      SHOW_HISTORY: 'homePage.containerTile.showHistory',
      HIDE_HISTORY: 'homePage.containerTile.hideHistory',
      BACK_BUTTON: 'homePage.containerTile.backButton',
      SHOW_UPCOMING_ACTIVITY: 'homePage.containerTile.showUpcomingActivity',
      HIDE_UPCOMING_ACTIVITY: 'homePage.containerTile.hideUpcomingActivity',
      OrderDetails: {
        STATUS: 'homePage.containerTile.orderDetails.status',
        ORDER_NUMBER: 'homePage.containerTile.orderDetails.orderNumber'
      },
      SERVICE_COUNTDOWN: 'homePage.containerTile.serviceCountdown',
      SERVICE_COUNTDOWN_ETA_BETWEEN: 'homePage.containerTile.serviceCountdownEtaBetween',
      SERVICE_COUNTDOWN_HELPER_STORAGE_TOMORROW:
        'homePage.containerTile.serviceCountdownHelperStorageTomorrow',
      SERVICE_COUNTDOWN_HELPER_STORAGE_TODAY:
        'homePage.containerTile.serviceCountdownHelperStorageToday',
      SERVICE_COUNTDOWN_HELPER_WITH_ETA: 'homePage.containerTile.serviceCountdownHelperWithEta',
      SERVICE_COUNTDOWN_HELPER_WITHOUT_ETA:
        'homePage.containerTile.serviceCountdownHelperWithoutEta',
      SERVICE_COUNTDOWN_HELPER24_HOUR: 'homePage.containerTile.serviceCountdownHelper24Hour'
    },
    ContainerCard: {
      SERVICE_DATE: 'homePage.containerCard.serviceDate',
      SERVICE_DATE_TRANSIT: 'homePage.containerCard.serviceDate_transit',
      SERVICE_DATE_VISIT: 'homePage.containerCard.serviceDate_visit',
      SERVICE_DATE_TODAY_TOMORROW: 'homePage.containerCard.serviceDateTodayTomorrow',
      SERVICE_DATE_TODAY_TOMORROW_TRANSIT:
        'homePage.containerCard.serviceDateTodayTomorrow_transit',
      SERVICE_DATE_TODAY_TOMORROW_VISIT: 'homePage.containerCard.serviceDateTodayTomorrow_visit',
      Status: {
        UNSCHEDULED_CONTAINER_TITLE: 'homePage.containerCard.status.unscheduledContainerTitle',
        UNSCHEDULED_CONTAINER_SUBTITLE: 'homePage.containerCard.status.unscheduledContainerSubtitle'
      },
      Timeline: {
        Labels: {
          DELIVERY: 'homePage.containerCard.timeline.labels.delivery',
          PICKUP: 'homePage.containerCard.timeline.labels.pickup',
          STORAGE: 'homePage.containerCard.timeline.labels.storage',
          TRANSIT: 'homePage.containerCard.timeline.labels.transit',
          DROPOFF: 'homePage.containerCard.timeline.labels.dropoff',
          RETURN: 'homePage.containerCard.timeline.labels.return',
          VISIT: 'homePage.containerCard.timeline.labels.visit'
        }
      }
    },
    MoveLegs: {
      Title: {
        INITIAL_DELIVERY: 'homePage.moveLegs.title.initialDelivery',
        REDELIVERY: 'homePage.moveLegs.title.redelivery',
        PICKUP: 'homePage.moveLegs.title.pickup',
        FINAL_PICKUP: 'homePage.moveLegs.title.finalPickup',
        CONTAINER_AT_WAREHOUSE: 'homePage.moveLegs.title.containerAtWarehouse',
        CONTAINER_AT_WAREHOUSE_HISTORY: 'homePage.moveLegs.title.containerAtWarehouseHistory',
        CONTAINER_MOVE_LOCAL: 'homePage.moveLegs.title.containerMoveLocal',
        WAREHOUSE_TO_WAREHOUSE: 'homePage.moveLegs.title.warehouseToWarehouse',
        SELF_INITIAL_DELIVERY: 'homePage.moveLegs.title.selfInitialDelivery',
        SELF_FINAL_PICKUP: 'homePage.moveLegs.title.selfFinalPickup',
        IN_TRANSIT: 'homePage.moveLegs.title.inTransit',
        CALL_TO_SCHEDULE: 'homePage.moveLegs.title.callToSchedule',
        CALL_TO_RESCHEDULE: 'homePage.moveLegs.title.callToReschedule',
        VISIT_CONTAINER: 'homePage.moveLegs.title.visitContainer',
        CHAT_BUTTON: 'homePage.moveLegs.title.chatButton',
        SMS_BUTTON: 'homePage.moveLegs.title.SMSButton'
      },
      UpcomingMoveLeg: {
        DESCRIPTION_GENERIC: 'homePage.moveLegs.upcomingMoveLeg.descriptionGeneric',
        DESCRIPTION_PREFIX: 'homePage.moveLegs.upcomingMoveLeg.descriptionPrefix',
        MoveLegSuffix: {
          FINAL_PICK_UP: 'homePage.moveLegs.upcomingMoveLeg.moveLegSuffix.finalPickUp',
          REDELIVERY: 'homePage.moveLegs.upcomingMoveLeg.moveLegSuffix.redelivery',
          SELF_INITIAL_DELIVERY:
            'homePage.moveLegs.upcomingMoveLeg.moveLegSuffix.selfInitialDelivery'
        }
      },
      IN_TRANSIT_DESCRIPTION: 'homePage.moveLegs.inTransitDescription',
      CALL_TO_SCHEDULE_DESCRIPTION: 'homePage.moveLegs.callToScheduleDescription',
      CALL_TO_SCHEDULE_WHEN_ONLINE_SCHEDULING_DISABLED:
        'homePage.moveLegs.callToScheduleWhenOnlineSchedulingDisabled',
      CALL_FOR_SPECIAL_ASSISTANCE: 'homePage.moveLegs.callForSpecialAssistance',
      MESSAGE_UNAVAILABLE_TO_SCHEDULE: 'homePage.moveLegs.message_Unavailable_ToSchedule',
      Scheduling: {
        SCHEDULE_UPDATE_SUCCESSFUL: 'homePage.moveLegs.scheduling.scheduleUpdateSuccessful',
        SCHEDULE_VISIT_CONTAINER_SUCCESSFUL:
          'homePage.moveLegs.scheduling.scheduleVisitContainerSuccessful',
        RESCHEDULE_VISIT_CONTAINER_SUCCESSFUL:
          'homePage.moveLegs.scheduling.rescheduleVisitContainerSuccessful',
        PRICE_CHANGE_TITLE: 'homePage.moveLegs.scheduling.priceChangeTitle',
        PRICE_CHANGE_DESCRIPTION: 'homePage.moveLegs.scheduling.priceChangeDescription',
        SERVICE_NOT_SAME_AREA_TITLE: 'homePage.moveLegs.scheduling.serviceNotSameAreaTitle',
        SERVICE_NOT_SAME_AREA_DESCRIPTION:
          'homePage.moveLegs.scheduling.serviceNotSameAreaDescription',
        AddressLabels: {
          LABEL_STORAGE_CENTER: 'homePage.moveLegs.scheduling.addressLabels.labelStorageCenter',
          LABEL_DEFAULT: 'homePage.moveLegs.scheduling.addressLabels.labelDefault'
        },
        CONTAINER_PLACEMENT_TITLE: 'homePage.moveLegs.scheduling.containerPlacementTitle',
        CONTAINER_PLACEMENT_BUTTON: 'homePage.moveLegs.scheduling.containerPlacementButton',
        CONTAINER_PLACEMENT_SUBTITLE: 'homePage.moveLegs.scheduling.containerPlacementSubtitle',
        CONTAINER_PLACEMENT_COMPLETED: 'homePage.moveLegs.scheduling.containerPlacementCompleted',
        NO_WAREHOUSE_HOURS_FOUND: 'homePage.moveLegs.scheduling.noWarehouseHoursFound',
        DateLabels: {
          LABEL_DELIVERY: 'homePage.moveLegs.scheduling.dateLabels.labelDelivery',
          LABEL_PICKUP: 'homePage.moveLegs.scheduling.dateLabels.labelPickup',
          LABEL_ARRIVAL: 'homePage.moveLegs.scheduling.dateLabels.labelArrival',
          LABEL_MOVE: 'homePage.moveLegs.scheduling.dateLabels.labelMove',
          LABEL_DROP_OFF: 'homePage.moveLegs.scheduling.dateLabels.labelDropOff',
          LABEL_RETURN: 'homePage.moveLegs.scheduling.dateLabels.labelReturn',
          LABEL_VISIT: 'homePage.moveLegs.scheduling.dateLabels.labelVisit',
          LABEL_VISIT_WINDOW: 'homePage.moveLegs.scheduling.dateLabels.labelVisitWindow',
          LABEL_PICKUP_WINDOW: 'homePage.moveLegs.scheduling.dateLabels.labelPickupWindow',
          LABEL_DEFAULT: 'homePage.moveLegs.scheduling.dateLabels.labelDefault',
          UNSCHEDULED_DATE: 'homePage.moveLegs.scheduling.dateLabels.UnscheduledDate'
        },
        PickupContainer: {
          Title: {
            STEP1: 'homePage.moveLegs.scheduling.pickupContainer.title.step1',
            STEP2: 'homePage.moveLegs.scheduling.pickupContainer.title.step2'
          },
          SUBTITLE: 'homePage.moveLegs.scheduling.pickupContainer.subtitle',
          DETAILS_TITLE: 'homePage.moveLegs.scheduling.pickupContainer.detailsTitle',
          ADDRESS_LABEL: 'homePage.moveLegs.scheduling.pickupContainer.addressLabel',
          DATE_LABEL: 'homePage.moveLegs.scheduling.pickupContainer.dateLabel',
          INFO_TITLE: 'homePage.moveLegs.scheduling.pickupContainer.infoTitle',
          Info: {
            Info1: {
              TITLE: 'homePage.moveLegs.scheduling.pickupContainer.info.info1.title',
              DESC: 'homePage.moveLegs.scheduling.pickupContainer.info.info1.desc'
            },
            Info2: {
              TITLE: 'homePage.moveLegs.scheduling.pickupContainer.info.info2.title',
              DESC: 'homePage.moveLegs.scheduling.pickupContainer.info.info2.desc'
            }
          },
          LOADING_TEXT: 'homePage.moveLegs.scheduling.pickupContainer.loadingText'
        },
        VisitContainer: {
          Title: {
            STEP1: 'homePage.moveLegs.scheduling.visitContainer.title.step1',
            STEP2: 'homePage.moveLegs.scheduling.visitContainer.title.step2'
          },
          SUBTITLE: 'homePage.moveLegs.scheduling.visitContainer.subtitle',
          WAREHOUSE_HOURS_UNSCHEDULED:
            'homePage.moveLegs.scheduling.visitContainer.warehouseHoursUnscheduled',
          DETAILS_TITLE: 'homePage.moveLegs.scheduling.visitContainer.detailsTitle',
          ADDRESS_LABEL: 'homePage.moveLegs.scheduling.visitContainer.addressLabel',
          ADDRESS_VALUE: 'homePage.moveLegs.scheduling.visitContainer.addressValue',
          DATE_LABEL: 'homePage.moveLegs.scheduling.visitContainer.dateLabel',
          INFO_TITLE: 'homePage.moveLegs.scheduling.visitContainer.infoTitle',
          Info: {
            Info1: {
              TITLE1: 'homePage.moveLegs.scheduling.visitContainer.info.info1.title1',
              DESC1: 'homePage.moveLegs.scheduling.visitContainer.info.info1.desc1'
            },
            Info2: {
              TITLE2: 'homePage.moveLegs.scheduling.visitContainer.info.info2.title2',
              DESC2: 'homePage.moveLegs.scheduling.visitContainer.info.info2.desc2'
            }
          },
          LOADING_TEXT: 'homePage.moveLegs.scheduling.visitContainer.loadingText'
        },
        DropOffContainer: {
          Title: {
            STEP1: 'homePage.moveLegs.scheduling.dropOffContainer.title.step1',
            STEP1_FINAL_DROP_OFF:
              'homePage.moveLegs.scheduling.dropOffContainer.title.step1_finalDropOff',
            STEP2: 'homePage.moveLegs.scheduling.dropOffContainer.title.step2',
            STEP2_FINAL_DROP_OFF:
              'homePage.moveLegs.scheduling.dropOffContainer.title.step2_finalDropOff'
          },
          SUBTITLE: 'homePage.moveLegs.scheduling.dropOffContainer.subtitle',
          DETAILS_TITLE: 'homePage.moveLegs.scheduling.dropOffContainer.detailsTitle',
          ADDRESS_LABEL: 'homePage.moveLegs.scheduling.dropOffContainer.addressLabel',
          DATE_LABEL: 'homePage.moveLegs.scheduling.dropOffContainer.dateLabel',
          INFO_TITLE: 'homePage.moveLegs.scheduling.dropOffContainer.infoTitle',
          Info: {
            Info1: {
              TITLE: 'homePage.moveLegs.scheduling.dropOffContainer.info.info1.title',
              DESC: 'homePage.moveLegs.scheduling.dropOffContainer.info.info1.desc'
            },
            Info2: {
              TITLE: 'homePage.moveLegs.scheduling.dropOffContainer.info.info2.title',
              DESC: 'homePage.moveLegs.scheduling.dropOffContainer.info.info2.desc'
            }
          },
          LOADING_TEXT: 'homePage.moveLegs.scheduling.dropOffContainer.loadingText'
        }
      },
      SCHEDULE_BUTTON: 'homePage.moveLegs.scheduleButton',
      InitialDeliveryReviewAlert: {
        UPPER_TITLE: 'homePage.moveLegs.initialDeliveryReviewAlert.upperTitle',
        TITLE: 'homePage.moveLegs.initialDeliveryReviewAlert.title',
        SUBTITLE: 'homePage.moveLegs.initialDeliveryReviewAlert.subtitle'
      },
      InitialDeliveryPriceDifferenceAlert: {
        TITLE: 'homePage.moveLegs.initialDeliveryPriceDifferenceAlert.title',
        SUBTITLE: 'homePage.moveLegs.initialDeliveryPriceDifferenceAlert.subtitle'
      },
      InitialDeliverySaveDetails: {
        TITLE: 'homePage.moveLegs.initialDeliverySaveDetails.title',
        SUBTITLE: 'homePage.moveLegs.initialDeliverySaveDetails.subtitle'
      }
    },
    ContainerPlacement: {
      Address: {
        TITLE: 'homePage.containerPlacement.address.title'
      },
      PavedSurfaceScreen: {
        TITLE: 'homePage.containerPlacement.pavedSurfaceScreen.title',
        SUBTITLE: 'homePage.containerPlacement.pavedSurfaceScreen.subtitle',
        ALERT_TITLE: 'homePage.containerPlacement.pavedSurfaceScreen.alertTitle',
        ALERT_DESCRIPTION: 'homePage.containerPlacement.pavedSurfaceScreen.alertDescription'
      },
      PlacementTips: {
        TITLE: 'homePage.containerPlacement.placementTips.title',
        ITEM1: 'homePage.containerPlacement.placementTips.item1',
        ITEM2: 'homePage.containerPlacement.placementTips.item2',
        ITEM3: 'homePage.containerPlacement.placementTips.item3',
        ITEM4: 'homePage.containerPlacement.placementTips.item4',
        ITEM5: 'homePage.containerPlacement.placementTips.item5',
        ITEM6: 'homePage.containerPlacement.placementTips.item6'
      },
      PLACEMENT_TIP: 'homePage.containerPlacement.placementTip',
      SiteTypeScreen: {
        TITLE: 'homePage.containerPlacement.siteTypeScreen.title',
        Buttons: {
          DRIVEWAY: 'homePage.containerPlacement.siteTypeScreen.buttons.driveway',
          STREET: 'homePage.containerPlacement.siteTypeScreen.buttons.street',
          PARKING_LOT: 'homePage.containerPlacement.siteTypeScreen.buttons.parkingLot'
        },
        Prompts: {
          Driveway: {
            SITE_TYPE: 'homePage.containerPlacement.siteTypeScreen.prompts.driveway.siteType',
            CONTAINER_LOCATION:
              'homePage.containerPlacement.siteTypeScreen.prompts.driveway.containerLocation',
            CAB_DIRECTION:
              'homePage.containerPlacement.siteTypeScreen.prompts.driveway.cabDirection'
          },
          StreetAlleyway: {
            SITE_TYPE: 'homePage.containerPlacement.siteTypeScreen.prompts.streetAlleyway.siteType'
          },
          ParkingLot: {
            SITE_TYPE: 'homePage.containerPlacement.siteTypeScreen.prompts.parkingLot.siteType'
          }
        }
      },
      DriverNotes: {
        TITLE: 'homePage.containerPlacement.driverNotes.title',
        SUBTITLE: 'homePage.containerPlacement.driverNotes.subtitle',
        LABEL: 'homePage.containerPlacement.driverNotes.label',
        HELPER_TEXT: 'homePage.containerPlacement.driverNotes.helperText'
      },
      ReviewScreen: {
        TITLE: 'homePage.containerPlacement.reviewScreen.title',
        SUBTITLE: 'homePage.containerPlacement.reviewScreen.subtitle',
        FINISH_BUTTON: 'homePage.containerPlacement.reviewScreen.finishButton',
        Labels: {
          ADDRESS: 'homePage.containerPlacement.reviewScreen.labels.address',
          PLACEMENT_SITE: 'homePage.containerPlacement.reviewScreen.labels.placementSite',
          CONTAINER_PLACEMENT: 'homePage.containerPlacement.reviewScreen.labels.containerPlacement',
          DRIVER_INSTRUCTIONS: 'homePage.containerPlacement.reviewScreen.labels.driverInstructions'
        }
      }
    },
    SplashScreen: {
      ACCEPT_BUTTON: 'homePage.splashScreen.acceptButton',
      TITLE: 'homePage.splashScreen.title',
      DESCRIPTION: 'homePage.splashScreen.description',
      Features: {
        Scheduling: {
          TITLE: 'homePage.splashScreen.features.scheduling.title',
          DESCRIPTION: 'homePage.splashScreen.features.scheduling.description'
        },
        OnePlace: {
          TITLE: 'homePage.splashScreen.features.onePlace.title',
          DESCRIPTION: 'homePage.splashScreen.features.onePlace.description'
        },
        IntegratedSupport: {
          TITLE: 'homePage.splashScreen.features.integratedSupport.title',
          DESCRIPTION: 'homePage.splashScreen.features.integratedSupport.description'
        }
      }
    },
    Sidebar: {
      RESOURCES_TITLE: 'homePage.sidebar.resourcesTitle',
      SERVICES_TITLE: 'homePage.sidebar.servicesTitle',
      Faqs: {
        TEXT: 'homePage.sidebar.faqs.text',
        URL: 'homePage.sidebar.faqs.url'
      },
      BillingPayment: {
        TEXT: 'homePage.sidebar.billingPayment.text',
        URL: 'homePage.sidebar.billingPayment.url'
      },
      DeliveryChecklist: {
        TEXT: 'homePage.sidebar.deliveryChecklist.text',
        URL: 'homePage.sidebar.deliveryChecklist.url'
      },
      PackingTips: {
        TEXT: 'homePage.sidebar.packingTips.text',
        URL: 'homePage.sidebar.packingTips.url'
      },
      PackingDosDonts: {
        TEXT: 'homePage.sidebar.packingDosDonts.text',
        URL: 'homePage.sidebar.packingDosDonts.url'
      },
      CarShipping: {
        TEXT: 'homePage.sidebar.carShipping.text',
        DESCRIPTION: 'homePage.sidebar.carShipping.description',
        URL: 'homePage.sidebar.carShipping.url'
      },
      PackingHelp: {
        TEXT: 'homePage.sidebar.packingHelp.text',
        DESCRIPTION: 'homePage.sidebar.packingHelp.description',
        URL: 'homePage.sidebar.packingHelp.url'
      },
      HomeDepot: {
        TITLE: 'homePage.sidebar.homeDepot.title',
        ICON_ALT_TEXT: 'homePage.sidebar.homeDepot.iconAltText',
        DESCRIPTION: 'homePage.sidebar.homeDepot.description',
        URL: 'homePage.sidebar.homeDepot.url'
      }
    },
    QuickLinks: {
      HEADER: 'homePage.quickLinks.header',
      AccountDetails: {
        TITLE: 'homePage.quickLinks.accountDetails.title',
        SUBTITLE: 'homePage.quickLinks.accountDetails.subtitle'
      },
      PaymentMethods: {
        TITLE: 'homePage.quickLinks.paymentMethods.title',
        SUBTITLE: 'homePage.quickLinks.paymentMethods.subtitle'
      },
      InvoicesAndStatements: {
        TITLE: 'homePage.quickLinks.invoicesAndStatements.title',
        SUBTITLE: 'homePage.quickLinks.invoicesAndStatements.subtitle'
      },
      MakeAPayment: {
        TITLE: 'homePage.quickLinks.makeAPayment.title',
        SUBTITLE: 'homePage.quickLinks.makeAPayment.subtitle'
      },
      Documents: {
        TITLE: 'homePage.quickLinks.documents.title',
        SUBTITLE: 'homePage.quickLinks.documents.subtitle'
      }
    }
  },
  Onboarding: {
    RentalAgreements: {
      HEADER: 'onboarding.rentalAgreements.header',
      SUBTITLE: 'onboarding.rentalAgreements.subtitle',
      RENTAL_AGREEMENT: 'onboarding.rentalAgreements.rentalAgreement',
      DECLINE_ERROR: 'onboarding.rentalAgreements.declineError'
    },
    SignRentalAgreements: {
      ORDER_NUMBER: 'onboarding.signRentalAgreements.order_number',
      HEADER: 'onboarding.signRentalAgreements.header',
      SUBTITLE: 'onboarding.signRentalAgreements.subtitle',
      VIEW_RENT_AGREEMENT: 'onboarding.signRentalAgreements.viewRentAgreement',
      LinkFlowAgreementViewer: {
        CHECKBOX_LABEL_START:
          'onboarding.signRentalAgreements.linkFlowAgreementViewer.checkboxLabelStart',
        RENTAL_AGREEMENT_LINK_TEXT:
          'onboarding.signRentalAgreements.linkFlowAgreementViewer.rentalAgreementLinkText',
        CHECKBOX_LABEL_END_WITH_ORDER_ID:
          'onboarding.signRentalAgreements.linkFlowAgreementViewer.checkboxLabelEndWithOrderId'
      }
    },
    ACCEPT_BUTTON_DESKTOP: 'onboarding.acceptButton_desktop',
    ACCEPT_BUTTON: 'onboarding.acceptButton',
    DECLINE_BUTTON: 'onboarding.declineButton',
    RETURN_TO_TASK_BUTTON: 'onboarding.returnToTaskButton'
  },
  BillingPage: {
    HEADER: 'billingPage.header',
    TOTAL_ACCOUNT_BALANCE: 'billingPage.totalAccountBalance',
    TOTAL_ACCOUNT_CREDIT: 'billingPage.totalAccountCredit',
    MAKE_PAYMENT_LINK: 'billingPage.makePaymentLink',
    INVOICE: 'billingPage.invoice',
    STATUS: 'billingPage.status',
    LOADING: 'billingPage.loading',
    PROCESSING_PAYMENT: 'billingPage.processingPayment',
    UpcomingPayments: {
      HEADER: 'billingPage.upcomingPayments.header'
    },
    PaymentHistory: {
      HEADER: 'billingPage.paymentHistory.header'
    },
    Statements: {
      HEADER: 'billingPage.statements.header',
      Statement: {
        TITLE: 'billingPage.statements.statement.title'
      }
    },
    PaymentMethods: {
      DefaultPaymentMethod: {
        HEADER: 'billingPage.paymentMethods.defaultPaymentMethod.header',
        NOT_FOUND: 'billingPage.paymentMethods.defaultPaymentMethod.notFound',
        VIEW_PAYMENT_METHODS_LINK:
          'billingPage.paymentMethods.defaultPaymentMethod.viewPaymentMethodsLink'
      }
    },
    BillingFaqCard: {
      COMBINED_TITLE: 'billingPage.billingFaqCard.combinedTitle',
      BILLING_TITLE: 'billingPage.billingFaqCard.billingTitle',
      LOOKING_FOR_MORE_DETAILS_TITLE: 'billingPage.billingFaqCard.lookingForMoreDetailsTitle',
      LOOKING_FOR_MORE_DETAILS_DESC: 'billingPage.billingFaqCard.lookingForMoreDetailsDesc',
      STATEMENT_INFO_ALERT_TITLE: 'billingPage.billingFaqCard.statementInfoAlertTitle',
      STATEMENT_INFO_ALERT_DESC: 'billingPage.billingFaqCard.statementInfoAlertDesc'
    }
  },
  CustomStatementPage: {
    HEADER: 'customStatementPage.header',
    SUBTITLE: 'customStatementPage.subtitle',
    LINK: 'customStatementPage.link',
    BUTTON: 'customStatementPage.button',
    DatePickers: {
      START_DATE: 'customStatementPage.datePickers.startDate',
      END_DATE: 'customStatementPage.datePickers.endDate'
    },
    GeneratedStatements: {
      HEADER: 'customStatementPage.generatedStatements.header',
      EMPTY: 'customStatementPage.generatedStatements.empty'
    },
    ErrorMessages: {
      NOT_FOUND: 'customStatementPage.errorMessages.notFound',
      INVALID_DATES_TITLE: 'customStatementPage.errorMessages.invalidDatesTitle',
      INVALID_DATES_DESCRIPTION: 'customStatementPage.errorMessages.invalidDatesDescription'
    }
  },
  MakePaymentsPage: {
    HEADER: 'makePaymentsPage.header',
    SUBTITLE: 'makePaymentsPage.subtitle',
    INVOICE_PREFIX: 'makePaymentsPage.invoicePrefix',
    SelectInvoice: {
      SUBTITLE: 'makePaymentsPage.selectInvoice.subtitle',
      QUESTION: 'makePaymentsPage.selectInvoice.question',
      QUESTION_SUBTEXT: 'makePaymentsPage.selectInvoice.questionSubtext',
      ALL_INVOICES: 'makePaymentsPage.selectInvoice.allInvoices',
      DUE_ON_PREFIX: 'makePaymentsPage.selectInvoice.dueOnPrefix'
    },
    TOTAL_LABEL: 'makePaymentsPage.totalLabel',
    INPUT_LABEL: 'makePaymentsPage.inputLabel',
    PAYMENT_AMOUNT_DISCLAIMER: 'makePaymentsPage.paymentAmountDisclaimer',
    SELECT_LABEL: 'makePaymentsPage.selectLabel',
    SELECT_METHOD_LOADING_TEXT: 'makePaymentsPage.selectMethodLoadingText',
    ADD_PAYMENT_METHOD_LINK: 'makePaymentsPage.addPaymentMethodLink',
    SUBMIT_BUTTON: 'makePaymentsPage.submitButton',
    SUBMIT_BUTTON_SEVEN: 'makePaymentsPage.submitButtonSeven',
    SUBMIT_BUTTON_FOURTEEN: 'makePaymentsPage.submitButtonFourteen',
    RadioOptions: {
      FULL_AMOUNT: 'makePaymentsPage.radioOptions.fullAmount',
      CUSTOM_AMOUNT: 'makePaymentsPage.radioOptions.customAmount'
    },
    SELECT_OPTION_LABEL: 'makePaymentsPage.selectOptionLabel',
    Notifications: {
      SUCCESS: 'makePaymentsPage.notifications.success'
    },
    Error: {
      EXCEEDS_TOTAL_AMOUNT_DUE: 'makePaymentsPage.error.exceedsTotalAmountDue',
      Declined: {
        NO_BALANCE_REMAINING: 'makePaymentsPage.error.declined.noBalanceRemaining',
        FRAUD_SUSPECTED: 'makePaymentsPage.error.declined.fraudSuspected',
        CARD_CLOSED: 'makePaymentsPage.error.declined.cardClosed',
        INACTIVE_CARD: 'makePaymentsPage.error.declined.inactiveCard',
        INVALID_ACCOUNT: 'makePaymentsPage.error.declined.invalidAccount',
        INSUFFICIENT_FUNDS: 'makePaymentsPage.error.declined.insufficientFunds',
        PROCESSOR_DECLINED: 'makePaymentsPage.error.declined.processorDeclined',
        CARD_ISSUER_DECLINED: 'makePaymentsPage.error.declined.cardIssuerDeclined',
        BANK_PAYMENT_UNAUTHORISED: 'makePaymentsPage.error.declined.bankPaymentUnauthorised',
        PAYPAL_ACCOUNT_ISSUE: 'makePaymentsPage.error.declined.paypalAccountIssue',
        LIMIT_EXCEEDED: 'makePaymentsPage.error.declined.limitExceeded',
        DECLINED: 'makePaymentsPage.error.declined.declined'
      },
      Warning: {
        TITLE: 'makePaymentsPage.error.warning.title',
        BODY: 'makePaymentsPage.error.warning.body'
      }
    }
  },
  PaymentMethodsPage: {
    HEADER: 'paymentMethodsPage.header',
    ADD_PAYMENT_METHOD_BUTTON: 'paymentMethodsPage.addPaymentMethodButton',
    MANAGE_LOAN: 'paymentMethodsPage.manageLoan',
    MAKE_DEFAULT: 'paymentMethodsPage.makeDefault',
    INFO_ICON_TEXT: 'paymentMethodsPage.infoIconText',
    DEFAULT: 'paymentMethodsPage.default',
    PaymentType: {
      PERSONAL_LOAN: 'paymentMethodsPage.paymentType.personalLoan',
      CREDIT_CARD: 'paymentMethodsPage.paymentType.creditCard',
      PAYPAL: 'paymentMethodsPage.paymentType.paypal',
      FINANCING: 'paymentMethodsPage.paymentType.financing',
      MOVE_LOAN_UPGRADE_TEXT: 'paymentMethodsPage.paymentType.moveLoanUpgradeText',
      MOVE_LOAN_CITI_BANK_TEXT: 'paymentMethodsPage.paymentType.moveLoanCitiBankText'
    },
    CallToChangeModal: {
      TITLE: 'paymentMethodsPage.callToChangeModal.title',
      BODY: 'paymentMethodsPage.callToChangeModal.body',
      Buttons: {
        CALL_US: 'paymentMethodsPage.callToChangeModal.buttons.callUs',
        CLOSE: 'paymentMethodsPage.callToChangeModal.buttons.close'
      }
    },
    Messages: {
      SUCCESS: 'paymentMethodsPage.messages.success',
      PAYMENT_METHOD_ISSUE: 'paymentMethodsPage.messages.paymentMethodIssue'
    }
  },
  ManagePaymentMethodsPage: {
    TITLE: 'managePaymentMethodsPage.title',
    CARD_FORM_TITLE: 'managePaymentMethodsPage.cardFormTitle',
    BILLING_ADDRESS_TITLE: 'managePaymentMethodsPage.billingAddressTitle',
    BILLING_ADDRESS_CHECKBOX_LABEL: 'managePaymentMethodsPage.billingAddressCheckboxLabel',
    BILLING_ADDRESS_SUBTITLE: 'managePaymentMethodsPage.billingAddressSubtitle',
    SAVE_BUTTON: 'managePaymentMethodsPage.saveButton',
    OTHER_WAYS_TO_PAY: 'managePaymentMethodsPage.otherWaysToPay',
    OR: 'managePaymentMethodsPage.or',
    PaymentInfo: {
      CardholderName: {
        LABEL: 'managePaymentMethodsPage.paymentInfo.cardholderName.label',
        PLACEHOLDER: 'managePaymentMethodsPage.paymentInfo.cardholderName.placeholder'
      },
      CardNumber: {
        LABEL: 'managePaymentMethodsPage.paymentInfo.cardNumber.label',
        PLACEHOLDER: 'managePaymentMethodsPage.paymentInfo.cardNumber.placeholder'
      },
      ExpirationDate: {
        LABEL: 'managePaymentMethodsPage.paymentInfo.expirationDate.label',
        PLACEHOLDER: 'managePaymentMethodsPage.paymentInfo.expirationDate.placeholder'
      },
      Cvv: {
        LABEL: 'managePaymentMethodsPage.paymentInfo.cvv.label',
        PLACEHOLDER: 'managePaymentMethodsPage.paymentInfo.cvv.placeholder'
      }
    },
    NO_BILLING_ADDRESS_FOUND: 'managePaymentMethodsPage.noBillingAddressFound',
    GO_TO_ACCOUNT_PAGE: 'managePaymentMethodsPage.goToAccountPage',
    GO_TO_ACCOUNT_PAGE_LABEL: 'managePaymentMethodsPage.goToAccountPageLabel',
    Errors: {
      PROCESSING_PAYMENT: 'managePaymentMethodsPage.errors.processingPayment',
      ALL_FIELDS_EMPTY: 'managePaymentMethodsPage.errors.allFieldsEmpty',
      CARDHOLDER_NAME_INVALID: 'managePaymentMethodsPage.errors.cardholderNameInvalid',
      CREDIT_CARD_INVALID: 'managePaymentMethodsPage.errors.creditCardInvalid',
      EXPIRATION_INVALID: 'managePaymentMethodsPage.errors.expirationInvalid',
      CVV_INVALID: 'managePaymentMethodsPage.errors.cvvInvalid',
      ADDRESS_INVALID: 'managePaymentMethodsPage.errors.addressInvalid',
      PAYPAL_CLOSED_UNEXPECTEDLY: 'managePaymentMethodsPage.errors.paypalClosedUnexpectedly'
    }
  },
  DocumentsPage: {
    HEADER: 'documentsPage.header',
    SUBTITLE: 'documentsPage.subtitle',
    Types: {
      ACH_AUTOPAY_AUTHORIZATION_FORM: 'documentsPage.types.ACH_AUTOPAY_AUTHORIZATION_FORM',
      ANCILLARY_ITEM_DOCUMENTATION: 'documentsPage.types.ANCILLARY_ITEM_DOCUMENTATION',
      AUCTION_PAPERWORK: 'documentsPage.types.AUCTION_PAPERWORK',
      BAD_DEBT_PAPERWORK: 'documentsPage.types.BAD_DEBT_PAPERWORK',
      BANKRUPTCY_PAPERWORK: 'documentsPage.types.BANKRUPTCY_PAPERWORK',
      BOOKING_CONFIRMATION: 'documentsPage.types.BOOKING_CONFIRMATION',
      CBP_FORM_7533_INWARD_CARGO: 'documentsPage.types.CBP_FORM_7533_INWARD_CARGO',
      CREDIT_CARD_AUTHORIZATION: 'documentsPage.types.CREDIT_CARD_AUTHORIZATION',
      CREDIT_CHECK_FORM: 'documentsPage.types.CREDIT_CHECK_FORM',
      CREDIT_CARD_CHARGEBACKS: 'documentsPage.types.CREDIT_CARD_CHARGEBACKS',
      CERTIFICATE_OF_INSURANCE: 'documentsPage.types.CERTIFICATE_OF_INSURANCE',
      RENTAL_AGREEMENT: 'documentsPage.types.RENTAL_AGREEMENT',
      IF_RENTAL_AGREEMENT: 'documentsPage.types.IF_RENTAL_AGREEMENT',
      LOCAL_RENTAL_AGREEMENT: 'documentsPage.types.LOCAL_RENTAL_AGREEMENT',
      IF_RENTAL_AGREEMENT_ELECTRONIC_ACCEPTANCE:
        'documentsPage.types.IF_RENTAL_AGREEMENT_ELECTRONIC_ACCEPTANCE',
      LOCAL_RENTAL_AGREEMENT_ELECTRONIC_ACCEPTANCE:
        'documentsPage.types.LOCAL_RENTAL_AGREEMENT_ELECTRONIC_ACCEPTANCE',
      CUSTOMER_INCIDENT_NOTE: 'documentsPage.types.CUSTOMER_INCIDENT_NOTE',
      DAMAGE_WAIVERS_AND_RELEASES: 'documentsPage.types.DAMAGE_WAIVERS_AND_RELEASES',
      DAMAGE_PICTURES: 'documentsPage.types.DAMAGE_PICTURES',
      COPY_OF_DEATH_CERTIFICATE: 'documentsPage.types.COPY_OF_DEATH_CERTIFICATE',
      DEED_OR_LEASE_VISITOR: 'documentsPage.types.DEED_OR_LEASE_VISITOR',
      DEFAULT_ON_PAYMENT_LETTER: 'documentsPage.types.DEFAULT_ON_PAYMENT_LETTER',
      UNACCOMPANIED_ARTICLES: 'documentsPage.types.UNACCOMPANIED_ARTICLES',
      ADD_A_GENERAL_FORM_TO_CAPTURE_OTHER_FORMS:
        'documentsPage.types.ADD_A_GENERAL_FORM_TO_CAPTURE_OTHER_FORMS',
      SPONGY_MOTH_FORM: 'documentsPage.types.SPONGY_MOTH_FORM',
      HOUSEHOLD_GOODS_INVENTORY_LIST: 'documentsPage.types.HOUSEHOLD_GOODS_INVENTORY_LIST',
      IMAGE_FILE: 'documentsPage.types.IMAGE_FILE',
      INVENTORY_ACKNOWLEDGEMENT_OF_HAZARDOUS_MATERIALS:
        'documentsPage.types.INVENTORY_ACKNOWLEDGEMENT_OF_HAZARDOUS_MATERIALS',
      RETAIL_CUSTOMER_INVOICE: 'documentsPage.types.RETAIL_CUSTOMER_INVOICE',
      LEGAL_CORRESPONDENCE: 'documentsPage.types.LEGAL_CORRESPONDENCE',
      LEGAL_STATUS: 'documentsPage.types.LEGAL_STATUS',
      MASTER_RENTAL_AGREEMENT: 'documentsPage.types.MASTER_RENTAL_AGREEMENT',
      CERTIFIED_NAME_CHANGE: 'documentsPage.types.CERTIFIED_NAME_CHANGE',
      FRAGILE_AND_NON_PAVED_SURFACE_WAIVER:
        'documentsPage.types.FRAGILE_AND_NON_PAVED_SURFACE_WAIVER',
      GENERAL_NOTE_WITH_FILE_ATTACHMENT: 'documentsPage.types.GENERAL_NOTE_WITH_FILE_ATTACHMENT',
      GENERAL_NOTE_WITH_IMAGE_FILE_ATTACHMENT:
        'documentsPage.types.GENERAL_NOTE_WITH_IMAGE_FILE_ATTACHMENT',
      OCEAN_TRANSPORT_QUOTE: 'documentsPage.types.OCEAN_TRANSPORT_QUOTE',
      ORDER_CONFIRMATION: 'documentsPage.types.ORDER_CONFIRMATION',
      ORDER_UPDATE_CONFIRMATION: 'documentsPage.types.ORDER_UPDATE_CONFIRMATION',
      COPY_OF_PASSPORTS: 'documentsPage.types.COPY_OF_PASSPORTS',
      ATTACH_PDF_FILE: 'documentsPage.types.ATTACH_PDF_FILE',
      FORM_B4_A_PERSONAL_EFFECTS_ACCOUNTING:
        'documentsPage.types.FORM_B4A_PERSONAL_EFFECTS_ACCOUNTING',
      FORM_B4_E_PERSONAL_EFFECTS_ACCOUNTING_DOCUMENT:
        'documentsPage.types.FORM_B4E_PERSONAL_EFFECTS_ACCOUNTING_DOCUMENT',
      MONTHLY_STATEMENT_OR_INVOICE: 'documentsPage.types.MONTHLY_STATEMENT_OR_INVOICE',
      PLACEMENT_WAIVER: 'documentsPage.types.PLACEMENT_WAIVER',
      FORM_5291_CUSTOMS_POWER_OF_ATTORNEY:
        'documentsPage.types.FORM_5291_CUSTOMS_POWER_OF_ATTORNEY',
      PODS_CANADA_CUSTOMS_FORM: 'documentsPage.types.PODS_CANADA_CUSTOMS_FORM',
      PURPOSE_OF_THE_MOVE_AND_CONTACT_PHONE_NUMBER_DOCUMENT:
        'documentsPage.types.PURPOSE_OF_THE_MOVE_AND_CONTACT_PHONE_NUMBER_DOCUMENT',
      SETTLEMENT_AGREEMENT: 'documentsPage.types.SETTLEMENT_AGREEMENT',
      SPOTTED_LANTERN_FLY_FORM: 'documentsPage.types.SPOTTED_LANTERN_FLY_FORM',
      FORM_II_RC_159_SUPPLEMENTAL_DECLARATION:
        'documentsPage.types.FORM_II_RC_159_SUPPLEMENTAL_DECLARATION',
      DEED_OR_LEASE_VACATION: 'documentsPage.types.DEED_OR_LEASE_VACATION',
      W9: 'documentsPage.types.W9',
      COPY_OF_WILL_AND_OR_TRUST: 'documentsPage.types.COPY_OF_WILL_AND_OR_TRUST',
      MILITARY_WEIGHT_TICKET_EMPTY: 'documentsPage.types.MILITARY_WEIGHT_TICKET_EMPTY',
      MILITARY_WEIGHT_TICKET_EMPTY_WITH_TRUCK:
        'documentsPage.types.MILITARY_WEIGHT_TICKET_EMPTY_WITH_TRUCK',
      MILITARY_WEIGHT_TICKET_FULL: 'documentsPage.types.MILITARY_WEIGHT_TICKET_FULL',
      MILITARY_WEIGHT_TICKET_FULL_WITH_TRUCK:
        'documentsPage.types.MILITARY_WEIGHT_TICKET_FULL_WITH_TRUCK',
      UNKNOWN: 'documentsPage.types.UNKNOWN'
    }
  },
  Footer: {
    PRIVACY_POLICY: 'footer.privacyPolicy',
    PRIVACY_POLICY_URL: 'footer.privacyPolicyUrl',
    TERMS_AND_CONDITIONS: 'footer.termsAndConditions',
    TERMS_AND_CONDITIONS_URL: 'footer.termsAndConditionsUrl',
    COPYRIGHT: 'footer.copyright',
    FINANCING_DISCLAIMERS_TITLE: 'footer.financingDisclaimersTitle',
    FINANCING_DISCLAIMERS_ONE: 'footer.financingDisclaimersOne',
    FINANCING_DISCLAIMERS_TWO: 'footer.financingDisclaimersTwo',
    FINANCING_DISCLAIMERS_THREE: 'footer.financingDisclaimersThree',
    FINANCING_DISCLAIMERS_FOUR: 'footer.financingDisclaimersFour',
    OVER3000_FINANCING_TERMS: 'footer.over3000FinancingTerms',
    UNDER3000_FINANCING_TERMS: 'footer.under3000FinancingTerms',
    MANAGE_COOKIE_PREFERENCES: 'footer.manageCookiePreferences'
  },
  Language: {
    US_ENGLISH_LABEL: 'language.usEnglishLabel',
    CA_FRENCH_LABEL: 'language.caFrenchLabel',
    ACCESSIBILITY_LABEL: 'language.accessibilityLabel'
  },
  MothInspectionPage: {
    HEADER: 'mothInspectionPage.header',
    MULTI_PAGE_HEADER: 'mothInspectionPage.multi_page_header',
    SUBTITLE: 'mothInspectionPage.subtitle',
    MULTI_PAGE_SUBTITLE: 'mothInspectionPage.multi_page_subtitle',
    FOOTNOTE: 'mothInspectionPage.footnote',
    Cards: {
      TITLE: 'mothInspectionPage.cards.title',
      BODY_TEXT: 'mothInspectionPage.cards.bodyText'
    },
    Faq: {
      HEADER: 'mothInspectionPage.faq.header',
      SUBTITLE: 'mothInspectionPage.faq.subtitle',
      LINK_TITLE: 'mothInspectionPage.faq.linkTitle',
      PROMPT: 'mothInspectionPage.faq.prompt',
      ANSWER: 'mothInspectionPage.faq.answer'
    }
  },
  MothInspectionFormPage: {
    INSTRUCTIONS: 'mothInspectionFormPage.instructions',
    AGREE_BUTTON: 'mothInspectionFormPage.agreeButton',
    SUCCESS_MESSAGE: 'mothInspectionFormPage.successMessage',
    RESTRICTED_ITEM_MESSAGE: 'mothInspectionFormPage.restrictedItemMessage',
    INTRO_TITLE: 'mothInspectionFormPage.introTitle',
    INTRO_DESC: 'mothInspectionFormPage.introDesc',
    INTRO_INFO: 'mothInspectionFormPage.introInfo',
    INTRO_LEARN_MORE: 'mothInspectionFormPage.introLearnMore',
    LegalCopy: {
      TITLE: 'mothInspectionFormPage.legalCopy.title',
      SUBTITLE: 'mothInspectionFormPage.legalCopy.subtitle',
      DESCRIPTION: 'mothInspectionFormPage.legalCopy.description',
      IMAGE1_CAPTION: 'mothInspectionFormPage.legalCopy.Image1Caption',
      IMAGE2_CAPTION: 'mothInspectionFormPage.legalCopy.Image2Caption',
      RESTRICTED_ITEM_TITLE: 'mothInspectionFormPage.legalCopy.RestrictedItemTitle',
      RESTRICTED_ITEM_DETAILS: 'mothInspectionFormPage.legalCopy.RestrictedItemDetails'
    }
  },
  NotFoundPage: {
    TITLE: 'notFoundPage.title',
    BODY: 'notFoundPage.body',
    BUTTON: 'notFoundPage.button'
  },
  FnpsModal: {
    TITLE: 'fnpsModal.title',
    ACTION_BUTTON_LABEL: 'fnpsModal.actionButtonLabel',
    CONTAINER_LOCATION: 'fnpsModal.containerLocation',
    Body: {
      INTRO: 'fnpsModal.body.intro',
      CONDITIONS: 'fnpsModal.body.conditions',
      TERMS_CONFIRMATION: 'fnpsModal.body.termsConfirmation',
      CONTAINER_LOCATION: 'fnpsModal.body.containerLocation'
    },
    ALERT_TITLE: 'fnpsModal.alertTitle',
    ALERT_DESCRIPTION: 'fnpsModal.alertDescription'
  },
  LoadingScreens: {
    INITIAL: 'loadingScreens.initial',
    HOME_PAGE: 'loadingScreens.homePage'
  },
  TaskPage: {
    WelcomePanel: {
      TITLE: 'taskPage.welcomePanel.title',
      BODY: 'taskPage.welcomePanel.body'
    },
    OrderStatusCard: {
      MOVING: 'taskPage.orderStatusCard.moving',
      STORAGE: 'taskPage.orderStatusCard.storage',
      CONTAINERS: 'taskPage.orderStatusCard.containers',
      ORDER_NO: 'taskPage.orderStatusCard.orderNo'
    },
    ProgressBar: {
      TITLE: 'taskPage.progressBar.title',
      COMPLETED: 'taskPage.progressBar.completed'
    },
    RentalAgreement: {
      TITLE: 'taskPage.rentalAgreement.title',
      DESCRIPTION: 'taskPage.rentalAgreement.description'
    },
    InvasiveSpecies: {
      TITLE: 'taskPage.invasiveSpecies.title',
      DESCRIPTION: 'taskPage.invasiveSpecies.description'
    },
    Order: {
      TITLE: 'taskPage.order.title',
      DESCRIPTION: 'taskPage.order.description'
    },
    DUE_DATE: 'taskPage.dueDate',
    DONE: 'taskPage.done',
    REVIEW_AND_SIGN_BUTTON: 'taskPage.reviewAndSignButton'
  },
  PodsReady: {
    SetPasswordPage: {
      INTRO: 'podsReady.setPasswordPage.intro',
      SUBTITLE: 'podsReady.setPasswordPage.subtitle',
      TITLE: 'podsReady.setPasswordPage.title',
      BUTTON_TEXT: 'podsReady.setPasswordPage.buttonText',
      Notifications: {
        Title: {
          INVALID_PASSWORD: 'podsReady.setPasswordPage.notifications.title.invalidPassword',
          USERNAME_ALREADY_TAKEN:
            'podsReady.setPasswordPage.notifications.title.usernameAlreadyTaken',
          CUSTOMER_ID_TAKEN: 'podsReady.setPasswordPage.notifications.title.customerIdTaken',
          DEFAULT: 'podsReady.setPasswordPage.notifications.title.default'
        },
        Message: {
          INVALID_PASSWORD: 'podsReady.setPasswordPage.notifications.message.invalidPassword',
          USERNAME_ALREADY_TAKEN:
            'podsReady.setPasswordPage.notifications.message.usernameAlreadyTaken',
          CUSTOMER_ID_TAKEN: 'podsReady.setPasswordPage.notifications.message.customerIdTaken',
          DEFAULT: 'podsReady.setPasswordPage.notifications.message.default'
        }
      }
    },
    SuccessPage: {
      SUCCESS_MESSAGE: 'podsReady.successPage.successMessage',
      TITLE: 'podsReady.successPage.title',
      SUBTITLE: 'podsReady.successPage.subtitle',
      BUTTON_TEXT: 'podsReady.successPage.buttonText',
      ALT_TEXT: 'podsReady.successPage.altText'
    }
  }
};
