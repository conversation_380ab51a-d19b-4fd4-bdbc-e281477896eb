import { generatePath } from 'react-router';

export enum ROUTES {
  HOME = '/',
  ACCOUNT = '/account',
  DOCUMENT = '/document',
  BILLING = '/billing',
  CUSTOM_STATEMENT = '/billing/custom-statement',
  VIEW_PAYMENT_METHODS = '/billing/view-payment-methods',
  MANAGE_PAYMENT_METHOD = '/billing/manage-payment-method',
  MAKE_PAYMENT = '/billing/make-payment',
  MOTH_FLY_INSPECTION = '/moth-fly-inspection',
  MOTH_FLY_INSPECTION_FORM = '/moth-fly-inspection-form',
  CONTAINER = '/container/:containerId',
  DEBUG = '/debug',
  NOT_FOUND = '/not-found'
}

export const buildContainerRoute = (containerId: string) =>
  generatePath(ROUTES.CONTAINER, { containerId });
