import groupBy from 'lodash/groupBy';
import { format, subDays } from 'date-fns';
import { Address, CustomerType } from '../networkRequests/responseEntities/CustomerEntities';
import {
  ContainerSummary,
  ServiceType,
  CityState,
  SingleOrderStatus
} from '../networkRequests/responseEntities/PodsReadyEntities';
import { EnumAsUnion } from '../helpers/enum-as-union';

export interface Order {
  orderId: string;
  orderType: OrderType;
  quoteId: number;
  containers: Container[];
  orderDate?: Date;
  price: number;
  initialDeliveryPlacementIsReviewed: boolean;
}

export interface Container {
  containerId: string;
  containerOrderId?: string;
  containerSize: string;
  moveLegs: MoveLeg[];
  upNextMoveLegId?: string;
}

export interface MoveLeg {
  moveLegId: string;
  moveLegType: MoveLegType;
  scheduledDate?: Date;
  eta?: string;
  siteIdentity: string;
  displayAddress: MoveLegAddress;
  originationAddress: MoveLegAddress;
  destinationAddress: MoveLegAddress;
  isCityService: boolean;
  isHawaii: boolean;
  isCrossBorder: boolean;
  isUpNext: boolean;
  scheduledStatus: ScheduledStatus;
  serviceCountdownType: ServiceCountdownType;
  isTransitLeg: boolean;
  transitDays: number;
  isSchedulableOnline: boolean;
  containerVisitDate?: Date;
  firstAvailableDate: Date;
  lastAvailableDate: Date;
  arrivalDate?: Date;
  moveDate?: Date;
}

export interface MoveLegAddress extends Address {
  country: string;
}

export enum OrderType {
  UNSPECIFIED = 'UNSPECIFIED',
  LOCAL = 'LOCAL',
  IF = 'IF',
  IC = 'IC',
  ANCILLARY_ONLY = 'ANCILLARY_ONLY'
}

export enum MoveLegTypeEnum {
  INITIAL_DELIVERY = 'INITIAL_DELIVERY',
  SELF_INITIAL_DELIVERY = 'SELF_INITIAL_DELIVERY',
  PICKUP = 'PICKUP',
  MOVE = 'MOVE',
  VISIT_CONTAINER = 'VISIT_CONTAINER',
  REDELIVERY = 'REDELIVERY',
  WAREHOUSE_TO_WAREHOUSE = 'WAREHOUSE_TO_WAREHOUSE',
  SELF_FINAL_PICKUP = 'SELF_FINAL_PICKUP',
  FINAL_PICKUP = 'FINAL_PICKUP',
  CITY_SERVICE_DELIVERY = 'CITY_SERVICE_DELIVERY',
  CITY_SERVICE_RETURN = 'CITY_SERVICE_RETURN',
  CITY_SERVICE_REDELIVERY = 'CITY_SERVICE_REDELIVERY',
  CITY_SERVICE_FINAL_PICKUP = 'CITY_SERVICE_FINAL_PICKUP',
  LOCAL_PORT_TO_LOCAL_PORT = 'LOCAL_PORT_TO_LOCAL_PORT',
  LOCAL_PORT_TO_WAREHOUSE = 'LOCAL_PORT_TO_WAREHOUSE',
  WEIGHT_TICKET_EMPTY = 'WEIGHT_TICKET_EMPTY',
  WEIGHT_TICKET_FULL = 'WEIGHT_TICKET_FULL',
  PORT_TO_PORT = 'PORT_TO_PORT',
  PORT_TO_WAREHOUSE = 'PORT_TO_WAREHOUSE',
  UNKNOWN = 'UNKNOWN',
  VISIT_LOCKOUT = 'VISIT_LOCKOUT',
  VISIT_OTHER = 'VISIT_OTHER',
  VISIT_REMOVE_LOCK = 'VISIT_REMOVE_LOCK',
  VISIT_REPOSITION_LEG = 'VISIT_REPOSITION_LEG',
  STORAGE_CENTER_RELOCATION = 'STORAGE_CENTER_RELOCATION',
  WAREHOUSE_TO_LOCAL_PORT = 'WAREHOUSE_TO_LOCAL_PORT',
  STORAGE_CENTER_TO_PORT = 'STORAGE_CENTER_TO_PORT'
}

export type MoveLegType = EnumAsUnion<typeof MoveLegTypeEnum>;

// export type ServiceCountdownType = 'ARRIVAL' | 'PICKUP' | 'DELIVERY' | 'MOVE' | 'VISIT';
export type ServiceCountdownType =
  | 'DELIVERY'
  | 'PICKUP'
  | 'DROPOFF'
  | 'MOVE'
  | 'RETURN'
  | 'SELFDELIVERY'
  | 'SELFPICKUP'
  | 'NONE';

export type ScheduledStatus = 'FUTURE' | 'UNSCHEDULED' | 'PAST';

export type MoveLegSchedulingType = 'PICKUP' | 'DROPOFF' | 'VISIT' | 'MOVE' | 'NONE';
export const serviceCountdownTypeForMoveLegType = (moveLegType: MoveLegType) => {
  // TODO: will update delivery and visit legs as part of those stories.
  switch (moveLegType) {
    case 'PICKUP':
    case 'FINAL_PICKUP':
    case 'INITIAL_DELIVERY':
      return 'PICKUP';
    case 'VISIT_CONTAINER':
      return 'VISIT';
    case 'REDELIVERY':
      return 'DROPOFF';
    case 'MOVE':
      return 'MOVE';
    default:
      return 'NONE';
  }
};
export interface UpdateMoveLegRequest {
  orderId: string;
  salesforceQuoteId?: string;
  containerOrderId?: string;
  quoteId?: string;
  moveLegId: string;
  moveLegType: MoveLegType;
  requestedDate: string | null;
  transitDays: number;
  isCancelLeg: boolean;
  serviceAddress?: ServiceAddress;
  containerPlacement?: ContainerPlacement;
  locationFields: ContainerAvailabilityLocationFields;
}

export interface ContainerAvailabilityLocationFields {
  zip: String;
  moveLegType: MoveLegType;
  orderType: OrderType;
  siteIdentity: String;
  isIfOpenCalendar: Boolean;
  customerType?: CustomerType;
  sessionId?: string; // correlation id
  custTrackingId?: string; // split visitor id
}

export interface ApplyQuoteToOrderRequest {
  sfQuoteId: string;
}

export type ContainerPlacement = {
  isPavedSurface: boolean;
  siteType: ContainerPlacementSiteType;
  placement: FinalContainerPlacement;
  driverNotes: string;
};

export type ContainerPlacementSiteType = 'DRIVEWAY' | 'STREET' | 'PARKING_LOT';

export type DrivewayStraightClose = 'DRIVEWAY_STRAIGHT_CLOSE_REAR' | 'DRIVEWAY_STRAIGHT_CLOSE_CAB';
export type DrivewayStraightMiddle =
  | 'DRIVEWAY_STRAIGHT_MIDDLE_REAR'
  | 'DRIVEWAY_STRAIGHT_MIDDLE_CAB';
export type DrivewayStraightFar = 'DRIVEWAY_STRAIGHT_FAR_REAR' | 'DRIVEWAY_STRAIGHT_FAR_CAB';
export type DrivewayCircularClose = 'DRIVEWAY_CIRCULAR_CLOSE_REAR' | 'DRIVEWAY_CIRCULAR_CLOSE_CAB';
export type DrivewayCircularFar = 'DRIVEWAY_CIRCULAR_FAR_REAR' | 'DRIVEWAY_CIRCULAR_FAR_CAB';
export type DrivewayLShapedClose = 'DRIVEWAY_L_SHAPED_CLOSE_REAR' | 'DRIVEWAY_L_SHAPED_CLOSE_CAB';
export type DrivewayLShapedMiddle =
  | 'DRIVEWAY_L_SHAPED_MIDDLE_REAR'
  | 'DRIVEWAY_L_SHAPED_MIDDLE_CAB';
export type DrivewayLShapedFar = 'DRIVEWAY_L_SHAPED_FAR_REAR' | 'DRIVEWAY_L_SHAPED_FAR_CAB';
export type StreetAlleyway =
  | 'STREET_LEFT_CAB'
  | 'STREET_FRONT_CAB'
  | 'STREET_RIGHT_CAB'
  | 'STREET_BACK_CAB';
export type ParkingLotTypeFrontScreens =
  | 'PARKING_LOT_FRONT_01'
  | 'PARKING_LOT_FRONT_02'
  | 'PARKING_LOT_FRONT_03'
  | 'PARKING_LOT_FRONT_04';

export type ParkingLotTypeRightScreens =
  | 'PARKING_LOT_RIGHT_01'
  | 'PARKING_LOT_RIGHT_02'
  | 'PARKING_LOT_RIGHT_03'
  | 'PARKING_LOT_RIGHT_04';

export type ParkingLotTypeBackScreens =
  | 'PARKING_LOT_BACK_01'
  | 'PARKING_LOT_BACK_02'
  | 'PARKING_LOT_BACK_03'
  | 'PARKING_LOT_BACK_04';

export type ParkingLotTypeLeftScreens =
  | 'PARKING_LOT_LEFT_01'
  | 'PARKING_LOT_LEFT_02'
  | 'PARKING_LOT_LEFT_03'
  | 'PARKING_LOT_LEFT_04';

export type FinalContainerPlacement =
  | DrivewayStraightClose
  | DrivewayStraightMiddle
  | DrivewayStraightFar
  | DrivewayCircularClose
  | DrivewayCircularFar
  | DrivewayLShapedClose
  | DrivewayLShapedMiddle
  | DrivewayLShapedFar
  | StreetAlleyway
  | ParkingLotTypeFrontScreens
  | ParkingLotTypeLeftScreens
  | ParkingLotTypeRightScreens
  | ParkingLotTypeBackScreens;

export interface ServiceAddress {
  address1: string;
  address2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
}

export interface SameServiceAreaRequest {
  originalAddress: ServiceAddress;
  updatedAddress: ServiceAddress;
}

export interface CloneQuoteFromOrderRequest {
  orderId: string;
}

export interface CloneQuoteFromOrderResponse {
  newQuoteIdentity: string;
  newQuoteSalesforceId: string;
}

// -- Getters --
const initialMoveLeg = (container: Container): MoveLeg => {
  const { moveLegs } = container;
  const initialMoveLegTypes = [
    MoveLegTypeEnum.INITIAL_DELIVERY.toString(),
    MoveLegTypeEnum.SELF_INITIAL_DELIVERY.toString()
  ];
  return (
    moveLegs.find((moveLeg) => initialMoveLegTypes.includes(moveLeg.moveLegType)) ?? moveLegs[0]
  );
};

export const lastMoveLeg = (container: Container): MoveLeg => {
  const { moveLegs } = container;
  return moveLegs[moveLegs.length - 1];
};

export const getUpNextMoveLeg = (container: Container) => {
  const { upNextMoveLegId } = container;
  return container.moveLegs.find((moveLeg) => moveLeg.moveLegId === upNextMoveLegId);
};

const determineServiceOption = (firstContainer: Container): ServiceType => {
  const initialOriginZip = initialMoveLeg(firstContainer).destinationAddress.postalCode;
  const finalDestinationZip = lastMoveLeg(firstContainer).originationAddress.postalCode;
  return initialOriginZip === finalDestinationZip ? 'STORAGE' : 'MOVING';
};

// -- orderSummary mapping --
const getContainerSummaries = (order: Order): ContainerSummary[] => {
  const groupedContainers = groupBy(order.containers, 'containerSize');
  return Object.entries(groupedContainers).map(([key, value]) => ({
    containerSize: key.toString(),
    count: value.length
  }));
};

const shortOriginAddress = (firstContainer: Container): CityState => {
  const address = initialMoveLeg(firstContainer).destinationAddress;
  return { city: address.city, state: address.state };
};

const shortDestinationAddress = (firstContainer: Container): CityState => {
  const address = lastMoveLeg(firstContainer).originationAddress;
  return { city: address.city, state: address.state };
};

const formatScheduledDate = (firstContainer: Container): string | undefined => {
  const moveLeg = initialMoveLeg(firstContainer);
  return moveLeg?.scheduledDate!! ? format(moveLeg.scheduledDate, 'LLL d') : undefined;
};

export function orderToOrderStatus(order: Order): SingleOrderStatus {
  const firstContainer = order.containers[0];

  return {
    service: determineServiceOption(firstContainer),
    origin: shortOriginAddress(firstContainer),
    destination: shortDestinationAddress(firstContainer),
    scheduledDate: formatScheduledDate(firstContainer),
    orderId: order.orderId,
    containers: getContainerSummaries(order)
  };
}

export const rentalAgreementDueDate = (order: Order): Date | undefined => {
  const moveLeg = initialMoveLeg(order.containers[0]);
  return moveLeg?.scheduledDate!! ? subDays(moveLeg?.scheduledDate, 2) : undefined;
};
