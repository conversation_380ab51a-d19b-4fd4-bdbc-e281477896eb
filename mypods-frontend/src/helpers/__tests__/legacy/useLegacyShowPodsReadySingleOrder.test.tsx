import { renderHook, waitFor } from '@testing-library/react';
import { QueryClientProvider } from '@tanstack/react-query';
import { ReactNode, Suspense } from 'react';
import { useLegacyShowPodsReadySingleOrder } from '../../legacy/useLegacyShowPodsReadySingleOrder';
import { runPendingPromises, testQueryClient } from '../../../testUtils/RenderHelpers';
import {
  createEntryPointResult,
  createOutstandingMothAgreement,
  createOutstandingRentalAgreement,
  mockIsPodsReadySingleOrderEnabled
} from '../../../testUtils/MyPodsFactories';
import {
  mockLegacyAuthorizationEntrypoint,
  mockLegacyGetRentalAgreement,
  mockLegacyPodsReadyAuthorizationEntrypoint
} from '../../../../setupTests';
import { EntryPointProvider } from '../../../context/EntryPointContext';

const mockOrderHasSignedMothAgreement = vi.hoisted(() => vi.fn());
vi.mock('../../../helpers/storageHelpers', () => ({
  orderHasSignedMothAgreement: mockOrderHasSignedMothAgreement
}));

describe('useLegacyShowPodsReadySingleOrder', () => {
  const singleRentalAgreement = createEntryPointResult({
    outstandingRentalAgreements: [createOutstandingRentalAgreement()]
  });
  const multiOrderDocuments = createEntryPointResult({
    outstandingRentalAgreements: [createOutstandingRentalAgreement({ orderId: '1' })],
    outstandingMothAgreements: [createOutstandingMothAgreement({ orderId: '2' })]
  });
  const multiDocumentsFromSameOrder = createEntryPointResult({
    outstandingRentalAgreements: [createOutstandingRentalAgreement({ orderId: '1' })],
    outstandingMothAgreements: [createOutstandingMothAgreement({ orderId: '1' })]
  });
  const justMothFormDocuments = createEntryPointResult({
    outstandingMothAgreements: [createOutstandingMothAgreement({ orderId: '1' })]
  });

  const noDocumentOrders = createEntryPointResult();

  const renderSubjectHook = async () => {
    const wrapper = ({ children }: { children: ReactNode }) => (
      <QueryClientProvider client={testQueryClient()}>
        <EntryPointProvider>
          <Suspense fallback={'SUSPENSE_FALLBACK'}>{children}</Suspense>
        </EntryPointProvider>
      </QueryClientProvider>
    );

    const { result } = renderHook(() => useLegacyShowPodsReadySingleOrder(), { wrapper });
    await runPendingPromises();
    return result.current;
  };

  beforeEach(() => {
    mockLegacyGetRentalAgreement.mockResolvedValue([]);
  });

  afterEach(() => {
    localStorage.clear();
  });

  it('never calls mockLegacyPodsReadyAuthorizationEntrypoint', async () => {
    mockLegacyAuthorizationEntrypoint.mockReturnValue({
      isSuccess: true,
      ...singleRentalAgreement
    });

    await renderSubjectHook();

    expect(mockLegacyPodsReadyAuthorizationEntrypoint).not.toHaveBeenCalled();
  });

  describe('isPodsReadySingleOrderEnabled true and', () => {
    beforeEach(() => {
      mockIsPodsReadySingleOrderEnabled.mockReturnValue(true);
    });

    it('has one outstanding rental agreement returns true', async () => {
      mockLegacyAuthorizationEntrypoint.mockReturnValue({
        isSuccess: true,
        ...singleRentalAgreement
      });

      await waitFor(async () => {
        expect(await renderSubjectHook()).toEqual({
          isFetching: false,
          showPodsReadySingleOrder: true
        });
      });
    });

    it('has one outstanding moth agreement marked signed in local storage -- returns false', async () => {
      mockOrderHasSignedMothAgreement.mockReturnValue(true);
      mockLegacyAuthorizationEntrypoint.mockReturnValue({
        isSuccess: true,
        ...justMothFormDocuments
      });

      await waitFor(async () => {
        expect(await renderSubjectHook()).toEqual({
          isFetching: false,
          showPodsReadySingleOrder: false
        });
      });
    });

    it('has more than one outstanding order document returns false', async () => {
      mockLegacyAuthorizationEntrypoint.mockReturnValue({
        isSuccess: true,
        ...multiOrderDocuments
      });

      await waitFor(async () => {
        expect(await renderSubjectHook()).toEqual({
          isFetching: false,
          showPodsReadySingleOrder: false
        });
      });
    });

    it('has more than order doc but all from the same doc return true', async () => {
      mockLegacyAuthorizationEntrypoint.mockReturnValue({
        isSuccess: true,
        ...multiDocumentsFromSameOrder
      });

      await waitFor(async () => {
        expect(await renderSubjectHook()).toEqual({
          isFetching: false,
          showPodsReadySingleOrder: true
        });
      });
    });

    it('has only moth documents returns true', async () => {
      mockLegacyAuthorizationEntrypoint.mockReturnValue({
        isSuccess: true,
        ...justMothFormDocuments
      });

      await waitFor(async () => {
        expect(await renderSubjectHook()).toEqual({
          isFetching: false,
          showPodsReadySingleOrder: true
        });
      });
    });

    it('has only previously signed moth documents returns false', async () => {
      mockOrderHasSignedMothAgreement.mockReturnValue(true);
      mockLegacyAuthorizationEntrypoint.mockReturnValue({
        isSuccess: true,
        ...justMothFormDocuments
      });

      await waitFor(async () => {
        expect(await renderSubjectHook()).toEqual({
          isFetching: false,
          showPodsReadySingleOrder: false
        });
      });
    });

    it('has no documents returns false', async () => {
      mockLegacyAuthorizationEntrypoint.mockReturnValue({ isSuccess: true, ...noDocumentOrders });

      await waitFor(async () => {
        expect(await renderSubjectHook()).toEqual({
          isFetching: false,
          showPodsReadySingleOrder: false
        });
      });
    });
  });

  describe('isPodsReadySingleOrderEnabled false and', () => {
    beforeEach(() => {
      mockIsPodsReadySingleOrderEnabled.mockReturnValue(false);
    });

    it('has outstanding document returns false', async () => {
      mockLegacyAuthorizationEntrypoint.mockReturnValue({
        isSuccess: true,
        ...singleRentalAgreement
      });

      await waitFor(async () => {
        expect(await renderSubjectHook()).toEqual({
          isFetching: false,
          showPodsReadySingleOrder: false
        });
      });
    });

    it('has no documents returns false', async () => {
      mockLegacyAuthorizationEntrypoint.mockReturnValue({ isSuccess: true, ...noDocumentOrders });

      await waitFor(async () => {
        expect(await renderSubjectHook()).toEqual({
          isFetching: false,
          showPodsReadySingleOrder: false
        });
      });
    });
  });
});
