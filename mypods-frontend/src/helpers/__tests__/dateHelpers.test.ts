import {
  formatDate,
  formatToLocale,
  formatETA,
  getDaysBetween,
  getDaysUntil,
  getHoursBetween
} from '../dateHelpers';
import { addDays } from 'date-fns';

describe('dateHelper', () => {
  describe('getDaysUntil', () => {
    it.each([
      [addDays(new Date(), 3), 3],
      [addDays(new Date(), 0), 0],
      [addDays(new Date(), -3), null],
      [undefined, null]
    ])('for value "%s" should return "%s"', (checkDate, expected) => {
      expect(getDaysUntil(checkDate)).toEqual(expected);
    });
  });

  describe('getDaysBetween', () => {
    it.each([
      [3, '2024-05-10'],
      [1, '2024-05-08'],
      [0, '2024-05-07'],
      [-1, '2024-05-06'],
      [-2, '2024-05-05']
    ])('should return "%s" for count of days between "2024-05-07" and "%s"', (output, input) => {
      const start = '2024-05-07';
      expect(getDaysBetween(new Date(start), new Date(input))).toEqual(output);
    });
  });

  describe('getHoursBetween', () => {
    it.each([
      [72, '2024-05-10'],
      [24, '2024-05-08'],
      [0, '2024-05-07'],
      [-24, '2024-05-06'],
      [-48, '2024-05-05']
    ])('should return "%s" for count of hours between "2024-05-07" and "%s"', (output, input) => {
      const start = '2024-05-07';
      expect(getHoursBetween(new Date(start), new Date(input))).toEqual(output);
    });
  });

  describe('formatDate', () => {
    it.each([['2050-01-01T00:00:00', 'M/d/yy', '1/1/50']])(
      'should format "%s" using "%s" to "%s"',
      (date, format, expected) => {
        expect(formatDate(new Date(date), format)).toBe(expected);
      }
    );
  });

  describe('formatToLocale', () => {
    it.each([[new Date('2050-01-01T00:00:00'), 'January 1, 2050']])(
      'should format "%s" to "%s"',
      (date, expected) => {
        expect(formatToLocale(date)).toBe(expected);
      }
    );
  });

  describe('formatETA', () => {
    it.each([
      ['07:30 AM - 03:00 PM', '7:30AM to 3PM'],
      ['  07:30 AM  -03:00 PM ', '7:30AM to 3PM'],
      ['12:00 AM - 12:00 PM', '12AM to 12PM'],
      ['07:00 AM - 03:00 PM', '7AM to 3PM'],
      [undefined, ''],
      ['', '']
    ])('formatTimeRange should format date properly for %s time range', (input, expectedOutput) => {
      expect(formatETA(input)).toEqual(expectedOutput);
    });

    it('should return an empty string if the eta is a full warehouse hours json', () => {
      expect(formatETA('{"monday": {"startTime": "07:30 AM", "endTime": "03:00 PM"}}')).toEqual('');
    });
    it('should return an empty string if the eta is null', () => {
      expect(formatETA(undefined)).toEqual('');
    });
  });
});
