import { QueryClientProvider } from '@tanstack/react-query';
import { runPendingPromises, testQueryClient } from '../../testUtils/RenderHelpers';
import { ReactNode, Suspense } from 'react';
import { renderHook } from '@testing-library/react';
import {
  MY_PODS_INTRO_SPLASH_SCREEN_SEEN_KEY,
  useShowMyPodsIntroSplashScreen
} from '../useShowMyPodsIntroSplashScreen';
import { mockIsMyPodsIntroSplashScreenEnabled } from '../../testUtils/MyPodsFactories';

describe('useShowMyPodsIntroSplashScreen', () => {
  const renderUseShowMyPodsIntroSplashScreen = async (setAlreadyShown: boolean = false) => {
    const wrapper = ({ children }: { children: ReactNode }) => (
      <QueryClientProvider client={testQueryClient()}>
        <Suspense fallback={'SUSPENSE_FALLBACK'}>{children}</Suspense>
      </QueryClientProvider>
    );
    const { result } = renderHook(() => useShowMyPodsIntroSplashScreen(), { wrapper });
    if (setAlreadyShown) {
      result.current.setShown();
    }
    await runPendingPromises();
    return result.current.useShouldShow();
  };

  const renderSetShown = async () => {
    await renderUseShowMyPodsIntroSplashScreen(true);
  };

  describe('when isMyPodsIntroSplashScreenEnabled true', () => {
    beforeEach(() => {
      mockIsMyPodsIntroSplashScreenEnabled.mockReturnValue(true);
    });

    it('and user has never viewed mypods splash screen before should return true', async () => {
      const actual = await renderUseShowMyPodsIntroSplashScreen();

      expect(actual).toEqual(true);
    });

    it('and calling setShown should save that the user as seen it', async () => {
      await renderSetShown();

      expect(localStorage.getItem(MY_PODS_INTRO_SPLASH_SCREEN_SEEN_KEY)).toEqual('true');
    });

    it('and user has viewed mypods splash screen before should return false', async () => {
      localStorage.setItem(MY_PODS_INTRO_SPLASH_SCREEN_SEEN_KEY, 'true');

      const actual = await renderUseShowMyPodsIntroSplashScreen();

      expect(actual).toEqual(false);
    });
  });

  describe('when isMyPodsIntroSplashScreenEnabled false', () => {
    beforeEach(() => {
      mockIsMyPodsIntroSplashScreenEnabled.mockReturnValue(false);
    });

    it('returns false and does not record that the user has seen it', async () => {
      const actual = await renderUseShowMyPodsIntroSplashScreen();

      expect(actual).toEqual(false);
      expect(localStorage.getItem(MY_PODS_INTRO_SPLASH_SCREEN_SEEN_KEY)).toBeNull();
    });
  });
});
