import { useSplitTreatments } from '@splitsoftware/splitio-react';
import { useTranslation } from 'react-i18next';
import { useLoadDynamicTranslations } from './useLoadDynamicTranslations';

export const POET_ENABLED = 'poet_enabled';
export const PODS_READY_SINGLE_ORDER_ENABLED = 'pb-pods-ready-single-order';
export const PODS_READY_PASSWORD_ONBOARDING = 'pb-password-onboarding';
export const EMAIL_VERIFICATION_ENABLED = 'mypods-email-verification';
export const ACORN_FINANCING_ENABLED = 'mypods_acorn_financing';
export const ORDER_MODIFICATION_ENABLED = 'pb-order-modification';
export const LINK_FLOW_RENTAL_AGREEMENT_ENABLED = 'pb-ra-link-flow';
export const MOTH_PAGINATION_ENABLED = 'pb-moth-pagination';
export const MYPODS_FNPS = 'MyPods_FNPS';
export const HOME_PAGE_ALERT_ENABLED = 'pb-mypods-home-alert';
export const LEFT_NAV_ENABLED = 'pb-left-nav';
export const MY_PODS_INTRO_SPLASH_SCREEN_ENABLED = 'pb-mypods-intro-splash-screen';
export const REDESIGNED_SCHEDULING = 'pb-redesigned-scheduling';
export const QUICK_LINKS = 'pb-quick-links';
export const CONTAINER_CARDS = 'pb-container-cards';
export const HOMEPAGE_SCANNABILITY = 'pb-homepage-scannability';

export interface SplitFeatureFlags {
  isPoetEnabled: () => boolean;
  isPodsReadySingleOrderEnabled: () => boolean;
  isPodsReadyPasswordOnboardingEnabled: () => boolean;
  isEmailVerificationEnabled: () => boolean;
  isAcornFinancingEnabled: () => boolean;
  isOrderModEnabled: () => boolean;
  isLinkFlowRentalAgreementEnabled: () => boolean;
  isMothPaginationEnabled: () => boolean;
  isFNPSEnabled: () => boolean;
  isLeftNavEnabled: () => boolean;
  isHomePageAlertEnabled: () => boolean;
  isMyPodsIntroSplashScreenEnabled: () => boolean;
  isRedesignedSchedulingEnabled: () => boolean;
  isContainerCardsEnabled: () => boolean;
  isQuickLinksEnabled: () => boolean;
  isHomepageScannabilityEnabled: () => boolean;
  isReady: boolean;
}

// NOTE: pass in the names of flags you will use, and then only use the functions for those values
/*
dynamic translations will be in form of "flagName.propertyName" for example:
The feature flag pb-mypods-home-alert with a configuration like:
{
  "en_us": {
    "title": "Online Scheduling Unavailable",
    "body": "Scheduling services through your MyPODS online account is temporarily unavailable. For assistance, please contact us via chat, text, or phone. Required documents must still be signed online."
  }
}
You would use "pb-mypods-home-alert.title" and "pb-mypods-home-alert.body"
*/
export const useFeatureFlags = (flagNames: string[]): SplitFeatureFlags => {
  const { treatments, isReady } = useSplitTreatments({ names: flagNames });
  const { i18n } = useTranslation();

  const isJsonEnabled = (flagName: string) => {
    const treatment = treatments?.[flagName];
    if (treatment?.treatment === 'on' && treatment.config) {
      // eslint-disable-next-line react-hooks/rules-of-hooks
      return useLoadDynamicTranslations(flagName, treatments, i18n);
    }
    return false;
  };
  const isEnabled = (flagName: string) => treatments?.[flagName]?.treatment === 'on';

  return {
    isPoetEnabled: () => isEnabled(POET_ENABLED),
    isPodsReadySingleOrderEnabled: () => isEnabled(PODS_READY_SINGLE_ORDER_ENABLED),
    isPodsReadyPasswordOnboardingEnabled: () => isEnabled(PODS_READY_PASSWORD_ONBOARDING),
    isEmailVerificationEnabled: () => isEnabled(EMAIL_VERIFICATION_ENABLED),
    isAcornFinancingEnabled: () => isEnabled(ACORN_FINANCING_ENABLED),
    isOrderModEnabled: () => isEnabled(ORDER_MODIFICATION_ENABLED),
    isLinkFlowRentalAgreementEnabled: () => isEnabled(LINK_FLOW_RENTAL_AGREEMENT_ENABLED),
    isMothPaginationEnabled: () => isEnabled(MOTH_PAGINATION_ENABLED),
    isFNPSEnabled: () => isEnabled(MYPODS_FNPS),
    isHomePageAlertEnabled: () => isJsonEnabled(HOME_PAGE_ALERT_ENABLED),
    isLeftNavEnabled: () => isEnabled(LEFT_NAV_ENABLED),
    isMyPodsIntroSplashScreenEnabled: () => isEnabled(MY_PODS_INTRO_SPLASH_SCREEN_ENABLED),
    isRedesignedSchedulingEnabled: () => isEnabled(REDESIGNED_SCHEDULING),
    isQuickLinksEnabled: () => isEnabled(QUICK_LINKS),
    isContainerCardsEnabled: () => isEnabled(CONTAINER_CARDS),
    isHomepageScannabilityEnabled: () => isEnabled(HOMEPAGE_SCANNABILITY),
    isReady
  };
};
