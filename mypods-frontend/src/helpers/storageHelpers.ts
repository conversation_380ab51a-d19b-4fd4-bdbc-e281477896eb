export const ORDERS_WITH_SIGNED_MOTH_AGREEMENTS = 'ordersWithSignedMothAgreements';
export const SIGNED_RENTAL_AGREEMENTS = 'signedRentalAgreements';
export const WEIGHT_TICKET_BANNER = 'dismissedWeightTicketBanner';
export const DOCUMENTS_TOKEN_KEY = 'DocumentToken';
export const BILLING_DOCUMENTS_TOKEN_KEY = 'BillingDocumentToken';

export const getOrdersWithSignedMothAgreements = (): string[] | null => {
  const signedAgreements = localStorage.getItem(ORDERS_WITH_SIGNED_MOTH_AGREEMENTS);
  if (signedAgreements != null) return JSON.parse(signedAgreements) as string[];
  return null;
};

export const orderHasSignedMothAgreement = (orderId: string): boolean =>
  getOrdersWithSignedMothAgreements()?.find((signedOrderId: string) => signedOrderId === orderId) !=
  null;

export const setOrdersWithSignedMothAgreements = (orderIds: string[] | null) => {
  if (orderIds == null) localStorage.removeItem(ORDERS_WITH_SIGNED_MOTH_AGREEMENTS);
  else localStorage.setItem(ORDERS_WITH_SIGNED_MOTH_AGREEMENTS, JSON.stringify(orderIds));
};

export const getSignedRentalAgreements = (): string[] | null => {
  const signedAgreements = localStorage.getItem(SIGNED_RENTAL_AGREEMENTS);
  if (signedAgreements != null) {
    return JSON.parse(signedAgreements) as string[];
  }
  return null;
};

export const rentalAgreementHasBeenSigned = (docId: string): boolean =>
  getSignedRentalAgreements()?.find((signedDocId: string) => signedDocId === docId) != null;

export const setSignedRentalAgreements = (docId: string[] | null) => {
  if (docId == null) {
    localStorage.removeItem(SIGNED_RENTAL_AGREEMENTS);
  } else {
    localStorage.setItem(SIGNED_RENTAL_AGREEMENTS, JSON.stringify(docId));
  }
};

export const addSignedRentalAgreements = (docIds: string[]) => {
  const existingIds = getSignedRentalAgreements();
  let idsToSave = docIds;
  if (existingIds) {
    idsToSave = [...docIds, ...existingIds];
  }
  setSignedRentalAgreements(idsToSave);
};

export const getWeightTicketBanner = (): boolean | null => {
  const showWeightedTicketBanner = localStorage.getItem(WEIGHT_TICKET_BANNER);
  if (showWeightedTicketBanner != null) return JSON.parse(showWeightedTicketBanner) as boolean;
  return null;
};

export const setWeightTicketBanner = (showWeightedTicketBanner: boolean | null) => {
  if (showWeightedTicketBanner == null) localStorage.removeItem(WEIGHT_TICKET_BANNER);
  else localStorage.setItem(WEIGHT_TICKET_BANNER, JSON.stringify(showWeightedTicketBanner));
};

export const getLocalStorage = (key: string): string | null => {
  const token = localStorage.getItem(key);
  if (token != null) return token;
  return null;
};

export const setLocalStorage = (key: string, token: string | null) => {
  if (token == null) localStorage.removeItem(key);
  else localStorage.setItem(key, token);
};

export const getDocumentToken = (): string | null => getLocalStorage(DOCUMENTS_TOKEN_KEY);

export const setDocumentToken = (token: string | null) => {
  setLocalStorage(DOCUMENTS_TOKEN_KEY, token);
};

export const getBillingDocumentToken = (): string | null =>
  getLocalStorage(BILLING_DOCUMENTS_TOKEN_KEY);

export const setBillingDocumentToken = (token: string | null) => {
  setLocalStorage(BILLING_DOCUMENTS_TOKEN_KEY, token);
};
