import {
  addDays as addDaysDateFns,
  differenceInDays,
  differenceInHours,
  format,
  startOfDay
} from 'date-fns';
import groupBy from 'lodash/groupBy';
import map from 'lodash/map';
import capitalize from 'lodash/capitalize';
import { DocumentSignatureTimezoneRequest } from '../networkRequests/responseEntities/DocumentApiEntities';

export const getDaysUntil = (date: Date | null | undefined): number | null => {
  if (!date) return null;
  const start = startOfDay(new Date());
  const end = startOfDay(date);
  const daysBetween = getDaysBetween(start, end);
  if (daysBetween < 0) return null;
  return daysBetween;
};

export const isWithin24Hours = (date: Date | undefined) => {
  const hoursUntil = getHoursUntil(date);
  return hoursUntil != null && hoursUntil <= 24;
};

export const getHoursUntil = (date: Date | null | undefined): number | null => {
  if (!date) return null;
  return getHoursBetween(new Date(), date);
};

export const getDaysBetween = (start: Date, end: Date) => differenceInDays(end, start);

export const getHoursBetween = (start: Date, end: Date) => differenceInHours(end, start);

export function formatDate(date: Date, dateFormat: string) {
  return format(date, dateFormat);
}

export const formatToLocale = (date: Date) =>
  date.toLocaleString(navigator.language, {
    month: 'long',
    day: 'numeric',
    year: 'numeric'
  });

export const formatShortMonthDayToLocale = (date: Date) =>
  date.toLocaleString(navigator.language, {
    month: 'short',
    day: 'numeric'
  });

export function addDays(date: Date, days: number) {
  return addDaysDateFns(date, days);
}

export function formatETA(timeRange: string | undefined): string {
  if (isFullWarehouseHours(timeRange)) {
    return '';
  }
  if (!timeRange?.trim()) {
    return '';
  }
  const [startTime, endTime] = timeRange.split('-').map((time) => time.trim());
  return `${truncateTime(startTime)} to ${truncateTime(endTime)}`;
}

export type WarehouseTiming = {
  day: string;
  hoursRange: string;
};

export function isFullWarehouseHours(eta: string | undefined): boolean {
  // if the eta is blank or not the JSON string return.
  if (!eta || !eta.match(/[{}[\]]/)) {
    return false;
  }
  return true;
}

export function formatFullWarehouseHours(eta: string | undefined): WarehouseTiming[] | null {
  // if the eta is blank or not the JSON string return.
  if (!eta || !isFullWarehouseHours(eta)) {
    return null;
  }

  const warehouseJSON = JSON.parse(eta)[0];

  const warehouseTimings = map(warehouseJSON, ({ startTime, endTime }, dayOfTheWeek: string) => ({
    day: capitalize(dayOfTheWeek.slice(0, 3)),
    hoursRange: startTime === 'Closed' ? 'Closed' : formatETA([startTime, endTime].join('-'))
  }));

  const sortedTimings = warehouseTimings.sort((a, b) => {
    const daysOfWeek = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    return daysOfWeek.indexOf(a.day) - daysOfWeek.indexOf(b.day);
  });
  const groupedTimings = groupBy(sortedTimings, ({ hoursRange }) => hoursRange);

  return Object.values(groupedTimings).map((groupedSchedule) => {
    if (groupedSchedule.length === 1) {
      return groupedSchedule[0];
    }
    const days = `${groupedSchedule[0].day} - ${groupedSchedule[groupedSchedule.length - 1].day}`;
    return {
      day: days,
      hoursRange: groupedSchedule[0].hoursRange
    };
  });
}

function truncateTime(time: string): string {
  // Trim spaces and split time into components
  const [hours, minutesPeriod] = time.trim().split(':');
  let [minutes, period] = minutesPeriod.split(' ');

  // Handle cases where period may not have a space before it
  if (!period) {
    period = minutes.slice(2);
    minutes = minutes.slice(0, 2);
  }

  // Convert hours to a number and remove leading zero if present
  const formattedHours = hours.startsWith('0') ? hours.slice(1) : hours;

  // Check if minutes are "00" and remove them
  const formattedMinutes = minutes === '00' ? '' : `:${minutes}`;

  // Return formatted time
  return `${formattedHours}${formattedMinutes}${period}`;
}

export function formatDateWithSuffix(dateString: string) {
  const date = new Date(dateString);
  return format(date, 'MMMM do, yyyy');
}

export function getTimezoneData(): DocumentSignatureTimezoneRequest {
  return {
    timezoneOffset: new Date().getTimezoneOffset(),
    timezoneName: Intl.DateTimeFormat().resolvedOptions().timeZone.toString()
  };
}
