import { HostedFieldsTokenizePayload } from 'braintree-web';
import { addDays, parseISO } from 'date-fns';
import { v4 as uuidv4 } from 'uuid';
import { Factory } from './Factories';
import {
  Customer,
  CustomerAddress,
  CustomerType,
  Email,
  formatAddress,
  Phone,
  UpdatePasswordResponse,
  UpdatePasswordResponseCode
} from '../networkRequests/responseEntities/CustomerEntities';
import {
  BillingInformation,
  BillingInvoice,
  BillingResponse,
  MonthlyBillingStatement
} from '../networkRequests/responseEntities/BillingEntities';
import {
  EntryPointResult,
  OutstandingFnpsAgreement,
  OutstandingMothAgreement,
  OutstandingRentalAgreement,
  RefreshSessionClaims
} from '../networkRequests/responseEntities/AuthorizationEntities';
import {
  CloneQuoteFromOrderResponse,
  Container,
  MoveLeg,
  MoveLegAddress,
  Order,
  OrderType
} from '../domain/OrderEntities';
import { IDocument, OrderDocument } from '../domain/DocumentEntities';
import {
  CustomerDocumentAPI,
  CustomerDocuments,
  DocumentAPI,
  SignFnpsWaiverRequest,
  SignMothAgreementRequest
} from '../networkRequests/responseEntities/DocumentApiEntities';
import {
  PaymentMethod,
  PaymentMethodAPI,
  paymentMethodApiToDomain
} from '../networkRequests/responseEntities/PaymentEntities';
import {
  ContainerAPI,
  MoveLegAPI,
  OrderAPI,
  scheduledStatus,
  UpdateMoveLegResponse
} from '../networkRequests/responseEntities/OrderAPIEntities';
import { ORDER_CONFIRMATION } from '../helpers/Documents';
import { formatDate, getTimezoneOffset } from '../helpers/dateHelpers';
import { SplitFeatureFlags } from '../helpers/useFeatureFlags';
import {
  PodsReadyAccessTokenClaims,
  ShowPodsReadySingleOrderResponse
} from '../networkRequests/responseEntities/PodsReadyEntities';
import { ContainerAvailability } from '../networkRequests/responseEntities/AvailabilityAPIEntities';
import { MoveLegScheduling } from '../context/SingleOrderContext';

export const createCustomer: Factory<Customer> = (overrides = {}) => ({
  id: '111111111',
  firstName: 'John',
  lastName: 'Doe',
  customerType: CustomerType.RESIDENTIAL,
  email: createEmail(),
  primaryPhone: createPhone(),
  secondaryPhone: undefined,
  billingAddress: createCustomerAddress(),
  shippingAddress: undefined,
  isConverted: true,
  smsOptIn: true,
  username: '<EMAIL>',
  ...overrides
});

export const createRefreshSessionClaims: Factory<RefreshSessionClaims> = (overrides = {}) => ({
  customerId: '111111111',
  firstName: 'John',
  lastName: 'Doe',
  username: '<EMAIL>',
  type: CustomerType.RESIDENTIAL,
  ownedCuids: ['111111111'],
  trackingUuid: uuidv4(),
  militaryBranch: 'unknown',
  militaryStatus: 'unknown',
  ...overrides
});

export const createPodsReadyClaims: Factory<PodsReadyAccessTokenClaims> = (overrides = {}) => ({
  customerId: '111111111',
  email: '<EMAIL>',
  hasPassword: false,
  firstName: 'John',
  lastName: 'Doe',
  trackingUuid: 'abc-123',
  type: CustomerType.RESIDENTIAL,
  militaryBranch: 'unknown',
  militaryStatus: 'unknown',
  ...overrides
});

export const createShowPodsReadySingleOrderResponse: Factory<ShowPodsReadySingleOrderResponse> = (
  overrides = {}
) => ({
  isFetching: false,
  showPodsReadySingleOrder: true,
  ...overrides
});

export const createEmail: Factory<Email> = (overrides = {}) => ({
  id: 1,
  address: '<EMAIL>',
  ...overrides
});

export const createPhone: Factory<Phone> = (overrides = {}) => ({
  id: 1,
  number: '************',
  ...overrides
});

export const createCustomerAddress: Factory<CustomerAddress> = (overrides = {}) => ({
  id: 1,
  addressType: 'Mailing',
  address1: '123 lake street',
  address2: '',
  city: 'Chicago',
  state: 'IL',
  postalCode: '12345',
  regionCode: 'US',
  ...overrides
});

export const createEmptyCustomerAddress: Factory<CustomerAddress> = (overrides = {}) => ({
  id: 0,
  addressType: 'Mailing',
  address1: '',
  address2: undefined,
  city: '',
  state: '',
  postalCode: '',
  regionCode: 'US',
  ...overrides
});

export const createMoveLegAddress: Factory<MoveLegAddress> = (overrides = {}) => ({
  address1: '321 Tuber Ln',
  address2: 'Apt 9',
  city: 'Spud City',
  state: 'ID',
  postalCode: '83401',
  country: 'US',
  ...overrides
});
export const createMonthlyStatement: Factory<MonthlyBillingStatement> = (overrides = {}) => ({
  childCustomerAccount: undefined,
  commercialInvoiceStatementDate: '2024-04-14T00:00:00Z',
  commercialInvoiceStatementDesc: '2024-4, 3/23/2024 - 4/22/2024',
  commercialInvoiceStatementId: 'PODSCS02140457',
  createdBy: 'Admin',
  createdDateTime: '2024-04-23T07:00:18',
  docuRefIdentity: **********,
  fileName: undefined,
  filePathAndName: undefined,
  fileType: undefined,
  invoiceType: 'Retail',
  parentCustomerAccount: '*********',
  path: undefined,
  ...overrides
});

export const createBillingInvoice: Factory<BillingInvoice> = (overrides = {}) => ({
  orderId: '12345',
  documentId: '**********',
  documentType: 'Invoice',
  documentDescription: undefined,
  invoiceNumber: 'PODS006078137',
  hasAttachment: true,
  documentStatus: 2,
  currencyType: 'USD',
  totalDue: 109.95,
  amountPaid: 109.95,
  balanceDue: 0.0,
  isPaid: true,
  dueDate: '2023-12-21T00:00:00',
  companyCode: undefined,
  isInternal: false,
  isPoet: false,
  ...overrides
});

export const createBillingInformation: Factory<BillingInformation> = (overrides = {}) => ({
  invoices: [createBillingInvoice({ isPaid: false })],
  monthlyStatements: [createMonthlyStatement()],
  totalBalance: 109.95,
  ...overrides
});

export const createUpdatePasswordResponse: Factory<UpdatePasswordResponse> = (overrides = {}) => ({
  responseCode: UpdatePasswordResponseCode.SUCCESS,
  ...overrides
});

export const createOutstandingRentalAgreement: Factory<OutstandingRentalAgreement> = (
  overrides = {}
) => ({
  orderId: '987654',
  companyCode: 'SPUD',
  identity: 'CONTRL-E',
  agreementType: 'LOCAL',
  ...overrides
});

export const createOutstandingMothAgreement: Factory<OutstandingMothAgreement> = (
  overrides = {}
) => ({
  orderId: '987654',
  ...overrides
});

export const createOutstandingFnpsAgreement: Factory<OutstandingFnpsAgreement> = (
  overrides = {}
) => ({
  customerId: '12345',
  orderId: '987654',
  moveId: '77777',
  isSigned: false,
  address: createCustomerAddress(),
  ...overrides
});

export const createSignFnpsRequest: Factory<SignFnpsWaiverRequest> = (overrides = {}) => ({
  orderId: '987654',
  moveId: '77777',
  firstName: 'Jane',
  lastName: 'Doe',
  address: createCustomerAddress(),
  ...getTimezoneOffset(),
  ...overrides
});

export const createEntryPointResult: Factory<EntryPointResult> = (overrides = {}) => ({
  outstandingRentalAgreements: [],
  outstandingMothAgreements: [],
  outstandingFnpsAgreements: [],
  hasMothForm: true,
  hasWeightTickets: true,
  maintenanceModeEnabled: false,
  acornFinancingEnabled: false,
  isFetching: false,
  ...overrides
});

export const createDocument: Factory<IDocument> = (overrides = {}) => ({
  id: '123',
  orderId: '456',
  type: 'MasterOrder',
  title: ORDER_CONFIRMATION,
  companyCode: '123',
  isPoet: false,
  ...overrides
});

export const createAPIDocument: Factory<DocumentAPI> = (overrides = {}) => ({
  id: '123',
  orderId: '456',
  type: 'MasterOrder',
  description: ORDER_CONFIRMATION,
  companyCode: '123',
  isPoet: false,
  ...overrides
});

export const createAPIDocuments: Factory<{ ['documents']: DocumentAPI[] }> = (overrides = {}) => ({
  documents: [createAPIDocument()],
  ...overrides
});

export const createCustomerDocuments: Factory<CustomerDocuments> = (overrides = {}) => ({
  documents: [createCustomerDocumentAPI()],
  token: 'default-token',
  ...overrides
});

export const createCustomerDocumentAPI: Factory<CustomerDocumentAPI> = (overrides = {}) => ({
  docRef: 'default-ref',
  docName: 'default-name',
  isCustomerFacing: true,
  id: 'default-id',
  docType: 'LEGAL_STATUS',
  docNotes: 'default-notes',
  invoiceNumber: 'default-invoice',
  title: 'IF Rental Agreement',
  ...overrides
});

export const createOrderDocument: Factory<OrderDocument> = (overrides = {}) => ({
  orderId: '1234',
  id: '12351',
  docType: 'RENTAL_AGREEMENT',
  isCustomerFacing: true,
  docStatus: 'SENT',
  billingCompanyCode: 'ABCD',
  docPath: '/sample.pdf',
  docName: 'sample.pdf',
  isInterFranchise: false,
  title: 'Local Rental Agreement',
  ...overrides
});

export const createMoveLegUpdateResponse: Factory<UpdateMoveLegResponse> = (overrides = {}) => ({
  priceDifference: undefined,
  quoteId: '0',
  ...overrides
});

export const createCloneQuoteFromOrderResponse: Factory<CloneQuoteFromOrderResponse> = (
  overrides = {}
) => ({
  newQuoteIdentity: 'quoteId',
  newQuoteSalesforceId: 'salesforceId',
  ...overrides
});

export const createPaymentMethodAPI: Factory<PaymentMethodAPI> = (overrides = {}) => ({
  paymentMethodId: '*********',
  isPrimary: true,
  cardType: 'Visa',
  cardNumberLastFourDigits: '1111',
  accountId: '',
  cardExpirationDate: '2028-01-01',
  locAmount: undefined,
  locApplicationId: 0,
  locBalance: undefined,
  locIsActive: false,
  locLender: undefined,
  locOriginationDate: undefined,
  locProviderName: undefined,
  locTerminationDate: undefined,
  ...overrides
});

export const createPaymentMethod: Factory<PaymentMethod> = (overrides = {}) => ({
  ...paymentMethodApiToDomain(createPaymentMethodAPI()),
  ...overrides
});

export const createOrder = (overrides: Partial<Order> = {}): Order => ({
  orderId: '987654',
  quoteId: 123,
  price: 100.0,
  containers: [createContainer()],
  orderDate: parseISO('2024-04-25T09:53:00.3731149'),
  orderType: OrderType.LOCAL,
  initialDeliveryPlacementIsReviewed: true,
  ...overrides
});

export const createOrderApi = (overrides: Partial<OrderAPI> = {}): OrderAPI => ({
  orderId: '987654',
  quoteId: 123,
  price: 100.0,
  containers: [createContainerApi()],
  orderDate: '2024-04-25T09:53:00.3731149',
  updatedDate: '2024-04-25T09:53:00.3731149',
  orderType: OrderType.LOCAL,
  initialDeliveryPlacementIsReviewed: true,
  ...overrides
});

export const createContainerApi = (overrides: Partial<ContainerAPI> = {}): ContainerAPI => ({
  containerId: '1678902847',
  containerOrderId: '200034658',
  upNextMoveLegId: undefined,
  containerSize: '16',
  moveLegs: [],
  ...overrides
});

export const createMoveLegApi = (overrides: Partial<MoveLegAPI> = {}): MoveLegAPI => ({
  moveLegId: '1',
  moveLegType: 'INITIAL_DELIVERY',
  serviceCountdownType: 'DELIVERY',
  scheduledDate: '2024-05-25T09:53:00.3731149',
  siteIdentity: 'ELP2',
  eta: undefined,
  displayAddress: createMoveLegAddress(),
  originationAddress: createMoveLegAddress(),
  destinationAddress: createMoveLegAddress(),
  isUpNext: false,
  isCityService: false,
  isHawaii: false,
  isCrossBorder: false,
  isSchedulableOnline: true,
  isTransitLeg: false,
  transitDays: 0,
  firstAvailableDate: '2024-01-01',
  lastAvailableDate: '2024-01-01',
  ...overrides
});

export const createContainer = (overrides: Partial<Container> = {}): Container => ({
  containerId: '1678902847',
  containerOrderId: '200034658',
  upNextMoveLegId: undefined,
  containerSize: '16',
  moveLegs: [createMoveLeg()],
  ...overrides
});

export const createMoveLeg = (overrides: Partial<MoveLeg> = {}): MoveLeg => {
  const moveLeg: MoveLeg = {
    moveLegId: '1',
    moveLegType: 'INITIAL_DELIVERY',
    serviceCountdownType: 'DELIVERY',
    scheduledDate: parseISO('2024-05-25T09:53:00.3731149'),
    scheduledStatus: 'UNSCHEDULED',
    siteIdentity: 'ELP2',
    eta: undefined,
    displayAddress: createMoveLegAddress(),
    originationAddress: createMoveLegAddress(),
    destinationAddress: createMoveLegAddress(),
    isUpNext: false,
    isCityService: false,
    isHawaii: false,
    isCrossBorder: false,
    isSchedulableOnline: true,
    isTransitLeg: false,
    transitDays: 0,
    firstAvailableDate: parseISO('2024-01-01'),
    lastAvailableDate: parseISO('2024-01-01'),
    ...overrides
  };
  moveLeg.scheduledStatus = scheduledStatus(moveLeg.scheduledDate);
  return moveLeg;
};

export const getThirtyDaysOfAvailability = (initialDate: Date = new Date()) =>
  Array(30)
    .fill(0)
    .map((_, i) => {
      const date = addDays(initialDate, i + 1);
      return {
        date: formatDate(date, 'yyyy-MM-dd'),
        isAvailable: true
      } as ContainerAvailability;
    });

export const createMoveLegScheduling = (
  overrides: Partial<MoveLegScheduling> = {}
): MoveLegScheduling => ({
  currentlySelectedMoveLeg: createMoveLeg(),
  editMoveLegScheduling: vi.fn(),
  stopMoveLegScheduling: vi.fn(),
  selectedOrderId: null,
  setSelectedOrderId: vi.fn(),
  clearOrderIdAndQuote: vi.fn(),
  isSaving: true,
  setIsSaving: vi.fn(),
  isCancelling: true,
  setIsCancelling: vi.fn(),
  ...overrides
});

export const createHostedFieldsTokenizePayload = (
  overrides: Partial<HostedFieldsTokenizePayload> = {}
) => ({
  nonce: 'test-token',
  details: {
    bin: '11112',
    lastFour: '4444',
    lastTwo: '44',
    cardType: 'visa',
    cardholderName: 'test user',
    expirationMonth: '12',
    expirationYear: '2030'
  },
  type: 'CreditCard',
  description: 'test description',
  ...overrides
});

export const createSignMothAgreementRequest = (
  overrideCustomer?: Customer,
  overrides: Partial<SignMothAgreementRequest> = {}
): SignMothAgreementRequest => {
  const customer = overrideCustomer ?? createCustomer();
  const orderId = '123456';

  return {
    orderId,
    firstName: customer.firstName,
    lastName: customer.lastName,
    email: customer.email?.address!!,
    address: formatAddress(customer.billingAddress),
    phone: customer.primaryPhone?.number!!,
    selectedCheckboxes: [],
    otherCheckboxes: [],
    ...getTimezoneOffset(),
    ...overrides
  };
};

export const mockIsESignRentalAgreementEnabled = vi.fn();
export const mockIsPodsReadySingleOrderEnabled = vi.fn();
export const mockIsPodsReadyPasswordOnboardingEnabled = vi.fn();
export const mockIsMyPodsIntroSplashScreenEnabled = vi.fn();

export const createUseFeatureFlagResult: Factory<SplitFeatureFlags> = (overrides) => ({
  isPoetEnabled: () => false,
  isPodsReadySingleOrderEnabled: mockIsPodsReadySingleOrderEnabled,
  isPodsReadyPasswordOnboardingEnabled: mockIsPodsReadyPasswordOnboardingEnabled,
  isEmailVerificationEnabled: () => false,
  isAcornFinancingEnabled: () => false,
  isOrderModEnabled: () => false,
  isLinkFlowRentalAgreementEnabled: mockIsESignRentalAgreementEnabled,
  isMothPaginationEnabled: () => false,
  isFNPSEnabled: () => false,
  isHomePageAlertEnabled: () => false,
  isLeftNavEnabled: () => false,
  isMyPodsIntroSplashScreenEnabled: mockIsMyPodsIntroSplashScreenEnabled,
  isRedesignedSchedulingEnabled: () => false,
  isQuickLinksEnabled: () => false,
  isContainerCardsEnabled: () => false,
  isHomepageScannabilityEnabled: () => false,
  isReady: true,
  ...overrides
});

export const createBillingRequest: Factory<BillingResponse> = (overrides) => ({
  billingInformation: createBillingInformation(),
  token: 'token',
  ...overrides
});

export const createBillingQueryResult: Factory<any> = (overrides) => ({
  data: createBillingRequest(),
  status: 'success',
  fetchStatus: 'idle',
  isPending: false,
  isSuccess: true,
  isError: false,
  isLoading: false,
  dataUpdatedAt: 1738090267109,
  error: null,
  errorUpdatedAt: 0,
  failureCount: 0,
  failureReason: null,
  errorUpdateCount: 0,
  isFetched: false,
  isFetchedAfterMount: false,
  isFetching: false,
  isRefetching: false,
  isLoadingError: false,
  isPaused: false,
  isPlaceholderData: false,
  isRefetchError: false,
  isStale: true,
  isInitialLoading: false,
  refetch: vi.fn(),
  ...overrides
});
