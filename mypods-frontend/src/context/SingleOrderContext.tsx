import React, {
  createContext,
  FC,
  ReactNode,
  useCallback,
  useContext,
  useMemo,
  useState
} from 'react';
import { CloneQuoteFromOrderResponse, MoveLeg, Order, OrderType } from '../domain/OrderEntities';
import { useScheduleMoveLegEvents } from '../config/useScheduleMoveLegEvents';
import { useMyPodsService } from '../networkRequests/MyPodsService';

interface SingleOrderContextProps {
  state: Pick<ISingleOrderContextState, 'order'>;
  children: ReactNode;
}

export type MoveLegScheduling = {
  currentlySelectedMoveLeg: MoveLeg | null;
  editMoveLegScheduling: (_: MoveLeg) => void;
  stopMoveLegScheduling: () => void;
  selectedOrderId: string | null;
  setSelectedOrderId: (orderId: string) => void;
  clearOrderIdAndQuote: () => void;
  isSaving: boolean;
  setIsSaving: (_: boolean) => void;
  isCancelling: boolean;
  setIsCancelling: (_: boolean) => void;
  isCloningQuote: boolean;
};

export interface ISingleOrderContextState {
  order: Order;
  currentlySelectedQuote: CloneQuoteFromOrderResponse | null;
  moveLegScheduling: MoveLegScheduling;
}

const initialState: ISingleOrderContextState = {
  order: {
    orderId: '',
    quoteId: 0,
    orderType: OrderType.LOCAL,
    containers: [],
    orderDate: undefined,
    price: 0,
    initialDeliveryPlacementIsReviewed: true
  },
  currentlySelectedQuote: null,
  moveLegScheduling: {
    currentlySelectedMoveLeg: null,
    editMoveLegScheduling: (_: MoveLeg) => {},
    stopMoveLegScheduling: () => {},
    selectedOrderId: null,
    setSelectedOrderId: (_: string) => {},
    clearOrderIdAndQuote: () => {},
    isSaving: false,
    setIsSaving: (_: boolean) => {},
    isCancelling: false,
    setIsCancelling: (_: boolean) => {},
    isCloningQuote: false
  }
};

export const SingleOrderContext = createContext<ISingleOrderContextState>(initialState);

export default function useSingleOrderContext() {
  return useContext(SingleOrderContext);
}

export const SingleOrderProvider: FC<SingleOrderContextProps> = ({ state, children }) => {
  const [currentlySelectedMoveLeg, setCurrentlySelectedMoveLeg] = useState<MoveLeg | null>(null);
  const [currentlySelectedOrderId, setCurrentlySelectedOrderId] = useState<string | null>(null);
  const [currentlySelectedQuote, setCurrentlySelectedQuote] =
    useState<CloneQuoteFromOrderResponse | null>(null);
  const { schedulingStart } = useScheduleMoveLegEvents();
  const { cloneQuoteFromOrder } = useMyPodsService();
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [isCancelling, setIsCancelling] = useState<boolean>(false);
  const [isCloningQuote, setIsCloningQuote] = useState<boolean>(false);

  const handleSchedulingMoveLeg = useCallback(
    (moveLeg: MoveLeg) => {
      setCurrentlySelectedMoveLeg(moveLeg);
      setIsCancelling(false);
      schedulingStart();
    },
    [setCurrentlySelectedMoveLeg, setIsCancelling]
  );

  const setSelectedOrderId = (orderId: string) => {
    if (currentlySelectedOrderId !== orderId) {
      setCurrentlySelectedQuote(null);
      setCurrentlySelectedOrderId(orderId);
      setIsCloningQuote(true);
      cloneQuoteFromOrder({ orderId })
        .then(setCurrentlySelectedQuote)
        .finally(() => {
          setIsCloningQuote(false);
        });
    }
  };

  const clearOrderIdAndQuote = () => {
    setCurrentlySelectedOrderId(null);
    setCurrentlySelectedQuote(null);
  };

  const handleStopScheduling = useCallback(() => {
    setCurrentlySelectedMoveLeg(null);
    setIsCancelling(false);
  }, [setCurrentlySelectedMoveLeg, setIsCancelling]);

  const value = useMemo(
    () => ({
      ...state,
      currentlySelectedQuote,
      moveLegScheduling: {
        currentlySelectedMoveLeg,
        editMoveLegScheduling: handleSchedulingMoveLeg,
        stopMoveLegScheduling: handleStopScheduling,
        selectedOrderId: currentlySelectedOrderId,
        setSelectedOrderId,
        clearOrderIdAndQuote,
        isSaving,
        setIsSaving,
        isCancelling,
        setIsCancelling,
        isCloningQuote
      }
    }),
    [
      state,
      currentlySelectedQuote,
      currentlySelectedMoveLeg,
      currentlySelectedOrderId,
      isSaving,
      setIsSaving,
      isCancelling,
      setIsCancelling,
      isCloningQuote,
      handleStopScheduling,
      handleSchedulingMoveLeg
    ]
  );

  return <SingleOrderContext.Provider value={value}>{children}</SingleOrderContext.Provider>;
};
