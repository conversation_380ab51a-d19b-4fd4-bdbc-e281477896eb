import './App.css';
import { useLocation } from 'react-router';
import React, { Suspense, useEffect, useRef } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'; // Create a client
import { ThemeProvider } from '@mui/material';
import { APIProvider } from '@vis.gl/react-google-maps';
import { Helmet, HelmetProvider } from 'react-helmet-async';
import TagManager from 'react-gtm-module';
import { useTranslation } from 'react-i18next';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { ENV_VARS, Environment } from './environment';
import { ROUTES } from './Routes';
import { theme } from './PodsTheme';
import { useDataDogConfig } from './config/useDataDogConfig';
import { ErrorBoundary } from './components/ErrorBoundary';
import { getVisitorId } from './config/getVisitorId';
import { ApigeeProvider } from './context/ApigeeContext';
import { isDebug } from './pages/DebugPage/DebugPage';
import { RequestHeaders } from './config/RequestHeaders';
import { DebugStack } from './routes/DebugStack';
import LoadingScreen from './components/Loading/LoadingScreen';
import { TranslationKeys } from './locales/TranslationKeys';
import { StackWrapper } from './routes/StackWrapper';
import { VisitorIdSplitContextProvider } from './context/SplitContext';

const STALE_TIME_IN_MS = 60 * 1000 * 30;
// Can be disabled on a per-query basis, see:
// https://tanstack.com/query/latest/docs/framework/react/guides/window-focus-refetching
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: STALE_TIME_IN_MS,
      refetchOnWindowFocus: false // default: true
    }
  }
});

const App = () => {
  const visitorID = getVisitorId();
  const location = useLocation();
  const datadog = useDataDogConfig();
  const { t: translate } = useTranslation();
  const globalBannersRef = useRef<HTMLDivElement>(null);
  const fullPageRoutesWithoutHeaderFooter = [
    ROUTES.MOTH_FLY_INSPECTION as string,
    ROUTES.MOTH_FLY_INSPECTION_FORM as string
  ];

  const showHeaderFooter = !fullPageRoutesWithoutHeaderFooter.includes(location.pathname);

  useEffect(() => {
    datadog.configure(visitorID);
    const isSyntheticTest = visitorID === RequestHeaders.SYNTHETIC_TEST_VISITOR_ID;
    if (!isSyntheticTest) TagManager.initialize(ENV_VARS.GTM_ARGS);
  }, []);

  if (isDebug(location)) {
    return <DebugStack queryClient={queryClient} />;
  }
  return (
    <VisitorIdSplitContextProvider>
      <HelmetProvider>
        <Helmet>
          <script async={false} type="module" src={ENV_VARS.ACORN_FINANCE_WIDGET}></script>
        </Helmet>
        <Helmet>
          <script async type="module" src={ENV_VARS.ACORN_DISCLOSURE_WIDGET} />
        </Helmet>
        <Helmet>
          {ENV_VARS.ENVIRONMENT !== Environment.PROD && <meta name="robots" content="noindex" />}
        </Helmet>
        <ThemeProvider theme={theme}>
          <ApigeeProvider>
            <ErrorBoundary renderError={(error) => <p>{error.message}</p>}>
              <QueryClientProvider client={queryClient}>
                <Suspense
                  fallback={
                    <LoadingScreen
                      loadingText={translate(TranslationKeys.LoadingScreens.HOME_PAGE)}
                    />
                  }>
                  <APIProvider apiKey={ENV_VARS.GOOGLE_PLACE_AND_MAPS_API_KEY}>
                    <StackWrapper
                      globalBannersRef={globalBannersRef}
                      showHeaderFooter={showHeaderFooter}
                    />
                  </APIProvider>
                </Suspense>
                <ReactQueryDevtools initialIsOpen={false} />
              </QueryClientProvider>
            </ErrorBoundary>
          </ApigeeProvider>
        </ThemeProvider>
      </HelmetProvider>
    </VisitorIdSplitContextProvider>
  );
};

export default App;
