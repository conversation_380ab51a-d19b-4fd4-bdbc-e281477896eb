import React from 'react';
import { useTranslation } from 'react-i18next';
import { POET_ENABLED, useFeatureFlags } from '../helpers/useFeatureFlags';
import { LegacyAppStack } from './LegacyStack';
import LoadingScreen from '../components/Loading/LoadingScreen';
import { TranslationKeys } from '../locales/TranslationKeys';
import { AppStack } from './AppStack';
import { Stack, WhichStackContext } from '../context/WhichStackContext';

// -- types --
interface Props {
  showHeaderFooter: boolean;
  globalBannersRef: React.RefObject<HTMLDivElement>;
}

// This component is temporary;
// the feature flag hook was being called before the SplitComponent was rendered
// & so it never updated the ready or enabled values

// TODO: POST-POET-CLEANUP
// The WhichStackProvider should go away once we're fully in poet, and we can return to just using the myPodsService ONLY

// -- impls --
export const StackWrapper: React.FC<Props> = ({ showHeaderFooter, globalBannersRef }: Props) => {
  const { t: translate } = useTranslation();
  const { isPoetEnabled, isReady } = useFeatureFlags([POET_ENABLED]);

  if (!isReady) {
    return <LoadingScreen loadingText={translate(TranslationKeys.LoadingScreens.HOME_PAGE)} />;
  }

  if (isPoetEnabled()) {
    return (
      <WhichStackContext.Provider value={Stack.POET}>
        <AppStack showHeaderFooter={showHeaderFooter} globalBannersRef={globalBannersRef} />
      </WhichStackContext.Provider>
    );
  }
  return (
    <WhichStackContext.Provider value={Stack.LEGACY}>
      <LegacyAppStack showHeaderFooter={showHeaderFooter} globalBannersRef={globalBannersRef} />
    </WhichStackContext.Provider>
  );
};
