import React, { RefObject } from 'react';
import { Navigate, Route, Routes } from 'react-router';
import { BillingProvider } from '../context/BillingContext';
import { NotificationProvider } from '../components/notifications/NotificationContext';
import { LegacyGlobalBanners } from '../components/legacyComponents/alert/LegacyGlobalBanners';
import { ROUTES } from '../Routes';
import { LegacyAccountPage } from '../pages/LegacyPages/AccountPage/LegacyAccountPage';
import { LegacyMakePaymentPage } from '../pages/LegacyPages/MakePaymentPage/LegacyMakePaymentPage';
import { LegacyPaymentMethodsPage } from '../pages/LegacyPages/PaymentMethodsPage/LegacyPaymentMethodsPage';
import { NotFoundPage } from '../pages/NotFoundPage/NotFoundPage';
import { EntryPointProvider } from '../context/EntryPointContext';
import { LegacyHomePage } from '../pages/LegacyPages/HomePage/LegacyHomePage';
import { LegacyDocumentsPage } from '../pages/LegacyPages/DocumentsPage/LegacyDocumentsPage';
import { PodsReadyRoutes } from '../PodsReadyRoutes';
import { LegacyPodsReadyAppStack } from './LegacyPodsReadyAppStack';
import { LiveChat } from '../chat/LiveChat';
import { SessionHandler } from '../components/session/SessionHandler';
import { SessionSplitContextProvider } from '../context/SplitContext';
import { LegacyOrdersContextProvider } from '../context/legacy/LegacyOrdersContext';
import { LegacyManagePaymentMethodPage } from '../pages/LegacyPages/ManagePaymentMethodPage/LegacyManagePaymentMethodPage';
import { LegacySiteLayout } from '../components/legacyComponents/LegacySiteLayout';
import { LegacyBillingPage } from '../pages/LegacyPages/BillingPage/LegacyBillingPage';
import { LegacyCustomStatementPage } from '../pages/LegacyPages/CustomStatementPage/LegacyCustomStatementPage';
import { LegacyMothInspectionVariantSelector } from '../pages/LegacyPages/MothInspectionPage/LegacyMothInspectionVariantSelector';
import { LegacyRentalAgreementOnboarding } from '../components/legacyComponents/LegacyRentalAgreementOnboarding/LegacyRentalAgreementOnboarding';

// -- types --
type Props = {
  showHeaderFooter: boolean;
  globalBannersRef: RefObject<HTMLDivElement>;
};

export const LegacyAppStack: React.FC<Props> = ({ showHeaderFooter, globalBannersRef }: Props) => (
  <Routes>
    <Route path={PodsReadyRoutes.BASE_INDEX}>
      <Route index element={<LegacyPodsReadyAppStack />} />
      <Route path="*" element={<LegacyPodsReadyAppStack />} />
    </Route>
    <Route
      path="/*"
      element={
        <SessionHandler>
          <SessionSplitContextProvider>
            <LiveChat>
              <EntryPointProvider>
                <LegacyOrdersContextProvider topOfPageRef={globalBannersRef}>
                  <BillingProvider>
                    <NotificationProvider>
                      <LegacyRentalAgreementOnboarding>
                        <LegacySiteLayout
                          showHeaderFooter={showHeaderFooter}
                          globalBanners={
                            showHeaderFooter && <LegacyGlobalBanners ref={globalBannersRef} />
                          }>
                          <Routes>
                            <Route path={ROUTES.HOME} element={<LegacyHomePage />} />
                            <Route path={ROUTES.ACCOUNT} element={<LegacyAccountPage />} />
                            <Route path={ROUTES.BILLING} element={<LegacyBillingPage />} />
                            <Route path={ROUTES.MAKE_PAYMENT} element={<LegacyMakePaymentPage />} />
                            <Route
                              path={ROUTES.VIEW_PAYMENT_METHODS}
                              element={<LegacyPaymentMethodsPage />}
                            />
                            <Route
                              path={ROUTES.MANAGE_PAYMENT_METHOD}
                              element={<LegacyManagePaymentMethodPage />}
                            />
                            <Route
                              path={ROUTES.CUSTOM_STATEMENT}
                              element={<LegacyCustomStatementPage />}
                            />
                            <Route path={ROUTES.DOCUMENT} element={<LegacyDocumentsPage />} />
                            <Route
                              path={ROUTES.MOTH_FLY_INSPECTION}
                              element={<LegacyMothInspectionVariantSelector />}
                            />
                            <Route path={ROUTES.NOT_FOUND} element={<NotFoundPage />} />
                            <Route path="/*" element={<Navigate to={ROUTES.NOT_FOUND} replace />} />
                          </Routes>
                        </LegacySiteLayout>
                      </LegacyRentalAgreementOnboarding>
                    </NotificationProvider>
                  </BillingProvider>
                </LegacyOrdersContextProvider>
              </EntryPointProvider>
            </LiveChat>
          </SessionSplitContextProvider>
        </SessionHandler>
      }
    />
  </Routes>
);
