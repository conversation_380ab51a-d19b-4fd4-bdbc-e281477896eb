import React, { Suspense } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { APIProvider } from '@vis.gl/react-google-maps';
import { ThemeProvider } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { Helm<PERSON>, HelmetProvider } from 'react-helmet-async';
import { v4 as uuidv4 } from 'uuid';
import { theme } from '../PodsTheme';
import { ApigeeProvider } from '../context/ApigeeContext';
import { TranslationKeys } from '../locales/TranslationKeys';
import { ErrorBoundary } from '../components/ErrorBoundary';
import LoadingScreen from '../components/Loading/LoadingScreen';
import { SiteLayout } from '../components/SiteLayout';
import { DebugPage } from '../pages/DebugPage/DebugPage';
import { ENV_VARS, Environment } from '../environment';
import { Customer, CustomerType } from '../networkRequests/responseEntities/CustomerEntities';
import { RefreshSessionClaims } from '../networkRequests/responseEntities/AuthorizationEntities';
import { OrderAPI } from '../networkRequests/responseEntities/OrderAPIEntities';
import { PaymentMethodAPI } from '../networkRequests/responseEntities/PaymentEntities';
import { QueryCacheKeys } from '../networkRequests/QueryCacheKeys';

// -- types --
type Props = {
  queryClient: QueryClient;
};

// -- impls --
export const DebugStack: React.FC<Props> = ({ queryClient }: Props) => {
  // -- hooks --
  const { t: translate } = useTranslation();

  if (!queryClient.getQueryData([QueryCacheKeys.CUSTOMER_KEY]))
    queryClient.setQueryData<Customer>([QueryCacheKeys.CUSTOMER_KEY], {
      firstName: '',
      lastName: '',
      id: '123456'
    });

  if (!queryClient.getQueryData([QueryCacheKeys.REFRESH_KEY])) {
    queryClient.setQueryData<RefreshSessionClaims>([QueryCacheKeys.REFRESH_KEY], {
      customerId: '123456',
      firstName: '',
      lastName: '',
      username: 'not logged in',
      type: CustomerType.RESIDENTIAL,
      ownedCuids: [],
      trackingUuid: uuidv4(),
      militaryBranch: 'unknown',
      militaryStatus: 'unknown'
    });
    queryClient.setQueryData<OrderAPI[]>([QueryCacheKeys.CUSTOMER_ORDERS_CACHE_KEY], []);
    queryClient.setQueryData<PaymentMethodAPI[]>([QueryCacheKeys.PAYMENT_METHODS_KEY], []);
  }

  return (
    <HelmetProvider>
      <Helmet>
        {ENV_VARS.ENVIRONMENT !== Environment.PROD && <meta name="robots" content="noindex" />}
      </Helmet>
      <ThemeProvider theme={theme}>
        <ApigeeProvider>
          <ErrorBoundary renderError={(error) => <p>{error.message}</p>}>
            <Suspense
              fallback={
                <LoadingScreen loadingText={translate(TranslationKeys.LoadingScreens.HOME_PAGE)} />
              }>
              <QueryClientProvider client={queryClient}>
                <APIProvider
                  apiKey={ENV_VARS.GOOGLE_PLACE_AND_MAPS_API_KEY}
                  solutionChannel="GMP_devsite_samples_v3_rgmautocomplete">
                  <SiteLayout>
                    <DebugPage client={queryClient} />
                  </SiteLayout>
                </APIProvider>
              </QueryClientProvider>
            </Suspense>
          </ErrorBoundary>
        </ApigeeProvider>
      </ThemeProvider>
    </HelmetProvider>
  );
};
