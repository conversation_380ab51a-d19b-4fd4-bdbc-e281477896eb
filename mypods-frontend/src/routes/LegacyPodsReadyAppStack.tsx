import React, { Suspense } from 'react';
import { Route, Routes } from 'react-router';
import { useTranslation } from 'react-i18next';
import LoadingScreen from '../components/Loading/LoadingScreen';
import { TranslationKeys } from '../locales/TranslationKeys';
import { redirectToLoginWithStatus } from '../context/ApigeeContext';
import { ErrorBoundary } from '../components/ErrorBoundary';
import { LegacyTaskPage } from '../pages/LegacyPages/PodsReadyPages/Task/LegacyTaskPage';
import { PodsReadyRoutes } from '../PodsReadyRoutes';
import { LegacySetPasswordPage } from '../pages/LegacyPages/PodsReadyPages/SetPassword/LegacySetPasswordPage';
import { LegacyPodsReadyPage } from '../pages/LegacyPages/PodsReadyPages/LegacyPodsReadyPage';
import { PodsReadySplitContextProvider } from '../context/SplitContext';
import { LegacyPodsReadySessionHandler } from '../components/session/legacy/LegacyPodsReadySessionHandler';
import { EntryPointProvider } from '../context/EntryPointContext';
import { PodsReadySuccessPage } from '../pages/PodsReadyPages/PodsReadySuccessPage';
import { LegacyMothInspectionVariantSelector } from '../pages/LegacyPages/MothInspectionPage/LegacyMothInspectionVariantSelector';
import { LegacyRentalAgreementOnboarding } from '../components/legacyComponents/LegacyRentalAgreementOnboarding/LegacyRentalAgreementOnboarding';
import { PodsReadyLiveChat } from '../chat/PodsReadyLiveChat';

export const LegacyPodsReadyAppStack: React.FC = () => {
  const { t: translate } = useTranslation();
  const removeBaseRoute = (fullRoute: string): string =>
    fullRoute.replace(PodsReadyRoutes.BASE_INDEX, '');

  return (
    <ErrorBoundary
      renderError={(_) => (
        <LoadingScreen loadingText={translate(TranslationKeys.LoadingScreens.HOME_PAGE)} />
      )}
      redirectOnError={(status) => {
        if (status === 401 || status === 400) {
          redirectToLoginWithStatus('INVALID_PODS_READY_TOKEN');
        } else {
          redirectToLoginWithStatus('AUTH_ERROR');
        }
      }}>
      <Suspense
        fallback={
          <LoadingScreen loadingText={translate(TranslationKeys.LoadingScreens.HOME_PAGE)} />
        }>
        <LegacyPodsReadySessionHandler>
          <PodsReadySplitContextProvider>
            <PodsReadyLiveChat>
              <EntryPointProvider>
                <Routes>
                  <Route path="/" element={<LegacyPodsReadyPage />} />
                  <Route
                    path={removeBaseRoute(PodsReadyRoutes.SET_PASSWORD)}
                    element={<LegacySetPasswordPage />}
                  />
                  <Route
                    path={removeBaseRoute(PodsReadyRoutes.TASKS)}
                    element={<LegacyTaskPage />}
                  />
                  <Route
                    path={removeBaseRoute(PodsReadyRoutes.RENTAL_AGREEMENT)}
                    element={<LegacyRentalAgreementOnboarding />}
                  />
                  <Route
                    path={removeBaseRoute(PodsReadyRoutes.SUCCESS)}
                    element={<PodsReadySuccessPage />}
                  />
                  <Route
                    path={removeBaseRoute(PodsReadyRoutes.MOTH_FLY_INSPECTION)}
                    element={<LegacyMothInspectionVariantSelector />}
                  />
                </Routes>
              </EntryPointProvider>
            </PodsReadyLiveChat>
          </PodsReadySplitContextProvider>
        </LegacyPodsReadySessionHandler>
      </Suspense>
    </ErrorBoundary>
  );
};
