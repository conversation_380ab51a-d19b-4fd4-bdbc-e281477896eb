import React, { RefObject } from 'react';
import { Navigate, Route, Routes } from 'react-router';
import { OrdersContextProvider } from '../context/OrdersContext';
import { NotificationProvider } from '../components/notifications/NotificationContext';
import { RentalAgreementOnboarding } from '../components/RentalAgreementOnboarding/RentalAgreementOnboarding';
import { GlobalBanners } from '../components/alert/GlobalBanners';
import { ROUTES } from '../Routes';
import { HomePage } from '../pages/HomePage/HomePage';
import { AccountPage } from '../pages/AccountPage/AccountPage';
import { BillingPage } from '../pages/BillingPage/BillingPage';
import { MakePaymentPage } from '../pages/MakePaymentPage/MakePaymentPage';
import { PaymentMethodsPage } from '../pages/PaymentMethodsPage/PaymentMethodsPage';
import { ManagePaymentMethodPage } from '../pages/ManagePaymentMethodPage/ManagePaymentMethodPage';
import { CustomStatementPage } from '../pages/CustomStatementPage/CustomStatementPage';
import { DocumentsPage } from '../pages/DocumentsPage/DocumentsPage';
import { NotFoundPage } from '../pages/NotFoundPage/NotFoundPage';
import { LiveChat } from '../chat/LiveChat';
import { SessionHandler } from '../components/session/SessionHandler';
import { PodsReadyAppStack } from './PodsReadyAppStack';
import { PodsReadyRoutes } from '../PodsReadyRoutes';
import { SessionSplitContextProvider } from '../context/SplitContext';
import { MothInspectionVariantSelector } from '../pages/MothFlyInspectionFormPage/MothInspectionVariantSelector';
import { SiteLayout } from '../components/SiteLayout';
import { ContainerDetailPage } from '../pages/HomePage/container/ContainerDetailPage';

type Props = {
  showHeaderFooter: boolean;
  globalBannersRef: RefObject<HTMLDivElement>;
};

export const AppStack: React.FC<Props> = ({ showHeaderFooter, globalBannersRef }: Props) => (
  <Routes>
    <Route path={PodsReadyRoutes.BASE_INDEX}>
      <Route index element={<PodsReadyAppStack />} />
      <Route path="*" element={<PodsReadyAppStack />} />
    </Route>
    <Route
      path="/*"
      element={
        <SessionHandler>
          <SessionSplitContextProvider>
            <LiveChat>
              <OrdersContextProvider topOfPageRef={globalBannersRef}>
                <NotificationProvider>
                  <RentalAgreementOnboarding>
                    <SiteLayout
                      showHeaderFooter={showHeaderFooter}
                      globalBanners={showHeaderFooter && <GlobalBanners ref={globalBannersRef} />}>
                      <Routes>
                        <Route path={ROUTES.HOME} element={<HomePage />} />
                        <Route path={ROUTES.ACCOUNT} element={<AccountPage />} />
                        <Route path={ROUTES.BILLING} element={<BillingPage />} />
                        <Route path={ROUTES.MAKE_PAYMENT} element={<MakePaymentPage />} />
                        <Route
                          path={ROUTES.VIEW_PAYMENT_METHODS}
                          element={<PaymentMethodsPage />}
                        />
                        <Route
                          path={ROUTES.MANAGE_PAYMENT_METHOD}
                          element={<ManagePaymentMethodPage />}
                        />
                        <Route path={ROUTES.CUSTOM_STATEMENT} element={<CustomStatementPage />} />
                        <Route path={ROUTES.DOCUMENT} element={<DocumentsPage />} />
                        <Route
                          path={ROUTES.MOTH_FLY_INSPECTION}
                          element={<MothInspectionVariantSelector />}
                        />
                        <Route path={ROUTES.NOT_FOUND} element={<NotFoundPage />} />
                        <Route path={ROUTES.CONTAINER} element={<ContainerDetailPage />} />
                        <Route path="/*" element={<Navigate to={ROUTES.NOT_FOUND} replace />} />
                      </Routes>
                    </SiteLayout>
                  </RentalAgreementOnboarding>
                </NotificationProvider>
              </OrdersContextProvider>
            </LiveChat>
          </SessionSplitContextProvider>
        </SessionHandler>
      }
    />
  </Routes>
);
