import { StepConnector, stepConnectorClasses, styled } from '@mui/material';
import { memo } from 'react';

const ProgressConnector = styled(StepConnector)(() => ({
  // Align the connector with the ICON midline (not the whole step)
  [`&.${stepConnectorClasses.alternativeLabel}`]: {
    top: 'calc(var(--icon-size, 24px) / 2)', // <— key change
    transform: 'none', // no centering vs whole step
    left: 'calc(-50% + (var(--icon-size, 24px) / 2))',
    right: 'calc(50% + (var(--icon-size, 24px) / 2))',
    margin: 0
  },

  // draw custom bars; hide default
  [`& .${stepConnectorClasses.line}`]: {
    border: 'none',
    position: 'relative',
    height: 0
  },

  // base track (centered on our connector top)
  [`& .${stepConnectorClasses.line}::before`]: {
    content: '""',
    position: 'absolute',
    left: 0,
    right: 0,
    top: '50%',
    transform: 'translateY(-50%)',
    height: 'var(--connector-thickness, 6px)',
    backgroundColor: 'var(--stepper-track-color, #D3D6DB)',
    borderRadius: 'calc(var(--connector-thickness, 6px) / 2)',
    pointerEvents: 'none'
  },

  // filled overlay
  [`& .${stepConnectorClasses.line}::after`]: {
    content: '""',
    position: 'absolute',
    left: 0,
    top: '50%',
    transform: 'translateY(-50%)',
    height: 'var(--connector-thickness, 6px)',
    width: 'var(--connector-progress, 0%)',
    backgroundColor: 'var(--stepper-active-color, #3A84E9)',
    borderRadius: 'calc(var(--connector-thickness, 6px) / 2)',
    pointerEvents: 'none',
    transition: 'width 200ms ease'
  }
}));

export default memo(ProgressConnector);
