import React, { memo } from 'react';
import { Box, StepIconProps } from '@mui/material';
import { CheckCircle as PhCheckCircle } from '@phosphor-icons/react/dist/ssr/CheckCircle';
import { Design } from '../../helpers/Design';

const CircleStepIcon = ({ active, completed, className }: StepIconProps) => {
  const activeColor = `var(--stepper-active-color, ${Design.Alias.Color.secondary500})`;
  const idleBorder = Design.Alias.Color.accent900;

  return (
    <Box
      className={className}
      sx={{
        width: 'var(--icon-size, 24px)',
        height: 'var(--icon-size, 24px)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 1,
        lineHeight: 0
      }}>
      {completed ? (
        <PhCheckCircle
          size="100%"
          weight="fill"
          color={String(activeColor)}
          style={{ display: 'block', pointerEvents: 'none' }}
        />
      ) : (
        <Box
          sx={{
            width: 'calc(var(--icon-size, 24px) / 2)',
            height: 'calc(var(--icon-size, 24px) / 2)',
            borderRadius: '50%',
            border: `2px solid ${active ? activeColor : idleBorder}`,
            backgroundColor: 'transparent'
          }}
        />
      )}
    </Box>
  );
};

export default memo(CircleStepIcon);
