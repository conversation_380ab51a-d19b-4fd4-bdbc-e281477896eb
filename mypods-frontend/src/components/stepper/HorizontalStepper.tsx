import React, { memo } from 'react';
import { Stepper, Step, StepLabel } from '@mui/material';
import CircleStepIcon from './CircleStepIcon';
import ProgressConnector from './ProgressConnector';
import { Design } from '../../helpers/Design';

type HorizontalStepperProps = {
  steps: string[];
  activeStep: number; // 0-based
  connectorProgress?: number; // 0..1 for the gap AFTER activeStep
  className?: string;
  style?: React.CSSProperties;
};

const clamp01 = (n: number) => Math.max(0, Math.min(1, n ?? 0));

const HorizontalStepper = ({
  steps,
  activeStep,
  connectorProgress = 0,
  className,
  style
}: HorizontalStepperProps) => {
  const partial = clamp01(connectorProgress);

  return (
    <Stepper
      activeStep={activeStep}
      alternativeLabel
      connector={<ProgressConnector />}
      className={className}
      style={style}
      sx={{
        // knobs
        '--icon-size': '20px',
        '--connector-thickness': '4px',
        '--stepper-active-color': Design.Alias.Color.secondary500,
        '--stepper-track-color': Design.Alias.Color.neutral300,

        // normalize icon box so connector math is correct
        '& .MuiStepLabel-iconContainer': {
          width: 'var(--icon-size)',
          height: 'var(--icon-size)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          m: 0,
          p: 0,
          lineHeight: 0
        }
      }}>
      {steps.map((label, idx) => {
        const gapIdx = idx - 1; // connector to the LEFT of this step

        // LEFT of active step => 100%, AFTER active => partial, else 0
        let pct = 0;
        if (gapIdx >= 0 && gapIdx < activeStep) pct = 1;
        else if (gapIdx === activeStep) pct = partial;

        const completed = idx < activeStep;

        return (
          <Step key={label} completed={completed} sx={{ '--connector-progress': `${pct * 100}%` }}>
            <StepLabel StepIconComponent={CircleStepIcon}>{label}</StepLabel>
          </Step>
        );
      })}
    </Stepper>
  );
};

export default memo(HorizontalStepper);
