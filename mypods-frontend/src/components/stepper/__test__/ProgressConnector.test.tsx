import { describe, it, expect } from 'vitest';
import { render } from '@testing-library/react';
import '@testing-library/jest-dom';
import { Stepper, Step, StepLabel } from '@mui/material';
import ProgressConnector from '../ProgressConnector';

describe('ProgressConnector', () => {
  it('renders one connector between two steps', () => {
    const { container } = render(
      <Stepper alternativeLabel connector={<ProgressConnector />}>
        <Step>
          <StepLabel>One</StepLabel>
        </Step>
        <Step>
          <StepLabel>Two</StepLabel>
        </Step>
      </Stepper>
    );

    const connectors = container.querySelectorAll('.MuiStepConnector-root');
    expect(connectors.length).toBe(1);

    const line = container.querySelector('.MuiStepConnector-line');
    expect(line).toBeInTheDocument();
  });

  it('consumes the per-Step CSS var (--connector-progress)', () => {
    // use inline style (not sx) so JSDOM exposes the var via element.style
    const { container } = render(
      <Stepper alternativeLabel connector={<ProgressConnector />}>
        <Step>
          <StepLabel>One</StepLabel>
        </Step>
        <Step style={{ ['--connector-progress' as any]: '70%' } as React.CSSProperties}>
          <StepLabel>Two</StepLabel>
        </Step>
      </Stepper>
    );

    const steps = container.querySelectorAll('.MuiStep-root');
    const stepTwo = steps[1] as HTMLElement;

    const cssVar = stepTwo.style.getPropertyValue('--connector-progress').trim();
    expect(cssVar).toBe('70%');

    // structure still intact
    const connectors = container.querySelectorAll('.MuiStepConnector-root');
    expect(connectors.length).toBe(1);
    expect(container.querySelector('.MuiStepConnector-line')).toBeInTheDocument();
  });

  it('aligns connector relative to icon size (sets alternativeLabel offsets)', () => {
    const { container } = render(
      <div style={{ ['--icon-size' as any]: '22px' }}>
        <Stepper alternativeLabel connector={<ProgressConnector />}>
          <Step>
            <StepLabel>One</StepLabel>
          </Step>
          <Step>
            <StepLabel>Two</StepLabel>
          </Step>
          <Step>
            <StepLabel>Three</StepLabel>
          </Step>
        </Stepper>
      </div>
    );

    const connectors = container.querySelectorAll('.MuiStepConnector-root');
    expect(connectors.length).toBe(2);
  });
});
