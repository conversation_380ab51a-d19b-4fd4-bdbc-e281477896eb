import { describe, it, expect } from 'vitest';
import { render } from '@testing-library/react';
import '@testing-library/jest-dom';
import CircleStepIcon from '../CircleStepIcon';

describe('CircleStepIcon', () => {
  it('renders a hollow dot when not completed', () => {
    const { container } = render(
      <CircleStepIcon active={false} completed={false} className="" icon={undefined} />
    );
    // Hollow: no SVG, inner Box exists
    expect(container.querySelector('svg')).toBeNull();
    expect(container.querySelector('div > div')).toBeInTheDocument();
  });

  it('renders a filled check-circle SVG when completed', () => {
    const { container } = render(
      <CircleStepIcon active={false} completed className="" icon={undefined} />
    );
    expect(container.querySelector('svg')).toBeInTheDocument();
  });

  it('accepts sizing CSS variables from a parent (sanity)', () => {
    const { container } = render(
      <div
        // supply custom sizing vars on a parent
        style={
          {
            ['--icon-size' as any]: '20px',
            ['--completed-icon-scale' as any]: 0.8,
            ['--hollow-dot-scale' as any]: 0.5
          } as React.CSSProperties
        }>
        <CircleStepIcon active={false} completed className="" icon={undefined} />
      </div>
    );

    // The vars should be present on the wrapper itself
    const wrapper = container.querySelector(':scope > div') as HTMLElement;
    expect(wrapper).toBeInTheDocument();
    expect(wrapper.style.getPropertyValue('--icon-size').trim()).toBe('20px');
    expect(wrapper.style.getPropertyValue('--completed-icon-scale').trim()).toBe('0.8');
    expect(wrapper.style.getPropertyValue('--hollow-dot-scale').trim()).toBe('0.5');

    // And the component should render normally under that wrapper
    const outer = container.querySelector(':scope > div > .MuiBox-root') as HTMLElement;
    expect(outer).toBeInTheDocument();
    // We deliberately don't assert computed width/height here— JSDOM won't resolve
    // Emotion/MUI class-based CSS variables into computed layout values.
  });
});
