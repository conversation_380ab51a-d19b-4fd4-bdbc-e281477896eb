import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import HorizontalStepper from '../HorizontalStepper';

describe('HorizontalStepper', () => {
  const steps = ['Location', 'Placement', 'Confirm'];

  it('renders all step labels', () => {
    render(<HorizontalStepper steps={steps} activeStep={0} connectorProgress={0} />);
    steps.forEach((label) => {
      expect(screen.getByText(label)).toBeInTheDocument();
    });
  });

  it('marks steps strictly to the left of activeStep as completed', () => {
    const { container } = render(
      <HorizontalStepper steps={steps} activeStep={2} connectorProgress={0} />
    );

    const stepRoots = container.querySelectorAll('.MuiStep-root');
    expect(stepRoots.length).toBe(steps.length);

    // indices 0 and 1 should be completed
    expect(stepRoots[0]).toHaveClass('Mui-completed');
    expect(stepRoots[1]).toHaveClass('Mui-completed');
    // index 2 should not
    expect(stepRoots[2]).not.toHaveClass('Mui-completed');
  });

  it('shows a check-circle SVG in the icon for completed steps', () => {
    const { container } = render(
      <HorizontalStepper steps={steps} activeStep={1} connectorProgress={0.35} />
    );

    const iconContainers = container.querySelectorAll('.MuiStepLabel-iconContainer');
    expect(iconContainers.length).toBe(steps.length);

    // Step 0 is completed => should contain an SVG (Phosphor CheckCircle)
    expect(iconContainers[0].querySelector('svg')).toBeInTheDocument();

    // Step 1 is active (not completed) => should NOT contain an SVG (hollow dot)
    expect(iconContainers[1].querySelector('svg')).not.toBeInTheDocument();
  });

  it('renders one connector per gap', () => {
    const { container } = render(
      <HorizontalStepper steps={steps} activeStep={1} connectorProgress={0.4} />
    );
    const connectors = container.querySelectorAll('.MuiStepConnector-root');
    expect(connectors.length).toBe(steps.length - 1);
    // line element exists (we custom-draw bars inside it)
    expect(container.querySelector('.MuiStepConnector-line')).toBeInTheDocument();
  });
});
