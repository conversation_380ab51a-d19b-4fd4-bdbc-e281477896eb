import React, { useContext, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Grid, Typography } from '@mui/material';
import { Design } from '../../helpers/Design';
import { TranslationKeys } from '../../locales/TranslationKeys';
import {
  CloneQuoteFromOrderResponse,
  ContainerPlacement,
  UpdateMoveLegRequest
} from '../../domain/OrderEntities';
import { useUpdateMoveLeg } from '../../networkRequests/mutations/useUpdateMoveLeg';
import { GtmScheduleType } from '../../config/google/GoogleEntities';
import useOrdersContext from '../../context/OrdersContext';
import { NotificationContext } from '../notifications/NotificationContext';
import useContainerContext from '../../context/ContainerContext';
import { useGtmEvents } from '../../config/google/useGtmEvents';
import useMoveLegContext from '../../pages/HomePage/container/moveleg/MoveLegContext';
import { toServiceAddress } from '../../pages/HomePage/container/scheduling/useMoveLegAddressState';
import { UpdateMoveLegResponse } from '../../networkRequests/responseEntities/OrderAPIEntities';
import { PodsAlert, PodsAlertIcon, PodsAlertType } from './PodsAlert';
import { useAcceptInitialDeliveryPlacement } from '../../networkRequests/mutations/useAcceptInitialDeliveryPlacement';
import { ContainerPlacementModal } from '../../pages/HomePage/container/scheduling/containerplacement/ContainerPlacementModal';
import { SolidTruckIcon } from '../icons/SolidTruckIcon';
import {
  PriceDifferenceAlertContent,
  ReviewContainerPlacementContent
} from './ReviewInitialDeliveryContents';
import { ContainerPlacementProvider } from '../../pages/HomePage/container/scheduling/containerplacement/context/ContainerPlacementProvider';
import { defaultInitialDeliveryPlacement } from '../../pages/HomePage/container/scheduling/containerplacement/screenmanager/useContainerPlacementScreenManager';
import useSingleOrderContext from '../../context/SingleOrderContext';
import { getSuccessMessage } from '../../pages/HomePage/container/scheduling/ScheduleMoveLeg';
import {
  createGtmScheduleType,
  createUpdateMoveLegRequest
} from '../../pages/HomePage/container/scheduling/moveLegHelpers';

const Tx = TranslationKeys.HomePage.MoveLegs;

export const ReviewInitialDeliveryAlert = ({
  hideAlertCallback,
  currentlySelectedQuote
}: {
  hideAlertCallback: () => void;
  currentlySelectedQuote: CloneQuoteFromOrderResponse | null | undefined;
}) => {
  const { t: translate } = useTranslation();
  const acceptInitialDelivery = useAcceptInitialDeliveryPlacement();
  const { moveLeg } = useMoveLegContext();
  const { order, container } = useContainerContext();
  const { refetch } = useOrdersContext();
  const { setNotification } = useContext(NotificationContext);
  const updateMoveLeg = useUpdateMoveLeg();
  const { moveLegScheduling } = useSingleOrderContext();
  const { setIsSaving: disableOtherMoveLegUpdates, clearOrderIdAndQuote } = moveLegScheduling;
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [diffedContainerPlacement, setDiffedContainerPlacement] =
    useState<ContainerPlacement | null>(null);
  const [priceDifferenceResponse, setPriceDifferenceResponse] =
    useState<UpdateMoveLegResponse | null>();
  const gtmEvents = useGtmEvents();

  const handleAcceptInitialDeliveryPlacement = () => {
    acceptInitialDelivery.mutate(order.orderId, {
      onSuccess: () => {
        hideAlertCallback();
        refetch();
      },
      onError: () => {
        setNotification({
          isError: true,
          message: translate(TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE)
        });
      },
      onSettled: () => {
        disableOtherMoveLegUpdates(false);
      }
    });
  };

  const handleFinish = (containerPlacement: ContainerPlacement) => {
    setDiffedContainerPlacement(containerPlacement);
    setIsModalOpen(false);
    gtmEvents.successPlacementFlow({
      orderId: order.orderId,
      containerId: container.containerId,
      moveLegId: moveLeg.moveLegId,
      moveLegType: moveLeg.moveLegType
    });
    handleSave(containerPlacement);
  };

  const openContainerPlacementModal = () => {
    gtmEvents.startPlacementFlow({
      orderId: order.orderId,
      containerId: container.containerId,
      moveLegId: moveLeg.moveLegId,
      moveLegType: moveLeg.moveLegType
    });
    setIsModalOpen(true);
  };

  const handleSave = (containerPlacement: ContainerPlacement) => {
    disableOtherMoveLegUpdates(true);

    const request: UpdateMoveLegRequest = createUpdateMoveLegRequest({
      order,
      container,
      moveLeg,
      selectedDate: moveLeg.scheduledDate!,
      serviceAddress: toServiceAddress(moveLeg.displayAddress),
      completedContainerPlacement: containerPlacement,
      currentlySelectedQuote: currentlySelectedQuote,
      priceDifferenceResponse: priceDifferenceResponse,
      isCancelLeg: false,
      isUpdating: true,
      isIfOpenCalendar: false
    });

    const gtmRequest: GtmScheduleType = createGtmScheduleType(order, container, moveLeg, request);
    gtmEvents.pushMoveLegScheduleEvent('submit_edit_schedule', gtmRequest);

    updateMoveLeg.mutate(request, {
      onSuccess: (response) => {
        gtmEvents.pushMoveLegScheduleEvent('success_edit_schedule', gtmRequest);
        if (response.quoteId !== '0') {
          setPriceDifferenceResponse(response);
          return;
        }
        clearOrderIdAndQuote();
        setPriceDifferenceResponse(null);
        setNotification({
          isError: false,
          message: translate(getSuccessMessage(moveLeg))
        });

        handleAcceptInitialDeliveryPlacement();
      },
      onError: () => {
        setNotification({
          message: translate(TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE),
          isError: true
        });
      },
      onSettled: () => {
        disableOtherMoveLegUpdates(false);
      }
    });
  };

  const confirmPriceDifference = () => {
    if (diffedContainerPlacement) {
      handleSave(diffedContainerPlacement);
    }
  };

  const cancelContainerPlacementUpdate = () => {
    disableOtherMoveLegUpdates(false);
    setPriceDifferenceResponse(null);
  };

  if (acceptInitialDelivery.isPending || updateMoveLeg.isPending) {
    return (
      <PodsAlert
        alertType={PodsAlertType.SUCCESS}
        icon={PodsAlertIcon.SPINNER}
        title={translate(Tx.InitialDeliverySaveDetails.TITLE)}
        description={translate(Tx.InitialDeliverySaveDetails.SUBTITLE)}
      />
    );
  }

  return (
    <Grid container {...styles.alertContainer} gap={0}>
      <Grid item {...styles.iconContainer}>
        <SolidTruckIcon {...styles.icon} />
      </Grid>
      <Grid container item xs {...styles.mainContent}>
        <Typography {...styles.upperTitle}>
          {translate(Tx.InitialDeliveryReviewAlert.UPPER_TITLE)}
        </Typography>
        {priceDifferenceResponse?.priceDifference ? (
          <PriceDifferenceAlertContent
            onConfirm={confirmPriceDifference}
            onCancel={cancelContainerPlacementUpdate}
            isLoading={false}
            priceDifference={Number(priceDifferenceResponse?.priceDifference)}
          />
        ) : (
          <ReviewContainerPlacementContent
            onReview={openContainerPlacementModal}
            onAccept={() => {
              disableOtherMoveLegUpdates(true);
              handleAcceptInitialDeliveryPlacement();
            }}
            isLoading={false}
          />
        )}
      </Grid>
      <ContainerPlacementProvider
        existingPlacement={defaultInitialDeliveryPlacement}
        handleFinish={handleFinish}>
        <ContainerPlacementModal
          open={isModalOpen}
          handleOnClose={() => {
            setIsModalOpen(false);
          }}
        />
      </ContainerPlacementProvider>
    </Grid>
  );
};

const styles = {
  alertContainer: {
    sx: {
      flexDirection: 'row',
      background: Design.Alias.Color.successLight,
      padding: '16px 12px',
      color: Design.Alias.Color.infoDark,
      borderRadius: '4px',
      border: '1px solid rgba(27, 94, 32, 0.30)'
    }
  },
  iconContainer: {
    sx: {
      paddingRight: '10px',
      color: Design.Alias.Color.neutral700
    }
  },
  icon: {
    sx: {
      path: { fill: Design.Alias.Color.successDark },
      margin: '-2px 0 0 2px'
    }
  },
  mainContent: {
    sx: {
      gap: Design.Primitives.Spacing.xs,
      flexDirection: 'column'
    }
  },
  upperTitle: {
    sx: {
      color: Design.Alias.Color.neutral700,
      fontSize: '.75rem',
      fontWeight: 600,
      textTransform: 'uppercase'
    }
  }
};
