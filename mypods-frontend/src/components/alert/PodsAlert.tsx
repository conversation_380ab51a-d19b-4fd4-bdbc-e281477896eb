import { CircularProgress, Grid, Typography } from '@mui/material';
import React from 'react';
import { Design } from '../../helpers/Design';
import { SolidTruckIcon } from '../icons/SolidTruckIcon';
import { InfoIcon } from '../icons/InfoIcon';
import { WarningIcon } from '../icons/WarningIcon';

export enum PodsAlertType {
  INFO,
  ERROR,
  GRAY,
  SUCCESS
}

export enum PodsAlertIcon {
  TRUCK,
  INFO,
  ERROR,
  SPINNER
}

export interface PodsAlertProps {
  title: string;
  description: string;
  icon?: PodsAlertIcon;
  alertType?: PodsAlertType;
}

export const PodsAlert = ({ title, description, icon, alertType }: PodsAlertProps) => {
  const myIcon = icon ?? PodsAlertIcon.INFO;
  const myAlertType = alertType ?? PodsAlertType.INFO;
  const styles = serviceCountdownAlertStyles(alertStylesForType.get(myAlertType)!);

  const renderIcon = () => {
    switch (myIcon) {
      case PodsAlertIcon.TRUCK:
        return <SolidTruckIcon {...styles.icon} />;
      case PodsAlertIcon.INFO:
        return <InfoIcon sx={{ height: '16px', width: '16px', ...styles.icon.sx }} />;
      case PodsAlertIcon.ERROR:
        return <WarningIcon sx={{ height: '16px', width: '16px', ...styles.icon.sx }} />;
      case PodsAlertIcon.SPINNER:
        // only functional for success, for now.
        return <CircularProgress size={16} color="success" />;
    }
  };

  return (
    <Grid container {...styles.serviceCountdownAlert}>
      <Grid item {...styles.iconContainer}>
        {renderIcon()}
      </Grid>
      <Grid container item xs {...styles.serviceTextContainer}>
        <Typography {...styles.serviceCountdownText}>{title}</Typography>
        <Typography
          {...styles.serviceHelperText}
          dangerouslySetInnerHTML={{ __html: description }}
        />
      </Grid>
    </Grid>
  );
};

interface PodsAlertTypeStyles {
  mainBackground: string;
  fontColor: string;
  iconColor: string;
}

const alertStylesForType = new Map<PodsAlertType, PodsAlertTypeStyles>([
  [
    PodsAlertType.INFO,
    {
      mainBackground: Design.Alias.Color.infoLight,
      fontColor: Design.Alias.Color.infoDark,
      iconColor: Design.Alias.Color.infoDark
    }
  ],
  [
    PodsAlertType.ERROR,
    {
      mainBackground: Design.Alias.Color.errorLight,
      fontColor: Design.Alias.Color.errorDark,
      iconColor: Design.Alias.Color.primary500
    }
  ],
  [
    PodsAlertType.GRAY,
    {
      mainBackground: Design.Alias.Color.neutral200,
      fontColor: Design.Alias.Color.neutral900,
      iconColor: Design.Alias.Color.neutral900
    }
  ],
  [
    PodsAlertType.SUCCESS,
    {
      mainBackground: Design.Alias.Color.successLight,
      fontColor: Design.Alias.Color.neutral900,
      iconColor: Design.Alias.Color.successMain
    }
  ]
]);

const serviceCountdownAlertStyles = (alertTypeStyles: PodsAlertTypeStyles) => ({
  serviceCountdownAlert: {
    sx: {
      flexDirection: 'row',
      background: alertTypeStyles.mainBackground,
      padding: '8px 16px',
      borderRadius: '4px',
      color: Design.Alias.Color.infoDark
    }
  },
  iconContainer: {
    sx: {
      paddingTop: '7px',
      paddingRight: '12px',
      color: alertTypeStyles.fontColor
    }
  },
  icon: {
    sx: {
      path: { fill: alertTypeStyles.iconColor }
    }
  },
  serviceTextContainer: {
    sx: {
      gap: Design.Primitives.Spacing.xxs,
      flexDirection: 'column'
    }
  },
  serviceCountdownText: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.MdBold,
      color: alertTypeStyles.fontColor
    }
  },
  serviceHelperText: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.Sm,
      color: alertTypeStyles.fontColor
    }
  }
});
