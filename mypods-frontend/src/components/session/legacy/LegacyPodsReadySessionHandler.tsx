import React from 'react';
import { redirectToLoginWithStatus } from '../../../context/ApigeeContext';
import { useSessionHandler } from '../useSessionHandler';
import { useLegacyStartPodsReadySession } from '../../../networkRequests/legacy/queries/podsReady/useLegacyStartPodsReadySession';

export const LegacyPodsReadySessionHandler = ({ children }: { children: React.ReactNode }) => {
  const { refetch } = useLegacyStartPodsReadySession();

  useSessionHandler({
    refreshSession: async () => {
      await refetch();
    },
    lastRefreshKey: 'pods-ready-session-last-refreshed',
    lastActivityKey: 'pods-ready-session-last-activity',
    sessionTimeoutMinutesKey: 'pods-ready-session-timeout-minutes',
    onSessionExpire: () => {
      redirectToLoginWithStatus('SESSION_EXPIRED');
    }
  });

  return children;
};
