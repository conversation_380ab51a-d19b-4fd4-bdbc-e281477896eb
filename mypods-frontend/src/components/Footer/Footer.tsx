import { Grid, Link, Typography, useMediaQuery } from '@mui/material';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router';
import { Design } from '../../helpers/Design';
import { theme } from '../../PodsTheme';
import { TranslationKeys } from '../../locales/TranslationKeys';
import { LanguageSelect } from './LanguageSelect';
import { ROUTES } from '../../Routes';
import { FinancingDisclosure } from './FinancingDisclosure';
import { useGetPaymentMethods } from '../../networkRequests/queries/useGetPaymentMethods';
import {
  financingEligible,
  findMostExpensiveEligibleOrder
} from '../../pages/BillingPage/utility/FinancingEligibility';
import { useGetCustomer } from '../../networkRequests/queries/useGetCustomer';
import { ACORN_FINANCING_ENABLED, useFeatureFlags } from '../../helpers/useFeatureFlags';
import { useGetCustomerOrders } from '../../networkRequests/queries/useGetCustomerOrders';

export const Footer: React.FC = () => {
  const { t: translate } = useTranslation();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const location = useLocation();
  const { customerOrders } = useGetCustomerOrders();
  const { isAcornFinancingEnabled } = useFeatureFlags([ACORN_FINANCING_ENABLED]);
  const acornFinancingEnabled = isAcornFinancingEnabled();
  const { customer } = useGetCustomer();
  const { paymentMethods } = useGetPaymentMethods();
  const mostExpensiveOrder = findMostExpensiveEligibleOrder(customerOrders);
  const isFinancingEligible = financingEligible(
    customer,
    acornFinancingEnabled,
    paymentMethods,
    customerOrders,
    mostExpensiveOrder
  );
  const style = styles(isMobile);
  const showFinancingFooter =
    isFinancingEligible &&
    (location.pathname === ROUTES.BILLING || location.pathname === ROUTES.HOME);

  return (
    <Grid container direction="row" {...style.footerContainer}>
      <Grid container {...style.footerInnerContainer}>
        <Grid container {...style.copyrightAndLinksContainer}>
          <Grid container item {...style.copyrightContainer}>
            <Typography variant="subtitle2">
              {translate(TranslationKeys.Footer.COPYRIGHT, {
                currentYear: new Date().getFullYear()
              })}
            </Typography>
          </Grid>

          <Grid container item gap={2} alignItems="start" maxWidth="fit-content">
            <Grid item maxWidth="fill-available">
              <Link
                href={translate(TranslationKeys.Footer.PRIVACY_POLICY_URL) ?? undefined}
                target="_blank"
                rel="noopener noreferrer"
                {...style.link}>
                {translate(TranslationKeys.Footer.PRIVACY_POLICY)}
              </Link>
            </Grid>
            <Grid item>
              <Link
                href={translate(TranslationKeys.Footer.TERMS_AND_CONDITIONS_URL) ?? undefined}
                target="_blank"
                rel="noopener noreferrer"
                {...style.link}>
                {translate(TranslationKeys.Footer.TERMS_AND_CONDITIONS)}
              </Link>
            </Grid>
            <Grid item>
              <Link
                role="button"
                className="ot-floating-button__open"
                underline="none"
                target="_blank"
                rel="noopener noreferrer"
                {...style.link}>
                {translate(TranslationKeys.Footer.MANAGE_COOKIE_PREFERENCES)}
              </Link>
            </Grid>
            <Grid item>
              <LanguageSelect />
            </Grid>
          </Grid>
          {showFinancingFooter && <FinancingDisclosure orderTotal={mostExpensiveOrder?.price} />}
        </Grid>
      </Grid>
    </Grid>
  );
};

const styles = (isMobile: boolean) => ({
  footerContainer: {
    sx: {
      backgroundColor: Design.Alias.Color.neutral100,
      direction: 'row',
      justifyContent: 'center'
    }
  },
  footerInnerContainer: {
    sx: {
      justifyContent: isMobile ? 'start' : 'space-between',
      flexDirection: isMobile ? 'column-reverse' : 'row',
      maxWidth: '1440px'
    }
  },
  copyrightAndLinksContainer: {
    sx: {
      flexDirection: isMobile ? 'column' : 'row',
      maxWidth: 'fill-available',
      justifyContent: 'space-between',
      rowGap: isMobile ? Design.Primitives.Spacing.xxs : Design.Primitives.Spacing.xs,
      marginX: isMobile ? Design.Primitives.Spacing.md : '2rem',
      marginY: isMobile ? Design.Primitives.Spacing.lgPlus : Design.Primitives.Spacing.md
    }
  },
  copyrightContainer: {
    sx: {
      paddingTop: isMobile ? 0 : Design.Primitives.Spacing.xxxs,
      maxWidth: 'fit-content'
    }
  },
  link: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.Xs,
      color: Design.Alias.Color.accent800,
      textDecoration: 'none',
      cursor: 'pointer'
    }
  }
});
