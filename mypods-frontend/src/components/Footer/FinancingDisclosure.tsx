import React, { useEffect } from 'react';

type Props = {
  orderTotal: number;
};

const ORDER_MIN = 1_000;
const ORDER_MAX = 100_000;

export const FinancingDisclosure = ({ orderTotal }: Props) => {
  acornDisclosureParameters.loanAmount = orderTotal;
  const showFinancingFooter = orderTotal >= ORDER_MIN && orderTotal < ORDER_MAX;
  useEffect(() => {
    if (showFinancingFooter) {
      setAcornDisclosureWidget('acorn-pods-disclosure');
    }
  }, [showFinancingFooter]);
  return (
    <>
      {showFinancingFooter && (
        // @ts-expect-error : this is a custom element added by the acornDisclosure script
        <acorn-pods-lender-disclosures id="acorn-pods-disclosure" />
      )}
      {!showFinancingFooter && <div> </div>}
    </>
  );
};
