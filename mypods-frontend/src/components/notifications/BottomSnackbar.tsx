import { Alert, Snackbar } from '@mui/material';
import React, { useLayoutEffect, useState } from 'react';
import { CheckmarkIcon } from '../icons/CheckmarkIcon';
import { ExclamationIcon } from '../icons/ExclamationIcon';

export interface Notification {
  message: string;
  isError?: boolean;
}

export interface BottomSnackbarProps {
  // Setting a new message opens snackbar for 5 seconds
  notification?: Notification;
  // Clearing message from parent closes snackbar
  onNotificationCleared: () => void;
}

export const BottomSnackbar: React.FC<BottomSnackbarProps> = ({
  notification,
  onNotificationCleared
}) => {
  const [open, setOpen] = useState<boolean>(false);
  // Storing last message so the closing animation renders it for that extra moment
  const [lastMessage, setLastNotification] = useState<Notification | undefined>(notification);

  useLayoutEffect(() => {
    if (notification != null) {
      setLastNotification(notification);
      setOpen(false);
      setOpen(true);
    } else {
      setOpen(false);
    }
  }, [notification]);

  const handleClose = () => {
    setOpen(false);
    onNotificationCleared();
  };

  return (
    <Snackbar
      open={open}
      autoHideDuration={500000}
      anchorOrigin={{
        vertical: 'bottom',
        horizontal: 'center'
      }}
      onClose={handleClose}>
      <Alert
        icon={lastMessage?.isError ? <ExclamationIcon /> : <CheckmarkIcon />}
        onClose={() => {
          setOpen(false);
        }}
        severity={lastMessage?.isError ? 'error' : 'success'}
        data-testid="notification-alert"
        variant="filled">
        {lastMessage?.message}
      </Alert>
    </Snackbar>
  );
};
