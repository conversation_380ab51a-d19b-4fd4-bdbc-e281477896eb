import React from 'react';
import { TruckIcon } from '@phosphor-icons/react';
import { css } from 'pods-component-library/styled-system/css';
import { Box } from 'pods-component-library/src/components/Layout/Box';
import { Design } from '../../../helpers/Design';

interface TimelineTruckIconProps {
  'data-testid'?: string;
}

export const TimelineTruckIcon = (props: TimelineTruckIconProps) => (
  <Box css={stylesheet.container}>
    <TruckIcon
      size={14}
      weight="fill"
      color={Design.Alias.Color.secondary900}
      style={{
        transform: 'scale(1.4)',
        transformOrigin: 'center',
        backgroundColor: 'white'
      }}
      {...props}
    />
  </Box>
);

const stylesheet = {
  container: css.raw({
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
  })
};
