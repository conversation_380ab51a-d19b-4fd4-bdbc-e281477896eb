import React from 'react';
import { CheckCircleIcon } from '@phosphor-icons/react';
import { css } from 'pods-component-library/styled-system/css';
import { Box } from 'pods-component-library/src/components/Layout/Box';
import { Design } from '../../../helpers/Design';

interface TimelineCheckmarkCircleProps {
  'data-testid'?: string;
}

export const TimelineCheckmarkCircleIcon = (props: TimelineCheckmarkCircleProps) => (
  <Box css={stylesheet.container}>
    <CheckCircleIcon
      size={14}
      weight="fill"
      color={Design.Alias.Color.secondary900}
      style={{
        transform: 'scale(1.3)',
        transformOrigin: 'center'
      }}
      {...props}
    />
  </Box>
);

const stylesheet = {
  container: css.raw({
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
  })
};
