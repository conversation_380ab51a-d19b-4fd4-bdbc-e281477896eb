import React from 'react';
import { ArrowLeftIcon } from '@phosphor-icons/react';
import { css } from 'pods-component-library/styled-system/css';
import { Box } from 'pods-component-library/src/components/Layout/Box';
import { Design } from '../../helpers/Design';

interface LeftArrowIconProps {
  'data-testid'?: string;
}

export const LeftArrowIcon = (props: LeftArrowIconProps) => (
  <Box css={stylesheet.container}>
    <ArrowLeftIcon
      size={14}
      weight="regular"
      color={Design.Alias.Color.secondary500}
      style={{
        transform: 'scale(1.4)',
        transformOrigin: 'center',
        backgroundColor: 'white'
      }}
      {...props}
    />
  </Box>
);

const stylesheet = {
  container: css.raw({
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
  })
};
