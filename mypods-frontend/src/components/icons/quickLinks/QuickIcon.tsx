import React from 'react';
import { Icon } from '@phosphor-icons/react';
import { css } from 'pods-component-library/styled-system/css';
import { Box } from 'pods-component-library/src/components/Layout/Box';
import { Design } from '../../../helpers/Design';

type QuickLinkProps = {
  icon: Icon;
};

export const QuickIcon = ({ icon: IconComponent }: QuickLinkProps) => (
  <Box css={stylesheet.container}>
    <IconComponent size={24} weight="light" color={Design.Alias.Color.secondary500} />
  </Box>
);

const stylesheet = {
  container: css.raw({
    width: '40px',
    height: '40px',
    borderRadius: '50%',
    backgroundColor: 'infoLight',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
  })
};
