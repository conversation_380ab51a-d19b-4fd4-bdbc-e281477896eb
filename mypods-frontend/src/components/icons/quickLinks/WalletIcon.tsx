import React from 'react';
import { Wallet as PhosphorWalletIcon } from '@phosphor-icons/react';
import { css } from 'pods-component-library/styled-system/css';
import { Box } from 'pods-component-library/src/components/Layout/Box';
import { Design } from '../../../helpers/Design';

export const WalletIcon = () => (
  <Box css={cssStyles.container}>
    <PhosphorWalletIcon size={24} weight="light" color={Design.Alias.Color.secondary500} />
  </Box>
);

const cssStyles = {
  container: css.raw({
    width: '40px',
    height: '40px',
    borderRadius: '50%',
    backgroundColor: 'infoLight',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
  })
};
