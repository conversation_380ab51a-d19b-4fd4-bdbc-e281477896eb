import React from 'react';
import { css } from 'pods-component-library/styled-system/css';
import { Box } from 'pods-component-library/src/components/Layout/Box';
import { useTranslation } from 'react-i18next';
import homeDepot from '../../../assets/THD_logo.png';
import { TranslationKeys } from '../../../locales/TranslationKeys';

export const HomeDepotIcon = () => {
  const { t: translate } = useTranslation();
  return (
    <Box css={stylesheet.container}>
      <img
        alt={translate(TranslationKeys.HomePage.Sidebar.HomeDepot.ICON_ALT_TEXT)}
        src={homeDepot}
      />
    </Box>
  );
};

const stylesheet = {
  container: css.raw({
    width: '40px',
    height: '40px',
    borderRadius: '50%',
    backgroundColor: '#FF6600',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
  })
};
