import React from 'react';
import { Grid } from '@mui/material';
import { Button } from 'pods-component-library';
import { css } from 'pods-component-library/styled-system/css';
import { useTranslation } from 'react-i18next';
import { PencilSimpleIcon } from '@phosphor-icons/react';
import { Design } from '../../helpers/Design';
import { TranslationKeys } from '../../locales/TranslationKeys';
import { formatToLocale } from '../../helpers/dateHelpers';
import { Customer } from '../../networkRequests/responseEntities/CustomerEntities';

interface Props {
  isSigned: boolean;
  isDisabled?: boolean;
  handleSignClicked: () => void;
  customer: Customer;
}

export const AddSignatureButtonCustomer = ({
  isSigned,
  handleSignClicked,
  isDisabled = false,
  customer
}: Props) => (
  <AddSignatureButton
    firstName={customer.firstName}
    lastName={customer.lastName}
    customerId={customer.id}
    isSigned={isSigned}
    handleSignClicked={handleSignClicked}
    isDisabled={isDisabled}
  />
);

interface FullProps {
  firstName: string;
  lastName: string;
  customerId: string;
  isSigned: boolean;
  isDisabled?: boolean;
  handleSignClicked: () => void;
}

export const AddSignatureButton = ({
  firstName,
  lastName,
  customerId,
  isSigned,
  handleSignClicked,
  isDisabled = false
}: FullProps) => {
  const { t: translate } = useTranslation();
  const Tx = TranslationKeys.CommonComponents.AddSignature;
  return (
    <>
      {!isSigned ? (
        <Grid container>
          <Button
            variant="filled"
            buttonSize="large"
            color="primary"
            isDisabled={isDisabled}
            onPress={() => {
              handleSignClicked();
            }}
            css={stylesheet.button}>
            <PencilSimpleIcon size={22} /> {translate(Tx.BUTTON_TEXT)}
          </Button>
        </Grid>
      ) : (
        <Grid container {...styles.signedContainer}>
          <Grid {...styles.label}>{translate(Tx.ELECTRONICALLY_SIGNED)}</Grid>
          <Grid container {...styles.name}>
            {`${firstName} ${lastName}`}
          </Grid>
          <Grid container {...styles.date}>
            <span>{translate(Tx.CUSTOMER_ID, { customerId })}</span>&nbsp;|&nbsp;
            <span>{`${translate(Tx.SIGNED_DATE_LABEL)} ${formatToLocale(new Date())}`}</span>
          </Grid>
        </Grid>
      )}
    </>
  );
};

const styles = {
  signedContainer: {
    sx: {
      border: `1px ${Design.Alias.Color.neutral300} solid`,
      borderRadius: '4px',
      width: 'fit-content',
      marginTop: '9px'
    }
  },
  label: {
    sx: {
      marginTop: '-9px',
      marginLeft: '10px',
      padding: '2px',
      border: `1px ${Design.Alias.Color.neutral300} solid`,
      ...Design.Alias.Text.BodyUniversal.Xs,
      color: Design.Alias.Color.neutral700,
      lineHeight: Design.Primitives.Spacing.xs,
      background: 'white'
    }
  },
  name: {
    sx: {
      paddingTop: '4px',
      marginX: '12px',
      borderBottom: `2px ${Design.Alias.Color.neutral300} solid`,
      ...Design.Alias.Text.Signature.Regular
    }
  },
  date: {
    sx: {
      paddingTop: '5px',
      marginBottom: '10px',
      marginX: '12px',
      ...Design.Alias.Text.BodyUniversal.Xs,
      color: Design.Alias.Color.neutral700
    }
  }
};

const stylesheet = {
  button: css.raw({
    paddingY: '12px',
    paddingX: '16px',
    width: 'fit-content'
  })
};
