import React from 'react';
import { Grid, Link } from '@mui/material';
import ArrowBackIosNewRoundedIcon from '@mui/icons-material/ArrowBackIosNewRounded';
import { To, useNavigate } from 'react-router';
import { SvgIconProps } from '@mui/material/SvgIcon/SvgIcon';
import { useTranslation } from 'react-i18next';
import { Design } from '../../helpers/Design';
import { TranslationKeys } from '../../locales/TranslationKeys';

export type BackLinkProps = {
  handleClick?: () => void;
  route?: To | -1;
  state?: Object;
  style?: any;
};

export const BackLink: React.FC<BackLinkProps> = ({
  handleClick,
  route,
  state,
  style
}: BackLinkProps) => {
  const { t: translate } = useTranslation();
  const navigate = useNavigate();

  const onClick = () => {
    if (route === undefined && handleClick) {
      handleClick();
    }
    if (typeof route === 'number') {
      navigate(route);
    } else if (state == null) {
      navigate(route!);
    } else {
      navigate(route!, { state });
    }
  };

  return (
    <Grid {...styles}>
      <Link
        onClick={onClick}
        style={style}
        data-testid="back-link"
        aria-label={translate(TranslationKeys.CommonComponents.BACK_BUTTON)}>
        <ArrowBackIosNewRoundedIcon {...iconStyles} />
      </Link>
    </Grid>
  );
};

// -- styles --
const styles = {
  sx: {
    cursor: 'pointer',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Design.Alias.Color.secondary700,
    height: '40px',
    maxWidth: '40px',
    borderRadius: '20px'
  }
};

const iconStyles: SvgIconProps = {
  style: { color: Design.Alias.Color.neutral100, marginRight: '2px', marginTop: '2px' }
};
