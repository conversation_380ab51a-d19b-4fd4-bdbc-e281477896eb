import React from 'react';
import { Grid, Link } from '@mui/material';
import ArrowBackIosNewRoundedIcon from '@mui/icons-material/ArrowBackIosNewRounded';
import { To, useNavigate } from 'react-router';
import { SvgIconProps } from '@mui/material/SvgIcon/SvgIcon';
import { useTranslation } from 'react-i18next';
import { Design } from '../../helpers/Design';
import { TranslationKeys } from '../../locales/TranslationKeys';

export type BackLinkProps = {
  handleClick?: () => void;
  route?: To | -1;
  state?: object;
  style?: any;
  label?: string;
};

export const BackHyperLink: React.FC<BackLinkProps> = ({
  handleClick,
  route,
  state,
  style,
  label
}: BackLinkProps) => {
  const { t: translate } = useTranslation();
  const navigate = useNavigate();

  const onClick = () => {
    if (route === undefined && handleClick) {
      handleClick();
    }
    if (typeof route === 'number') {
      navigate(route);
    } else if (state == null) {
      navigate(route!);
    } else {
      navigate(route!, { state });
    }
  };

  return (
    <Grid {...styles}>
      <Link
        onClick={onClick}
        style={style || linkStyle}
        data-testid="back-link"
        aria-label={translate(TranslationKeys.CommonComponents.BACK_BUTTON)}>
        <ArrowBackIosNewRoundedIcon {...iconStyles} />
        <span style={textStyle}>{label}</span>
      </Link>
    </Grid>
  );
};

// -- styles --
const styles = {
  sx: {
    cursor: 'pointer',
    display: 'flex',
    alignItems: 'center'
  }
};

const linkStyle = {
  display: 'flex',
  alignItems: 'center',
  textDecoration: 'none',
  color: 'inherit'
};

const textStyle = {
  fontSize: '16px',
  display: 'inline-block',
  verticalAlign: 'middle'
};

const iconStyles: SvgIconProps = {
  style: { color: Design.Alias.Color.accent600, height: '15px' }
};
