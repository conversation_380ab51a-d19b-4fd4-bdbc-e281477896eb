import React, { useCallback, useContext, useState } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import { Box, Grid, useMediaQuery } from '@mui/material';
import { useResizeObserver } from '@wojtekmaj/react-hooks';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
import { RentalAgreementsTitle } from './RentalAgreementsTitle';
import { Design } from '../../../helpers/Design';
import { DocumentActionsBar } from './DocumentActionsBar';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { theme } from '../../../PodsTheme';
import { PageLayout } from '../../PageLayout';
import { NotificationContext } from '../../notifications/NotificationContext';
import { RentalAgreementHeader } from './RentalAgreementHeader';
import { OutstandingRentalAgreement } from '../../../networkRequests/responseEntities/AuthorizationEntities';
import {
  GtmAgreementType,
  gtmAgreementTypeFromRentalAgreement,
  GtmError
} from '../../../config/google/GoogleEntities';
import { createGtmErrorRequest } from '../../../config/google/googleAnalyticsUtils';
import { OrderDocument } from '../../../domain/DocumentEntities';
import { PodsReadyRoutes } from '../../../PodsReadyRoutes';

// Polyfill for Promise.withResolvers so that older browsers can view rental agreements
// withResolvers is only available in Node >= 22, or behind feature flag ' --js-promise-withresolvers'
// we will need to use a legacy pdf worker that does not use Promise.withResolvers
// see discussion at https://github.com/wojtekmaj/react-pdf/issues/1811
// @ts-expect-error withResolvers does not exist on type PromiseConstructor (until node 22)
if (typeof Promise.withResolvers === 'undefined') {
  if (window)
    // @ts-expect-error This does not exist outside of polyfill which this is doing
    window.Promise.withResolvers = () => {
      let resolve;
      let reject;
      const promise = new Promise((res, rej) => {
        resolve = res;
        reject = rej;
      });
      return { promise, resolve, reject };
    };
  pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/legacy/build/pdf.worker.min.mjs`;
} else {
  pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.mjs`;
}

const MAX_WIDTH: number = 1000;
const resizeObserverOptions = {};

export interface PdfRentalAgreementViewerProps {
  onAccept: () => void;
  pdfUrl: string;
  outstandingRentalAgreement: OutstandingRentalAgreement | OrderDocument;
  acceptIsLoading: boolean;
  isInterFranchise: boolean;
  podsReadyTaskPage: boolean;
  useGtmEvents: () => {
    declineAgreement: (agreementType: GtmAgreementType, orderId: string) => void;
    errorEvent: (args: GtmError) => void;
  };
}

export const PdfRentalAgreementViewer = ({
  onAccept,
  pdfUrl,
  outstandingRentalAgreement,
  acceptIsLoading,
  isInterFranchise,
  podsReadyTaskPage,
  useGtmEvents
}: PdfRentalAgreementViewerProps) => {
  const { setNotification } = useContext(NotificationContext);
  const { t: translate } = useTranslation();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const gtmEvents = useGtmEvents();
  const navigate = useNavigate();
  const styles = viewerStyles(isMobile);
  const [numPages, setNumPages] = useState<number>();
  const [containerRef, setContainerRef] = useState<HTMLElement | null>(null);
  const [containerWidth, setContainerWidth] = useState<number>();
  const [showScrollHelper, setShowScrollHelper] = useState<boolean>(true);
  const [acceptanceEnabled, setAcceptanceEnabled] = useState<boolean>(false);
  const agreementType = gtmAgreementTypeFromRentalAgreement(isInterFranchise ? 'IF' : 'LOCAL');

  const getOnScroll = ({ target }: React.UIEvent) => {
    const { scrollHeight, scrollTop, clientHeight } = target as HTMLDivElement;
    const scrollTopMax = scrollHeight - clientHeight;

    const closeToPageEnd = scrollTopMax - 100;
    if (scrollTop > closeToPageEnd && showScrollHelper) setShowScrollHelper(false);
    if (scrollTop > closeToPageEnd && !acceptanceEnabled) setAcceptanceEnabled(true);
  };

  const onResize = useCallback<ResizeObserverCallback>((entries) => {
    const [entry] = entries;

    if (entry) {
      setContainerWidth(entry.contentRect.width);
    }
  }, []);
  useResizeObserver(containerRef, resizeObserverOptions, onResize);

  const onDocumentLoadSuccess = ({ numPages: nextNumPages }: { numPages: number }) => {
    setNumPages(nextNumPages);
  };

  const onDecline = () => {
    setNotification({
      message: translate(TranslationKeys.Onboarding.RentalAgreements.DECLINE_ERROR),
      isError: true
    });
    gtmEvents.declineAgreement(agreementType, outstandingRentalAgreement.orderId);
    gtmEvents.errorEvent(
      createGtmErrorRequest(
        'Customer has declined signing Rental Agreement',
        'agreement',
        'front_end'
      )
    );
    if (podsReadyTaskPage) {
      navigate(PodsReadyRoutes.TASKS);
    }
  };

  return (
    <Grid
      sx={{
        paddingX: isMobile ? '12px' : '104px',
        paddingTop: Design.Primitives.Spacing.lgPlus,
        overflowY: 'auto',
        justifyContent: 'center',
        display: 'flex',
        height: 'calc(100vh - 80px)', // 80px is the height of the DocumentActionsBar
        zIndex: 999
      }}
      onScroll={getOnScroll}>
      <PageLayout columnsMd={12} columnsLg={8}>
        <RentalAgreementsTitle />
        <Box {...styles.documentsContainer}>
          <RentalAgreementHeader
            outstandingRentalAgreement={outstandingRentalAgreement}
            isInterFranchise={isInterFranchise}
          />
          <Box {...styles.pdfWrapper}>
            <Box {...styles.pdfView} ref={setContainerRef}>
              <Document file={pdfUrl} onLoadSuccess={onDocumentLoadSuccess}>
                {Array.from(new Array(numPages), (_el, index) => (
                  <Page
                    renderTextLayer={false}
                    renderAnnotationLayer={false}
                    key={`page_${index + 1}`}
                    pageNumber={index + 1}
                    width={containerWidth ? Math.min(containerWidth, MAX_WIDTH) : MAX_WIDTH}
                  />
                ))}
              </Document>
            </Box>
          </Box>
        </Box>
        <DocumentActionsBar
          showScroll={showScrollHelper}
          acceptanceEnabled={acceptanceEnabled}
          onAccept={onAccept}
          acceptIsLoading={acceptIsLoading}
          onDecline={onDecline}
        />
      </PageLayout>
    </Grid>
  );
};

const viewerStyles = (isMobile: boolean) => ({
  wrapper: {
    sx: {
      flexDirection: 'column',
      alignContent: 'center',
      backgroundColor: 'white'
    }
  },
  mainContainer: {
    sx: {
      marginTop: Design.Primitives.Spacing.lgPlus,
      gap: Design.Primitives.Spacing.xxs,
      flexDirection: 'column'
    }
  },
  documentsContainer: {
    sx: {
      border: `1px solid ${Design.Alias.Color.neutral300}`,
      backgroundColor: Design.Alias.Color.neutral100,
      borderRadius: '8px',
      boxShadow: `0px 4px 6px 0px rgba(0, 0, 0, 0.10)`,
      display: 'flex',
      flexDirection: 'column',
      gap: '8px',
      marginBottom: isMobile ? '24px' : '55px'
    }
  },
  pdfWrapper: {
    style: {
      height: '100%'
    },
    sx: {
      paddingX: '10px',
      paddingTop: isMobile ? 0 : '10px',
      paddingBottom: '10px'
    }
  },
  pdfView: {
    border: `1px solid ${Design.Alias.Color.neutral300}`,
    borderRadius: '4px',
    overflow: 'hidden'
  }
});
