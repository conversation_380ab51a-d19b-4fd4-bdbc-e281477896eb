import React from 'react';
import { Grid } from '@mui/material';
import { Button } from 'pods-component-library';
import { css } from 'pods-component-library/styled-system/css';
import { useTranslation } from 'react-i18next';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { FixedBottomContainer } from '../../FixedBottomContainer';

export interface ContinueButtonProps {
  onAccept: () => void;
  isMobile: boolean;
  isSigned: boolean;
  isProcessing: boolean;
}

export const ContinueButton = ({
  onAccept,
  isMobile,
  isSigned,
  isProcessing
}: ContinueButtonProps) => {
  const { t } = useTranslation();
  const handleAccept = () => {
    if (!isProcessing) {
      onAccept();
    }
  };

  const renderButton = () => (
    <Button
      variant="filled"
      buttonSize="large"
      color="primary"
      isDisabled={!isSigned}
      onPress={() => {
        handleAccept();
      }}
      isLoading={isProcessing}
      css={stylesheet.continueButton}>
      {t(TranslationKeys.CommonComponents.CONTINUE_BUTTON)}
    </Button>
  );
  return (
    <Grid container>
      {isMobile ? <FixedBottomContainer>{renderButton()}</FixedBottomContainer> : renderButton()}
    </Grid>
  );
};

const stylesheet = {
  continueButton: css.raw({
    smDown: {
      width: '100%'
    },
    sm: {
      paddingY: '12px',
      paddingX: '40px',
      width: 'fit-content'
    }
  })
};
