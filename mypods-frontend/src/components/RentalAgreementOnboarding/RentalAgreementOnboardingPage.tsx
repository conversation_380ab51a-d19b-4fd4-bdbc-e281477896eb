import React, { ReactNode, useContext, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { datadogLogs } from '@datadog/browser-logs';
import { useLocation, useNavigate } from 'react-router';
import isEmpty from 'lodash/isEmpty';
import { NotificationContext } from '../notifications/NotificationContext';
import { useAcceptRentalAgreement } from '../../networkRequests/mutations/useAcceptRentalAgreement';
import { useGetOrderDocuments } from '../../networkRequests/queries/v2/useGetOrderDocuments';
import { useGetCustomer } from '../../networkRequests/queries/useGetCustomer';
import {
  LINK_FLOW_RENTAL_AGREEMENT_ENABLED,
  PODS_READY_SINGLE_ORDER_ENABLED,
  useFeatureFlags
} from '../../helpers/useFeatureFlags';
import { RentalAgreementEvents } from './RentalAgreementEvents';
import {
  CORPORATE_RENTAL_AGREEMENT,
  LOCAL_RENTAL_AGREEMENT_PATH
} from '../../networkRequests/MyPodsConstants';
import { fillableRentalAgreementCompanyCodeMap } from './fillableRentalAgreementCompanyCodeMap';
import { AcceptRentalAgreementRequest } from '../../networkRequests/responseEntities/DocumentApiEntities';
import { getTimezoneData } from '../../helpers/dateHelpers';
import { TranslationKeys } from '../../locales/TranslationKeys';
import { LinkFlowRentalAgreementViewer } from './RentalAgreementDocumentViewer/LinkFlowRentalAgreementViewer';
import { PdfRentalAgreementViewer } from './RentalAgreementDocumentViewer/PdfRentalAgreementViewer';
import { PodsReadyRoutes } from '../../PodsReadyRoutes';
import { useGtmEventsWithCustomer } from '../../config/google/useGtmEvents';
import { usePoetSplitEvents } from '../../config/usePoetSplitEvents';
import { isPodsReadyLocation } from '../../helpers/podsReadyHelpers';
import { useShowPodsReadySingleOrder } from '../../helpers/useShowPodsReadySingleOrder';
import { SplitEventType } from '../../config/SplitEventTypes';
import LoadingScreen from '../Loading/LoadingScreen';
import { ROUTES } from '../../Routes';
import { useAcceptRentalAgreements } from '../../networkRequests/mutations/useAcceptRentalAgreements';

type RentalAgreementOnboardingPageProps = { children?: ReactNode };
const CORPORATE_COMPANY_CODE = 'PEIU';
const DEFAULT_RENTAL_AGREEMENT_IDENTITY = 'CONTRACT';

export const RentalAgreementOnboardingPage = ({ children }: RentalAgreementOnboardingPageProps) => {
  const { setNotification } = useContext(NotificationContext);
  const { t: translate } = useTranslation();
  const { customer } = useGetCustomer();
  const gtmEvents = useGtmEventsWithCustomer(customer);
  const splitEvents = usePoetSplitEvents();
  const { isReady, isPodsReadySingleOrderEnabled } = useFeatureFlags([
    PODS_READY_SINGLE_ORDER_ENABLED
  ]);
  const navigate = useNavigate();
  const location = useLocation();
  const acceptRentalAgreement = useAcceptRentalAgreement();
  const acceptMultipleRentalAgreements = useAcceptRentalAgreements();
  const {
    outstandingRentalAgreements,
    status,
    markRentalAgreementAsCompleted,
    markAllRentalAgreementsAsCompleted,
    outstandingMothAgreements
  } = useGetOrderDocuments();
  const { isLinkFlowRentalAgreementEnabled } = useFeatureFlags([
    LINK_FLOW_RENTAL_AGREEMENT_ENABLED,
    PODS_READY_SINGLE_ORDER_ENABLED
  ]);
  const [firstAgreement, ...remainingAgreements] = outstandingRentalAgreements;
  const hasOutstandingRentalAgreements = outstandingRentalAgreements.length > 0;
  const hasOutstandingMothForms = outstandingMothAgreements.length > 0;
  const [numberOfCurrentAgreement, setNumberOfCurrentAgreement] = useState<number>(1);
  const { showPodsReadySingleOrder, isFetching } = useShowPodsReadySingleOrder();
  let mappedCompanyCode: string | undefined;
  const eventMonitor = RentalAgreementEvents({
    isInterFranchise: firstAgreement?.isInterFranchise ?? false,
    orderId: firstAgreement?.orderId ?? '',
    customer
  });
  const navigateToPodsReadyTasks = showPodsReadySingleOrder && !isPodsReadyLocation(location);
  useEffect(() => {
    if (
      isReady &&
      !isPodsReadySingleOrderEnabled() &&
      (hasOutstandingRentalAgreements || hasOutstandingMothForms)
    ) {
      // if you have pods-ready enabled, the PODS_READY_START event is emitted when you land on the pods ready task page
      // If you don't have pods ready enabled, we're calling the start of the flow when you land on the home page
      splitEvents.send(SplitEventType.PODS_READY_START);
    }
  }, [isReady]);

  const getUrl = (companyCode: string) => {
    if (companyCode === CORPORATE_COMPANY_CODE || firstAgreement.isInterFranchise) {
      return CORPORATE_RENTAL_AGREEMENT;
    }
    mappedCompanyCode = fillableRentalAgreementCompanyCodeMap.get(companyCode);

    if (!mappedCompanyCode) {
      datadogLogs.logger.log('No rental agreement found for company code', {
        companyCode: mappedCompanyCode
      });
      return CORPORATE_RENTAL_AGREEMENT;
    }
    return LOCAL_RENTAL_AGREEMENT_PATH(mappedCompanyCode);
  };

  const getUrlListFromAgreements = () =>
    outstandingRentalAgreements?.map((agreement) => getUrl(agreement.billingCompanyCode));

  const handleAcceptAll = async () => {
    const requests = outstandingRentalAgreements.map((agreement) => ({
      identity: DEFAULT_RENTAL_AGREEMENT_IDENTITY,
      orderId: agreement.orderId,
      companyCode: mappedCompanyCode ?? CORPORATE_COMPANY_CODE,
      firstName: customer.firstName,
      lastName: customer.lastName,
      commercialCorpName: customer.commercialCorpName,
      docId: agreement.id,
      docTitle: agreement.title,
      ...getTimezoneData()
    }));

    acceptMultipleRentalAgreements.mutate(requests, {
      onSuccess: () => {
        eventMonitor.submitSuccess();
        markAllRentalAgreementsAsCompleted(requests.map((r) => r.docId));
        if (!hasOutstandingMothForms) {
          splitEvents.send(SplitEventType.PODS_READY_COMPLETE);
        }
        navigate(location.state?.onSuccessRoute ?? ROUTES.HOME);
      },
      onError: () => {
        eventMonitor.submitFailure();
        setNotification({
          message: translate(TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE),
          isError: true
        });
      }
    });
  };

  const handleAccept = () => {
    const { firstName, lastName, commercialCorpName } = customer;
    const { orderId, id: agreementId, title } = firstAgreement;
    const request: AcceptRentalAgreementRequest = {
      identity: DEFAULT_RENTAL_AGREEMENT_IDENTITY,
      orderId,
      companyCode: mappedCompanyCode ?? CORPORATE_COMPANY_CODE,
      firstName,
      lastName,
      commercialCorpName,
      docId: agreementId,
      docTitle: title,
      ...getTimezoneData()
    };

    if (!hasOutstandingMothForms && remainingAgreements.length === 0) {
      // If this is the last RA to sign, and there are no moth forms, this user is 'pods ready' after signing this agreement
      splitEvents.send(SplitEventType.PODS_READY_COMPLETE);
    }
    acceptRentalAgreement.mutate(request, {
      onSuccess: () => {
        eventMonitor.submitSuccess();
        setNumberOfCurrentAgreement(numberOfCurrentAgreement + 1);
        markRentalAgreementAsCompleted(agreementId);
        if (isEmpty(remainingAgreements) && !children) {
          navigate(location.state?.onSuccessRoute ?? ROUTES.HOME, { replace: true });
        }
      },
      onError: () => {
        eventMonitor.submitFailure();
        setNotification({
          message: translate(TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE),
          isError: true
        });
      }
    });
  };
  useEffect(() => {
    if (navigateToPodsReadyTasks) {
      navigate(PodsReadyRoutes.TASKS, { replace: true });
    }
  }, [navigateToPodsReadyTasks]);

  if (isFetching || navigateToPodsReadyTasks) {
    return <LoadingScreen loadingText="" />;
  }

  if (hasOutstandingRentalAgreements && status === 'success') {
    eventMonitor.startViewingAgreement();
    const pdfUrl = getUrl(firstAgreement.billingCompanyCode);
    if (isLinkFlowRentalAgreementEnabled()) {
      return (
        <LinkFlowRentalAgreementViewer
          onAccept={handleAcceptAll}
          rentalAgreements={outstandingRentalAgreements}
          pdfUrls={getUrlListFromAgreements()}
          isProcessing={acceptMultipleRentalAgreements.isPending}
          podsReadyTaskPage={!children}
          customer={customer}
        />
      );
    }
    return (
      <PdfRentalAgreementViewer
        onAccept={handleAccept}
        pdfUrl={pdfUrl}
        outstandingRentalAgreement={firstAgreement}
        acceptIsLoading={acceptRentalAgreement.isPending}
        isInterFranchise={firstAgreement.isInterFranchise}
        podsReadyTaskPage={!children}
        useGtmEvents={() => gtmEvents}
      />
    );
  }
  if (children) {
    return children;
  }
};
