import { Box, Typography, useMediaQuery } from '@mui/material';
import React, { useEffect } from 'react';
import { Design } from '../../helpers/Design';
import { theme } from '../../PodsTheme';
import { useGetSplashScreenTruckHelper } from '../../helpers/useGetSplashScreenTruckHelper';
import { ENV_VARS } from '../../environment';

const LoadingScreen = ({ loadingText }: { loadingText: string }) => {
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const style = styles(isMobile);
  const truckImageUrl = useGetSplashScreenTruckHelper();

  useEffect(() => {
    const image = new Image();
    image.src = truckImageUrl;
    ['8', '12', '16'].forEach((containerSize) => {
      new Image().src = `${ENV_VARS.ASSETS_BASE_URL}/images/containers/${containerSize}ftContainer.png`;
    });
  }, []);
  return (
    <Box {...style.container}>
      <img
        alt="Loading animated gif"
        src="https://st-rbf-storage-account-aac5h9bwg8aycwen.z01.azurefd.net/rbf/quote-loading-truck.gif"
        width="196"
        height="98"
      />
      <Typography {...style.loadingText}>{loadingText}</Typography>
      <Typography {...style.subText}>This may take up to 30 seconds.</Typography>
    </Box>
  );
};

export default LoadingScreen;

const styles = (isMobile: boolean) => ({
  container: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100vh'
    }
  },
  loadingText: {
    ...(isMobile
      ? { ...Design.Alias.Text.Heading.Mobile.Lg }
      : { ...Design.Alias.Text.Heading.Desktop.Lg }),
    sx: {
      color: Design.Alias.Color.accent900,
      marginTop: '24px'
    }
  },
  subText: {
    ...Design.Alias.Text.BodyUniversal.Xs,
    sx: {
      color: Design.Alias.Color.neutral700,
      marginTop: '16px'
    }
  }
});
