import { LegacyRentalAgreementOnboarding } from '../LegacyRentalAgreementOnboarding';
import { render, screen, waitFor } from '@testing-library/react';

import React, { Suspense } from 'react';
import { InitialEntry } from 'history';
import { MemoryRouter } from 'react-router';
import { ThemeProvider } from '@mui/material';
import { theme } from '../../../../PodsTheme';
import { QueryClientProvider } from '@tanstack/react-query';
import { ErrorBoundary } from '../../../ErrorBoundary';
import {
  mockedUseFeatureFlags,
  mockGetCustomer,
  mockLegacyAcceptRentalAgreement,
  mockLegacyAuthorizationEntrypoint,
  mockLegacyGetCustomer,
  mockLegacyGetRentalAgreement,
  mockLegacyRefreshSession,
  mockSendSplitEvent
} from '../../../../../setupTests';
import {
  createCustomer,
  createEntryPointResult,
  createOutstandingMothAgreement,
  createOutstandingRentalAgreement,
  createRefreshSessionClaims,
  createUseFeatureFlagResult,
  mockIsESignRentalAgreementEnabled
} from '../../../../testUtils/MyPodsFactories';
import { TestOutstandingDocumentViewerViews } from '../../../../testUtils/testComponents/TestRentalAgreementViewer';
import userEvent, { UserEvent } from '@testing-library/user-event';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import { NotificationProvider } from '../../../notifications/NotificationContext';
import { ENV_VARS } from '../../../../environment';
import { EntryPointProvider } from '../../../../context/EntryPointContext';
import { vi } from 'vitest';
import { getTimezoneData } from '../../../../helpers/dateHelpers';
import { Customer } from '../../../../networkRequests/responseEntities/CustomerEntities';
import { testQueryClient } from '../../../../testUtils/RenderHelpers';
import { SplitEventType } from '../../../../config/SplitEventTypes';

describe('LegacyRentalAgreementOnboarding', () => {
  let user: UserEvent;
  let initialCustomer: Customer;
  const pdfUrl = 'https://cool.pdf.com';
  const sessionClaims = createRefreshSessionClaims();
  const outstandingRentalAgreement = createOutstandingRentalAgreement();
  const entryPointNoneOutstanding = createEntryPointResult({
    outstandingRentalAgreements: []
  });
  const maintenanceMode = createEntryPointResult({
    maintenanceModeEnabled: true,
    outstandingRentalAgreements: []
  });

  const mockLogger = vi.hoisted(() => vi.fn());
  vi.mock('@datadog/browser-logs', () => {
    return {
      datadogLogs: {
        logger: {
          log: mockLogger
        }
      }
    };
  });

  beforeEach(() => {
    mockGetCustomer.mockResolvedValue(createCustomer());
    mockedUseFeatureFlags.useFeatureFlags.mockImplementation(() =>
      createUseFeatureFlagResult({
        isPodsReadySingleOrderEnabled: () => false,
        isPoetEnabled: () => false
      })
    );
    mockLegacyGetRentalAgreement.mockResolvedValue(pdfUrl);
    user = userEvent.setup();
    initialCustomer = createCustomer();
    mockLegacyGetCustomer.mockResolvedValue(initialCustomer);
    mockLegacyRefreshSession.mockResolvedValue(sessionClaims);
    mockLegacyAcceptRentalAgreement.mockReturnValue(Promise.resolve());
  });

  function renderWithProviders(
    subject: React.JSX.Element,
    initialEntries: InitialEntry[] | string[] | undefined = undefined
  ) {
    return render(
      <MemoryRouter initialEntries={initialEntries}>
        <ThemeProvider theme={theme}>
          <QueryClientProvider client={testQueryClient()}>
            <ErrorBoundary renderError={(error) => <p>{error.message}</p>}>
              <Suspense fallback={<div>Loading authorization entrypoint...</div>}>
                <EntryPointProvider>
                  <NotificationProvider>{subject}</NotificationProvider>
                </EntryPointProvider>
              </Suspense>
            </ErrorBoundary>
          </QueryClientProvider>
        </ThemeProvider>
      </MemoryRouter>
    );
  }

  function renderSubject() {
    return renderWithProviders(
      <LegacyRentalAgreementOnboarding>
        <div>Children</div>
      </LegacyRentalAgreementOnboarding>
    );
  }

  const expectChildrenToBeRendered = () => screen.getByText('Children');

  const expectChildrenToNotBeRendered = () =>
    expect(screen.queryByText('Children')).not.toBeInTheDocument();

  it('should redirect to auth if in maintenance mode', async () => {
    mockLegacyAuthorizationEntrypoint.mockResolvedValue(maintenanceMode);
    renderSubject();

    await waitFor(() => {
      expect(window.location.replace).toHaveBeenLastCalledWith(ENV_VARS.LOGOUT);
    });
  });

  it('should render children given no outstanding rental agreements', async () => {
    mockLegacyAuthorizationEntrypoint.mockResolvedValue(entryPointNoneOutstanding);
    renderSubject();

    await waitFor(() => expect(mockLegacyAuthorizationEntrypoint).toHaveBeenCalledTimes(1));
    await waitFor(async () => {
      expectChildrenToBeRendered();
    });
  });

  it('should render document viewer given outstanding rental agreements', async () => {
    const expectedEntryPointResult = createEntryPointResult({
      outstandingRentalAgreements: [outstandingRentalAgreement]
    });
    mockLegacyAuthorizationEntrypoint.mockResolvedValue(expectedEntryPointResult);
    renderSubject();

    expectChildrenToNotBeRendered();
    expect(await TestOutstandingDocumentViewerViews.button()).toBeInTheDocument();
    expect(await TestOutstandingDocumentViewerViews.pdfLink()).toHaveAttribute('href', pdfUrl);
    expect(mockSendSplitEvent).toHaveBeenCalledWith(SplitEventType.PODS_READY_START);
  });

  it('should render the application as a child after accepting rental agreement', async () => {
    const expectedEntryPointResult = createEntryPointResult({
      outstandingRentalAgreements: [outstandingRentalAgreement]
    });
    const { firstName, lastName } = initialCustomer;
    const expectedRequest = {
      orderId: outstandingRentalAgreement.orderId,
      identity: outstandingRentalAgreement.identity,
      companyCode: 'PEIU',
      firstName: firstName,
      lastName: lastName,
      ...getTimezoneData()
    };
    mockLegacyAuthorizationEntrypoint.mockResolvedValue(expectedEntryPointResult);
    renderSubject();

    await waitFor(async () => user.click(await TestOutstandingDocumentViewerViews.button()));

    expect(mockLegacyAcceptRentalAgreement).toHaveBeenCalledWith(expectedRequest);
    expectChildrenToBeRendered();
  });

  it('should render a notification in a snackbar if accepting the rental agreement fails', async () => {
    const expectedEntryPointResult = createEntryPointResult({
      outstandingRentalAgreements: [outstandingRentalAgreement]
    });

    mockLegacyAuthorizationEntrypoint.mockResolvedValue(expectedEntryPointResult);
    mockLegacyAcceptRentalAgreement.mockImplementation((_request, { onError }) => {
      onError!(new Error('Failure'));
    });
    renderSubject();

    await waitFor(async () => user.click(await TestOutstandingDocumentViewerViews.button()));

    expectChildrenToNotBeRendered();
    const genericFailure = TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE;
    expect(screen.getByText(genericFailure)).toBeInTheDocument();
  });

  it('should render children after accepting multiple rental agreements', async () => {
    const outstandingRentalAgreement1 = outstandingRentalAgreement;
    const outstandingRentalAgreement2 = { ...outstandingRentalAgreement1, orderId: '999888777' };
    const expectedEntryPointResult = createEntryPointResult({
      outstandingRentalAgreements: [outstandingRentalAgreement1, outstandingRentalAgreement2]
    });
    mockLegacyAuthorizationEntrypoint.mockResolvedValue(expectedEntryPointResult);
    renderSubject();

    await waitFor(async () => user.click(await TestOutstandingDocumentViewerViews.button()));

    expect(mockLegacyAcceptRentalAgreement).toHaveBeenCalledTimes(1);

    await waitFor(async () => user.click(await TestOutstandingDocumentViewerViews.button()));
    expect(mockLegacyAcceptRentalAgreement).toHaveBeenCalledTimes(2);
    expectChildrenToBeRendered();
    const numPodsReadyStartCalls = mockSendSplitEvent.mock.calls.filter(
      (call) => call[0] == SplitEventType.PODS_READY_START
    ).length;
    const numPodsReadyCompleteCalls = mockSendSplitEvent.mock.calls.filter(
      (call) => call[0] == SplitEventType.PODS_READY_COMPLETE
    ).length;
    expect(numPodsReadyStartCalls).toEqual(1);
    expect(numPodsReadyCompleteCalls).toEqual(1);
  });

  it('should not send PODS_READY_COMPLETE if there is an outstanding moth form render children after accepting multiple rental agreements', async () => {
    const outstandingRentalAgreement1 = outstandingRentalAgreement;
    const outstandingRentalAgreement2 = { ...outstandingRentalAgreement1, orderId: '999888777' };
    const expectedEntryPointResult = createEntryPointResult({
      outstandingRentalAgreements: [outstandingRentalAgreement1, outstandingRentalAgreement2],
      outstandingMothAgreements: [createOutstandingMothAgreement()]
    });
    mockLegacyAuthorizationEntrypoint.mockResolvedValue(expectedEntryPointResult);
    renderSubject();

    await waitFor(async () => user.click(await TestOutstandingDocumentViewerViews.button()));

    expect(mockLegacyAcceptRentalAgreement).toHaveBeenCalledTimes(1);

    await waitFor(async () => user.click(await TestOutstandingDocumentViewerViews.button()));
    expect(mockLegacyAcceptRentalAgreement).toHaveBeenCalledTimes(2);
    expectChildrenToBeRendered();
    const numPodsReadyStartCalls = mockSendSplitEvent.mock.calls.filter(
      (call) => call[0] == SplitEventType.PODS_READY_START
    ).length;
    const numPodsReadyCompleteCalls = mockSendSplitEvent.mock.calls.filter(
      (call) => call[0] == SplitEventType.PODS_READY_COMPLETE
    ).length;
    expect(numPodsReadyStartCalls).toEqual(1);
    expect(numPodsReadyCompleteCalls).toEqual(0);
  });

  it('should render e-sign form when isESignRentalAgreementEnabled returns true', async () => {
    mockIsESignRentalAgreementEnabled.mockResolvedValue(true);
    mockLegacyAuthorizationEntrypoint.mockResolvedValue(
      createEntryPointResult({
        outstandingRentalAgreements: [outstandingRentalAgreement]
      })
    );

    renderSubject();

    expect(
      await screen.findByText(TranslationKeys.Onboarding.SignRentalAgreements.HEADER)
    ).toBeInTheDocument();
  });
});
