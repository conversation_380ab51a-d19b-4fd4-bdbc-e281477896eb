import React, { ReactNode, useContext, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate } from 'react-router';
import isEmpty from 'lodash/isEmpty';
import { NotificationContext } from '../../notifications/NotificationContext';
import { useLegacyAcceptRentalAgreement } from '../../../networkRequests/legacy/mutations/useLegacyAcceptRentalAgreement';
import { useEntryPointContext } from '../../../context/EntryPointContext';
import { AcceptRentalAgreementRequest } from '../../../networkRequests/responseEntities/DocumentApiEntities';
import { gtmAgreementTypeFromRentalAgreement } from '../../../config/google/GoogleEntities';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { getTimezoneData } from '../../../helpers/dateHelpers';
import { PdfRentalAgreementViewer } from '../../RentalAgreementOnboarding/RentalAgreementDocumentViewer/PdfRentalAgreementViewer';
import {
  LINK_FLOW_RENTAL_AGREEMENT_ENABLED,
  PODS_READY_SINGLE_ORDER_ENABLED,
  useFeatureFlags
} from '../../../helpers/useFeatureFlags';
import { LinkFlowRentalAgreementViewer } from '../../RentalAgreementOnboarding/RentalAgreementDocumentViewer/LinkFlowRentalAgreementViewer';
import { RentalAgreementEvents } from '../../RentalAgreementOnboarding/RentalAgreementEvents';
import { useLegacyGetCustomer } from '../../../networkRequests/legacy/queries/useLegacyGetCustomer';
import { ROUTES } from '../../../Routes';
import { useGtmEventsWithCustomer } from '../../../config/google/useGtmEvents';
import { PodsReadyRoutes } from '../../../PodsReadyRoutes';
import { useLegacyShowPodsReadySingleOrder } from '../../../helpers/legacy/useLegacyShowPodsReadySingleOrder';
import LoadingScreen from '../../Loading/LoadingScreen';
import { isPodsReadyLocation } from '../../../helpers/podsReadyHelpers';
import { SplitEventType } from '../../../config/SplitEventTypes';
import { useLegacyAcceptRentalAgreements } from '../../../networkRequests/legacy/mutations/useLegacyAcceptRentalAgreements';
import { useLegacySplitEvents } from '../../../config/useLegacySplitEvents';

type RentalAgreementOnboardingProps = { children?: ReactNode };
const CORPORATE_COMPANY_CODE = 'PEIU';

export const LegacyRentalAgreementOnboarding = ({ children }: RentalAgreementOnboardingProps) => {
  const { setNotification } = useContext(NotificationContext);
  const { t: translate } = useTranslation();
  const { customer } = useLegacyGetCustomer();
  const gtmEvents = useGtmEventsWithCustomer(customer);
  const splitEvents = useLegacySplitEvents();
  const { isReady, isPodsReadySingleOrderEnabled } = useFeatureFlags([
    PODS_READY_SINGLE_ORDER_ENABLED
  ]);
  const navigate = useNavigate();
  const location = useLocation();
  const acceptRentalAgreement = useLegacyAcceptRentalAgreement();
  const acceptMultipleRentalAgreements = useLegacyAcceptRentalAgreements();
  const { entryPointResult, pdfUrls, setEntryPointResult } = useEntryPointContext();
  const { isLinkFlowRentalAgreementEnabled } = useFeatureFlags([
    LINK_FLOW_RENTAL_AGREEMENT_ENABLED
  ]);
  const [firstAgreement, ...remainingAgreements] = entryPointResult.outstandingRentalAgreements;
  const [numberOfCurrentAgreement, setNumberOfCurrentAgreement] = useState<number>(1);
  const { showPodsReadySingleOrder, isFetching } = useLegacyShowPodsReadySingleOrder();
  const hasOutstandingRentalAgreements = entryPointResult.outstandingRentalAgreements.length > 0;
  const hasOutstandingMothForms = entryPointResult.outstandingMothAgreements.length > 0;
  let mappedCompanyCode: string | undefined;
  const eventMonitor = RentalAgreementEvents({
    isInterFranchise: firstAgreement?.agreementType === 'IF',
    orderId: firstAgreement?.orderId,
    customer
  });
  const navigateToPodsReadyTasks = showPodsReadySingleOrder && !isPodsReadyLocation(location);

  useEffect(() => {
    if (
      isReady &&
      !isPodsReadySingleOrderEnabled() &&
      (hasOutstandingRentalAgreements || hasOutstandingMothForms)
    ) {
      // if you have pods-ready enabled, the PODS_READY_START event is emitted when you land on the pods ready task page
      // If you don't have pods ready enabled, we're calling the start of the flow when you land on the home page
      splitEvents.send(SplitEventType.PODS_READY_START);
    }
  }, [isReady]);

  const getUrl = (companyCode: string) =>
    pdfUrls?.find((it) => it.companyCode === companyCode)!.url;
  const pdfUrl = hasOutstandingRentalAgreements ? getUrl(firstAgreement.companyCode) : '';

  const getUrlsForAgreements = () =>
    entryPointResult.outstandingRentalAgreements.map(
      (agreement) => pdfUrls?.find((pdf) => pdf.companyCode === agreement.companyCode)?.url ?? ''
    );

  const handleAcceptAll = async () => {
    const requests = entryPointResult.outstandingRentalAgreements.map((agreement) => ({
      identity: agreement.identity,
      orderId: agreement.orderId,
      companyCode: mappedCompanyCode ?? CORPORATE_COMPANY_CODE,
      firstName: customer.firstName,
      lastName: customer.lastName,
      docId: agreement.id,
      ...getTimezoneData()
    }));

    acceptMultipleRentalAgreements.mutate(requests, {
      onSuccess: () => {
        eventMonitor.submitSuccess();

        setEntryPointResult({
          ...entryPointResult,
          outstandingRentalAgreements: []
        });

        if (!hasOutstandingMothForms) {
          splitEvents.send(SplitEventType.PODS_READY_COMPLETE);
        }
        navigate(location.state?.onSuccessRoute ?? ROUTES.HOME);
      },
      onError: () => {
        eventMonitor.submitFailure();
        setNotification({
          message: translate(TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE),
          isError: true
        });
      }
    });
  };

  const handleAccept = () => {
    const { firstName, lastName } = customer;
    const { identity, orderId, id } = firstAgreement;
    const request: AcceptRentalAgreementRequest = {
      identity,
      orderId,
      companyCode: mappedCompanyCode ?? CORPORATE_COMPANY_CODE,
      firstName,
      lastName,
      docId: id,
      ...getTimezoneData()
    };

    const agreementType = gtmAgreementTypeFromRentalAgreement(firstAgreement.agreementType);
    gtmEvents.submitAgreement(agreementType, firstAgreement.orderId);

    if (!hasOutstandingMothForms && remainingAgreements.length === 0) {
      // If this is the last RA to sign, and there are no moth forms, this user is 'pods ready' after signing this agreement
      splitEvents.send(SplitEventType.PODS_READY_COMPLETE);
    }
    eventMonitor.submitAgreement();
    acceptRentalAgreement.mutate(request, {
      onSuccess: () => {
        eventMonitor.submitSuccess();
        setNumberOfCurrentAgreement(numberOfCurrentAgreement + 1);
        setEntryPointResult({
          ...entryPointResult,
          outstandingRentalAgreements: remainingAgreements
        });
        if (isEmpty(remainingAgreements) && !children) {
          navigate(location.state?.onSuccessRoute ?? ROUTES.HOME, { replace: true });
        }
      },
      onError: () => {
        eventMonitor.submitFailure();
        setNotification({
          message: translate(TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE),
          isError: true
        });
      }
    });
  };
  useEffect(() => {
    if (navigateToPodsReadyTasks) {
      navigate(PodsReadyRoutes.TASKS, { replace: true });
    }
  }, [navigateToPodsReadyTasks]);

  if (isFetching || navigateToPodsReadyTasks) {
    return <LoadingScreen loadingText="" />;
  }

  if (hasOutstandingRentalAgreements) {
    eventMonitor.startViewingAgreement();
    if (isLinkFlowRentalAgreementEnabled()) {
      return (
        <LinkFlowRentalAgreementViewer
          onAccept={handleAcceptAll}
          rentalAgreements={entryPointResult.outstandingRentalAgreements}
          pdfUrls={getUrlsForAgreements()}
          isProcessing={acceptMultipleRentalAgreements.isPending}
          podsReadyTaskPage={!children}
          customer={customer}
        />
      );
    }
    return (
      <PdfRentalAgreementViewer
        onAccept={handleAccept}
        pdfUrl={pdfUrl}
        outstandingRentalAgreement={firstAgreement}
        acceptIsLoading={acceptRentalAgreement.isPending}
        isInterFranchise={firstAgreement.agreementType === 'IF'}
        podsReadyTaskPage={!children}
        useGtmEvents={() => gtmEvents}
      />
    );
  }

  if (children) {
    return children;
  }
};
