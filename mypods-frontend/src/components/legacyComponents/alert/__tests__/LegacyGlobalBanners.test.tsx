import { screen, waitFor, within } from '@testing-library/react';
import { LegacyGlobalBanners } from '../LegacyGlobalBanners';
import {
  mockLegacyGetCustomer,
  mockLegacySignFnpsWaiver,
  mockNavigate,
  mockRefreshSession,
  mockSendSplitEvent
} from '../../../../../setupTests';
import {
  createCustomer,
  createEntryPointResult,
  createOutstandingFnpsAgreement,
  createOutstandingMothAgreement,
  createRefreshSessionClaims,
  createSignFnpsRequest
} from '../../../../testUtils/MyPodsFactories';
import { renderWithLegacyProvidersAndState } from '../../../../testUtils/RenderHelpers';
import React from 'react';
import { DISMISS_ICON_ID } from '../../../icons/DismissXIcon';
import userEvent from '@testing-library/user-event';
import { ROUTES } from '../../../../Routes';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import { initialEntryPointState } from '../../../../context/EntryPointContext';
import {
  EntryPointResult,
  RefreshSessionClaims
} from '../../../../networkRequests/responseEntities/AuthorizationEntities';
import { BannerContext } from '../../../../locales/TranslationConstants';
import { addSignedMothOrderId } from '../../../../helpers/entrypointHelpers';
import { Customer } from '../../../../networkRequests/responseEntities/CustomerEntities';
import {
  getWeightTicketBanner,
  setOrdersWithSignedMothAgreements,
  setWeightTicketBanner
} from '../../../../helpers/storageHelpers';
import { SplitEventType } from '../../../../config/SplitEventTypes';

describe('Legacy Global Banners', () => {
  const customer = createCustomer();
  const orderId = '123';
  const Tx = TranslationKeys.CommonComponents.Banner;
  const sessionClaims: RefreshSessionClaims = createRefreshSessionClaims();

  const renderBanners = (
    entryPointResult: EntryPointResult,
    initialCustomer: Customer = customer
  ) => {
    mockLegacyGetCustomer.mockResolvedValue(initialCustomer);
    mockRefreshSession.mockResolvedValue(sessionClaims);

    return renderWithLegacyProvidersAndState(<LegacyGlobalBanners />, {
      entryPointState: {
        ...initialEntryPointState,
        entryPointResult: entryPointResult
      },
      initialCustomer: initialCustomer,
      initialEntries: ['/']
    });
  };

  afterEach(() => {
    setOrdersWithSignedMothAgreements(null);
    setWeightTicketBanner(null);
  });

  describe('Weight Tickets', () => {
    const withWeightTicket = createEntryPointResult({
      hasWeightTickets: true
    });

    const renderGlobalBannerWithWeightTickets = () => {
      return renderBanners(withWeightTicket);
    };

    it('should display banner for weight tickets', async () => {
      renderGlobalBannerWithWeightTickets();

      expect(
        await screen.findByText(`${Tx.TITLE}[${BannerContext.REVIEW_DOCUMENTS}]`)
      ).toBeInTheDocument();
      expect(
        await screen.findByText(`${Tx.MESSAGE}[${BannerContext.REVIEW_DOCUMENTS}]`)
      ).toBeInTheDocument();
      expect(
        await screen.findByText(`${Tx.CTA_LABEL}[${BannerContext.REVIEW_DOCUMENTS}]`)
      ).toBeInTheDocument();
    });

    it('should navigate to document page on button click', async () => {
      renderGlobalBannerWithWeightTickets();

      await waitFor(async () => {
        await userEvent.click(
          screen.getByRole('button', { name: `${Tx.CTA_LABEL}[reviewDocuments]` })
        );
      });
      expect(mockNavigate).toHaveBeenCalledWith(ROUTES.DOCUMENT);
    });

    it('should not render weight ticket banner if a weight ticket does not exist for a customer', () => {
      const noWeightTicket = createEntryPointResult({
        hasWeightTickets: false
      });
      renderBanners(noWeightTicket);

      expect(
        screen.queryByText(`${Tx.TITLE}[${BannerContext.REVIEW_DOCUMENTS}]`)
      ).not.toBeInTheDocument();
    });

    it('should dismiss banner on X click', async () => {
      renderGlobalBannerWithWeightTickets();

      await waitFor(async () => {
        await userEvent.click(screen.getByTestId(DISMISS_ICON_ID));
      });
      expect(
        screen.queryByText(`${Tx.TITLE}[${BannerContext.REVIEW_DOCUMENTS}]`)
      ).not.toBeInTheDocument();
      expect(getWeightTicketBanner()).toBeTruthy();
    });

    it('should not render weight ticket banner if a weight ticket does exist but has been dismissed', () => {
      setWeightTicketBanner(true);

      renderGlobalBannerWithWeightTickets();

      expect(
        screen.queryByText(`${Tx.TITLE}[${BannerContext.REVIEW_DOCUMENTS}]`)
      ).not.toBeInTheDocument();
    });
  });

  describe('Moth Agreements', () => {
    const mothAgreement = createEntryPointResult({
      outstandingMothAgreements: [createOutstandingMothAgreement({ orderId })]
    });

    it('should display banner for moth agreements', async () => {
      renderBanners(mothAgreement);

      expect(
        await screen.findByText(`${Tx.TITLE}[${BannerContext.MOTH_AGREEMENTS}]`)
      ).toBeInTheDocument();
      expect(
        await screen.findByText(`${Tx.MESSAGE}[${BannerContext.MOTH_AGREEMENTS}]`)
      ).toBeInTheDocument();
      expect(
        await screen.findByText(`${Tx.CTA_LABEL}[${BannerContext.MOTH_AGREEMENTS}]`)
      ).toBeInTheDocument();
    });

    it('should navigate to the first page of the moth form on button click', async () => {
      renderBanners(mothAgreement);

      await waitFor(async () => {
        await userEvent.click(
          screen.getByRole('button', { name: `${Tx.CTA_LABEL}[${BannerContext.MOTH_AGREEMENTS}]` })
        );
      });
      expect(mockNavigate).toHaveBeenCalledWith(ROUTES.MOTH_FLY_INSPECTION);
    });

    it('should send a split event on button click', async () => {
      renderBanners(mothAgreement);

      await waitFor(async () => {
        await userEvent.click(
          screen.getByRole('button', { name: `${Tx.CTA_LABEL}[${BannerContext.MOTH_AGREEMENTS}]` })
        );
      });
      expect(mockSendSplitEvent).toHaveBeenCalledWith(SplitEventType.MOTH_FORM_START);
    });

    it('should not display a banner if user has already signed it', () => {
      addSignedMothOrderId(orderId);
      renderBanners(mothAgreement);

      expect(
        screen.queryByText(`${Tx.TITLE}[${BannerContext.MOTH_AGREEMENTS}]`)
      ).not.toBeInTheDocument();
    });
  });

  describe('Fragile Non-Paved Surface Agreement', () => {
    const moveId = '123';
    const outstandingFnpsAgreement1 = createOutstandingFnpsAgreement({ orderId, moveId });
    const outstandingFnpsAgreement2 = createOutstandingFnpsAgreement({
      orderId: '456',
      moveId: 'm456'
    });
    const surfaceAgreement = createEntryPointResult({
      outstandingFnpsAgreements: [outstandingFnpsAgreement1]
    });
    const multipleFnpsAgreements = createEntryPointResult({
      outstandingFnpsAgreements: [outstandingFnpsAgreement1, outstandingFnpsAgreement2]
    });

    it('should display a banner for surface agreements', async () => {
      renderBanners(surfaceAgreement);
      expect(
        await screen.findByText(`${Tx.TITLE}[${BannerContext.FNPS_AGREEMENTS}]`)
      ).toBeInTheDocument();
      expect(
        await screen.findByText(`${Tx.MESSAGE}[${BannerContext.FNPS_AGREEMENTS}]`)
      ).toBeInTheDocument();
      expect(
        await screen.findByText(`${Tx.CTA_LABEL}[${BannerContext.FNPS_AGREEMENTS}]`)
      ).toBeInTheDocument();
    });

    it('should display the pagination for FNPS waiver', async () => {
      renderBanners(multipleFnpsAgreements);

      await waitFor(async () => {
        await userEvent.click(
          screen.getByText(
            `${TranslationKeys.CommonComponents.Banner.CTA_LABEL}[${BannerContext.FNPS_AGREEMENTS}]`
          )
        );
      });

      await waitFor(() => {
        expect(screen.getByRole('dialog')).toBeInTheDocument();
      });

      const paginationContainer = await screen.findByTestId('pods-modal-pagination');

      expect(
        within(paginationContainer).getByText(
          TranslationKeys.CommonComponents.PROGRESS_COUNTER +
            `[${1},${multipleFnpsAgreements.outstandingFnpsAgreements.length}]`
        )
      ).toBeInTheDocument();

      expect(screen.getByText(TranslationKeys.CommonComponents.NEXT_BUTTON)).toBeInTheDocument();
    });

    it('should successfully submit fnps waiver on button click', async () => {
      renderBanners(surfaceAgreement);

      await waitFor(async () => {
        await userEvent.click(
          screen.getByText(
            `${TranslationKeys.CommonComponents.Banner.CTA_LABEL}[${BannerContext.FNPS_AGREEMENTS}]`
          )
        );
        await userEvent.click(
          screen.getByRole('button', {
            name: TranslationKeys.CommonComponents.AddSignature.BUTTON_TEXT
          })
        );
        await userEvent.click(
          screen.getByRole('button', { name: TranslationKeys.FnpsModal.ACTION_BUTTON_LABEL })
        );
      });

      const expectedRequest = createSignFnpsRequest({
        orderId: outstandingFnpsAgreement1.orderId,
        moveId: moveId,
        firstName: customer.firstName,
        lastName: customer.lastName
      });

      expect(mockLegacySignFnpsWaiver).toHaveBeenCalledWith(expectedRequest);
      expect(
        screen.getByText(TranslationKeys.CommonComponents.Notification.SIGN_FNPS_WAIVER_SUCCEEDED)
      ).toBeInTheDocument();
      // TODO: Figure out a way to test that the banner has been removed
    });

    it('should display error alert if signing fnps waiver fails', async () => {
      mockLegacySignFnpsWaiver.mockRejectedValue({});
      renderBanners(surfaceAgreement);

      await waitFor(async () => {
        await userEvent.click(
          screen.getByText(
            `${TranslationKeys.CommonComponents.Banner.CTA_LABEL}[${BannerContext.FNPS_AGREEMENTS}]`
          )
        );
        await userEvent.click(
          screen.getByRole('button', {
            name: TranslationKeys.CommonComponents.AddSignature.BUTTON_TEXT
          })
        );
        await userEvent.click(
          screen.getByRole('button', { name: TranslationKeys.FnpsModal.ACTION_BUTTON_LABEL })
        );
      });
      expect(screen.getByText(TranslationKeys.FnpsModal.ALERT_TITLE)).toBeInTheDocument();
      expect(screen.getByText(TranslationKeys.FnpsModal.ALERT_DESCRIPTION)).toBeInTheDocument();
    });
  });

  describe('Desynced Emails Banner', () => {
    const entrypoint = createEntryPointResult();

    it('should not display a banner, when a customer is unconverted', () => {
      const unconvertedCustomer = createCustomer({
        isConverted: false,
        email: { id: 1, address: '<EMAIL>' },
        username: '<EMAIL>'
      });

      renderBanners(entrypoint, unconvertedCustomer);

      expect(
        screen.queryByText(
          `${TranslationKeys.CommonComponents.Banner.TITLE}[${BannerContext.DESYNCED_EMAILS}]`
        )
      ).toBeNull();
    });

    it('should not display a banner, when a customer is converted & has matching emails', () => {
      const convertedCustomer = createCustomer({
        isConverted: true,
        email: { id: 1, address: '<EMAIL>' },
        username: '<EMAIL>'
      });

      renderBanners(entrypoint, convertedCustomer);

      expect(
        screen.queryByText(
          `${TranslationKeys.CommonComponents.Banner.TITLE}[${BannerContext.DESYNCED_EMAILS}]`
        )
      ).toBeNull();
    });

    it("should display a banner, when a customer is converted, but their email & username don't match", async () => {
      const desyncedCustomer = createCustomer({
        isConverted: true,
        email: { id: 1, address: '<EMAIL>' },
        username: '<EMAIL>'
      });

      renderBanners(entrypoint, desyncedCustomer);

      expect(
        await screen.findByText(
          `${TranslationKeys.CommonComponents.Banner.TITLE}[${BannerContext.DESYNCED_EMAILS}]`
        )
      ).toBeInTheDocument();
    });

    it('should navigate to account page on button click', async () => {
      const desyncedCustomer = createCustomer({
        isConverted: true,
        email: { id: 1, address: '<EMAIL>' },
        username: '<EMAIL>'
      });

      renderBanners(entrypoint, desyncedCustomer);

      await waitFor(async () => {
        await userEvent.click(
          screen.getByRole('button', { name: `${Tx.CTA_LABEL}[desyncedEmails]` })
        );
      });
      expect(mockNavigate).toHaveBeenCalledWith(ROUTES.ACCOUNT);
    });
  });
});
