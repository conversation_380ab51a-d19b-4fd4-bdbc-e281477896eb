import React, { forwardRef, useContext, useState } from 'react';
import { Grid, IconButton, Typography, useMediaQuery } from '@mui/material';
import Button from '@mui/material/Button';
import { useNavigate } from 'react-router';
import { useTranslation } from 'react-i18next';
import { useSplitEvents } from '../../../config/useSplitEvents';
import { SplitEventType } from '../../../config/SplitEventTypes';
import { theme } from '../../../PodsTheme';
import { Design } from '../../../helpers/Design';
import { WarningIcon } from '../../icons/WarningIcon';
import { DismissXIcon } from '../../icons/DismissXIcon';
import { ROUTES } from '../../../Routes';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { useEntryPointContext } from '../../../context/EntryPointContext';
import { BannerContext } from '../../../locales/TranslationConstants';
import { PageLayout } from '../../PageLayout';
import { FNPSModal } from '../../Modals/FNPSModal';
import { NotificationContext } from '../../notifications/NotificationContext';
import { SignFnpsWaiverRequest } from '../../../networkRequests/responseEntities/DocumentApiEntities';
import { unsignedMothAgreements } from '../../../helpers/entrypointHelpers';
import { getWeightTicketBanner, setWeightTicketBanner } from '../../../helpers/storageHelpers';
import { useGtmEvents } from '../../../config/google/useGtmEvents';
import { OutstandingFnpsAgreement } from '../../../networkRequests/responseEntities/AuthorizationEntities';
import { customerEmailOutOfSync } from '../../../networkRequests/responseEntities/CustomerEntities';
import { getTimezoneOffset } from '../../../helpers/dateHelpers';
import { useLegacyGetCustomer } from '../../../networkRequests/legacy/queries/useLegacyGetCustomer';
import { useLegacySignFnpsWaiver } from '../../../networkRequests/legacy/mutations/useLegacySignFnpsWaiver';

export const WEIGHT_TICKET_BANNER = 'dismissedWeightTicketBanner';

// -- types --
// TODO: For handling 'variants'
type BannerVariant = 'warning';

type BannerProps = {
  title: string;
  message?: string;
  ctaLabel?: string;
  onButtonClick?: () => void;
  variant?: BannerVariant;
  dismissible?: boolean;
  onDismiss?: () => void;
};

export const LegacyGlobalBanners = forwardRef<HTMLDivElement>((_props, ref) => {
  const navigate = useNavigate();
  const { setNotification } = useContext(NotificationContext);
  const { customer } = useLegacyGetCustomer();
  const { t: translate } = useTranslation();
  const { entryPointResult, removeOutstandingFnpsAgreement } = useEntryPointContext();
  const splitEvents = useSplitEvents(customer.id);
  const { isPending, mutate: signFnpsWaiver } = useLegacySignFnpsWaiver();
  const Tx = TranslationKeys.CommonComponents.Banner;
  const banners: BannerProps[] = [];
  const [isFnpsModalOpen, setIsFnpsModalOpen] = useState<boolean>(false);
  const [showAlert, setShowAlert] = useState<boolean>(false);
  const gtmEvents = useGtmEvents();

  const handleUnsignedMothAgreementClick = () => {
    splitEvents.send(SplitEventType.MOTH_FORM_START);
    navigate(ROUTES.MOTH_FLY_INSPECTION);
  };

  if (getWeightTicketBanner() !== true && entryPointResult.hasWeightTickets) {
    banners.push({
      title: `${translate(Tx.TITLE, { context: BannerContext.REVIEW_DOCUMENTS })}`,
      message: `${translate(Tx.MESSAGE, { context: BannerContext.REVIEW_DOCUMENTS })}`,
      ctaLabel: `${translate(Tx.CTA_LABEL, { context: BannerContext.REVIEW_DOCUMENTS })}`,
      onButtonClick: () => navigate(ROUTES.DOCUMENT),
      dismissible: true,
      onDismiss: () => setWeightTicketBanner(true)
    });
  }

  if (unsignedMothAgreements(entryPointResult).length > 0) {
    banners.push({
      title: `${translate(Tx.TITLE, { context: BannerContext.MOTH_AGREEMENTS })}`,
      message: `${translate(Tx.MESSAGE, { context: BannerContext.MOTH_AGREEMENTS })}`,
      ctaLabel: `${translate(Tx.CTA_LABEL, { context: BannerContext.MOTH_AGREEMENTS })}`,
      onButtonClick: handleUnsignedMothAgreementClick
    });
  }

  if (entryPointResult.outstandingFnpsAgreements.length > 0) {
    banners.push({
      title: `${translate(Tx.TITLE, { context: BannerContext.FNPS_AGREEMENTS })}`,
      message: `${translate(Tx.MESSAGE, { context: BannerContext.FNPS_AGREEMENTS })}`,
      ctaLabel: `${translate(Tx.CTA_LABEL, { context: BannerContext.FNPS_AGREEMENTS })}`,
      onButtonClick: () => {
        setIsFnpsModalOpen(true);
      }
    });
  }

  if (customerEmailOutOfSync(customer)) {
    banners.push({
      title: `${translate(Tx.TITLE, { context: BannerContext.DESYNCED_EMAILS })}`,
      message: `${translate(Tx.MESSAGE, { context: BannerContext.DESYNCED_EMAILS })}`,
      ctaLabel: `${translate(Tx.CTA_LABEL, { context: BannerContext.DESYNCED_EMAILS })}`,
      onButtonClick: () => {
        navigate(ROUTES.ACCOUNT);
      }
    });
  }

  const handleWaiverSubmission = (
    { orderId, moveId, address }: OutstandingFnpsAgreement,
    onSuccess?: () => void
  ) => {
    gtmEvents.submitAgreement('fragile_non_paved_surface_waiver', orderId);
    const signFnpsRequest: SignFnpsWaiverRequest = {
      orderId,
      moveId,
      firstName: customer.firstName,
      lastName: customer.lastName,
      address,
      ...getTimezoneOffset()
    };
    signFnpsWaiver(signFnpsRequest, {
      onSuccess: () => {
        setNotification({
          isError: false,
          message: translate(
            TranslationKeys.CommonComponents.Notification.SIGN_FNPS_WAIVER_SUCCEEDED
          )
        });
        if (entryPointResult.outstandingFnpsAgreements.length === 1) setIsFnpsModalOpen(false);
        if (onSuccess) onSuccess();
        gtmEvents.successAgreement('fragile_non_paved_surface_waiver', orderId);
        removeOutstandingFnpsAgreement(orderId, moveId);
      },
      onError: () => {
        setShowAlert(true);
      }
    });
  };
  return (
    <>
      <Grid ref={ref} {...globalBannerStyles}>
        {banners.map((banner, index) => (
          <Banner key={`${banner.title}-${index}`} {...banner} />
        ))}
      </Grid>
      <FNPSModal
        modalId="fnpsModal"
        onClose={() => {
          setShowAlert(false);
          setIsFnpsModalOpen(false);
        }}
        open={isFnpsModalOpen}
        isLoading={isPending}
        showAlert={showAlert}
        onClickActionButton={handleWaiverSubmission}
        customer={customer}
      />
    </>
  );
});

const globalBannerStyles = {
  sx: {
    width: '100%'
  }
};

const Banner: React.FC<BannerProps> = ({
  title,
  message,
  ctaLabel,
  onButtonClick,
  dismissible = false,
  onDismiss
}: BannerProps) => {
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = style(isMobile, dismissible);
  const [showBanner, setShowBanner] = useState(true);

  const handleDismiss = () => {
    if (onDismiss) onDismiss();
    setShowBanner(false);
  };

  return (
    showBanner && (
      <Grid container {...styles.bannerContainer}>
        {/* // The next two grids apply the styles that align our alerts with the nav bar */}
        <PageLayout columnsLg={9}>
          <Grid container item {...styles.innerContainer}>
            <Grid item sx={{ paddingX: Design.Primitives.Spacing.xs }}>
              <WarningIcon />
            </Grid>
            <Grid item flexDirection={isMobile ? 'column' : 'row'} {...styles.content}>
              <Grid item flexDirection="column" {...styles.textContainer}>
                <Typography {...styles.title}>{title}</Typography>
                <Typography {...styles.message}>{message}</Typography>
              </Grid>
              <Button
                variant={dismissible ? 'outlined' : 'contained'}
                color="warning"
                onClick={onButtonClick}
                {...styles.ctaButton}>
                <Typography {...styles.ctaLabel}>{ctaLabel}</Typography>
              </Button>
            </Grid>
            {dismissible && (
              <Grid item>
                <IconButton {...styles.closeButton} onClick={handleDismiss}>
                  <DismissXIcon />
                </IconButton>
              </Grid>
            )}
          </Grid>
        </PageLayout>
      </Grid>
    )
  );
};
// TODO: our MuiTheme overrides the ability to use the color prop, create color variants in the theme
// As we make an alert or info variant, we can begin swapping the color variables into the styling.
const transparentWarningColor = `${Design.Alias.Color.warningMain}1A`; // 4% opacity, is what Mui uses
const style = (isMobile: boolean, dismissible: boolean) => ({
  bannerContainer: {
    sx: {
      minHeight: isMobile ? 'fit-content' : '77px',
      flexDirection: 'row',
      backgroundColor: Design.Alias.Color.warningLight,
      justifyContent: 'center',
      alignItems: isMobile ? 'flexStart' : 'center',
      color: Design.Alias.Color.warningDark
    }
  },
  innerContainer: {
    sx: {
      [theme.breakpoints.between('xs', 'md')]: {
        padding: '16px'
      },
      justifyContent: 'center',
      alignItems: isMobile ? 'flexStart' : 'center'
    }
  },
  content: {
    sx: {
      display: 'flex',
      flex: 1,
      justifyContent: 'space-between',
      alignItems: isMobile ? 'flexStart' : 'center',
      columnGap: Design.Primitives.Spacing.xxs
    }
  },
  textContainer: {
    sx: {
      display: 'flex',
      rowGap: isMobile ? Design.Primitives.Spacing.xxs : Design.Primitives.Spacing.xxxs,
      width: 'fit-content'
    }
  },
  title: {
    ...Design.Alias.Text.BodyUniversal.MdBold,
    color: 'inherit'
  },
  message: {
    ...Design.Alias.Text.BodyUniversal.Sm,
    color: 'inherit'
  },
  ctaButton: {
    sx: {
      '&.MuiButtonBase-root': {
        ...Design.Alias.Text.BodyUniversal.SmBold,
        color: dismissible ? 'inherit' : Design.Alias.Color.warningDark,
        textTransform: 'none',
        transitionBehavior: 'normal, normal, normal, normal',
        transitionDelay: '0s, 0s, 0s, 0s',
        transitionDuration: '0.25s, 0.25s, 0.25s, 0.25s',
        transitionProperty: 'background-color, box-shadow, border-color, color',
        transitionTimingFunction: 'cubic-bezier(0.4, 0, 0.2)',
        backgroundColor: dismissible ? 'inherit' : Design.Alias.Color.warningDark,
        borderColor: Design.Alias.Color.warningDark,
        border: `1px solid`,
        minWidth: 'fit-content',
        marginTop: isMobile ? Design.Primitives.Spacing.xxs : '0px',
        '&:disabled': {
          color: transparentWarningColor,
          backgroundColor: transparentWarningColor,
          outlineColor: Design.Alias.Color.warningDark,
          border: `1px solid ${Design.Alias.Color.warningDark}`
        },
        '&:hover': {
          backgroundColor: dismissible ? transparentWarningColor : Design.Alias.Color.warningDarkest
        },
        '&:active': {
          backgroundColor: `${Design.Alias.Color.warningMain}B3` // 80% opacity
        }
      }
    }
  },
  ctaLabel: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.SmBold,
      color: dismissible ? 'inherit' : Design.Primitives.Color.NeutralLight.white,
      textWrap: 'noWrap'
    }
  },
  closeButton: {
    sx: {
      paddingLeft: Design.Primitives.Spacing.xs,
      padding: 0,
      marginLeft: isMobile ? '0px' : '20px'
    }
  }
});
