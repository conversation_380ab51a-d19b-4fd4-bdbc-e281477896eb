import React from 'react';
import {
  Box,
  List,
  Divider,
  useMediaQuery,
  ListItemIcon,
  ListItemText,
  ListItemButton
} from '@mui/material';
import { useLocation, useNavigate } from 'react-router';
import { CreditCard, Files, Package, SignOut, UserCircle } from '@phosphor-icons/react';
import { theme } from '../../../../PodsTheme';
import { LeftSideDesktopNavigation } from './LeftSideDesktopNavigation';
import { LeftSideMobileNavigation } from './LeftSideMobileNavigation';
import { useGetCustomer } from '../../../../networkRequests/queries/useGetCustomer';
import { ROUTES } from '../../../../Routes';
import { Design } from '../../../../helpers/Design';
import { ENV_VARS } from '../../../../environment';
import { LeftSideNavMenuListItem } from './LeftSideNavMenuListItem';

export const LegacyLeftSideNavigationBar = () => {
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const location = useLocation();
  const navigate = useNavigate();
  const { customer } = useGetCustomer();

  // Navigation items
  const navItems = [
    {
      title: 'MyPods',
      path: ROUTES.HOME,
      icon: <Package size={24} style={{ color: Design.Alias.Color.secondary500 }} />
    },
    {
      title: 'Billing',
      path: ROUTES.BILLING,
      icon: <CreditCard size={24} style={{ color: Design.Alias.Color.secondary500 }} />
    },
    {
      title: 'Documents',
      path: ROUTES.DOCUMENT,
      icon: <Files size={24} style={{ color: Design.Alias.Color.secondary500 }} />
    }
  ];

  const userName = customer ? `${customer.firstName} ${customer.lastName}` : '';
  const userEmail = customer?.email?.address || '';
  const styles = sidebarStyles(isMobile);
  const sidebarContent = (
    <Box sx={styles.sidebarBox}>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          height: '100%',
          overflowX: 'visible',
          overflowY: 'hidden'
        }}>
        <List sx={styles.navList}>
          {navItems.map((item) => (
            <LeftSideNavMenuListItem
              key={item.title}
              item={item}
              onClick={() => {
                navigate(item.path);
              }}
              selectedItem={location.pathname === item.path}
            />
          ))}
        </List>
        <Divider sx={{ my: Design.Primitives.Spacing.xs }} />
        <Box sx={styles.userInfoBox}>
          <ListItemButton
            onClick={() => {
              navigate(ROUTES.ACCOUNT);
            }}
            sx={styles.userProfile}>
            <ListItemIcon sx={styles.ItemIcon}>
              <UserCircle
                size={24}
                style={{
                  color:
                    location.pathname === ROUTES.ACCOUNT
                      ? Design.Primitives.Color.Blue.oasis
                      : Design.Alias.Color.accent900
                }}
              />
            </ListItemIcon>
            <ListItemText
              primary={userName}
              secondary={userEmail}
              primaryTypographyProps={styles.userName}
              secondaryTypographyProps={styles.userEmail}
            />
          </ListItemButton>
          <Box component="a" href={ENV_VARS.LOGOUT} sx={styles.logoutButton}>
            <ListItemIcon sx={styles.ItemIcon}>
              <SignOut size={24} style={{ color: Design.Alias.Color.neutral700 }} />
            </ListItemIcon>
            <ListItemText primary="Logout" primaryTypographyProps={styles.logoutText} />
          </Box>
        </Box>
      </Box>
    </Box>
  );

  return (
    <Box sx={{ display: 'flex' }}>
      {isMobile ? (
        <LeftSideMobileNavigation>{sidebarContent}</LeftSideMobileNavigation>
      ) : (
        <LeftSideDesktopNavigation>{sidebarContent}</LeftSideDesktopNavigation>
      )}
    </Box>
  );
};

const sidebarStyles = (isMobile: boolean) => ({
  sidebarBox: {
    display: 'flex',
    flexDirection: 'column',
    height: '100vh',
    width: { xs: '100vw', md: Design.Alias.Sizing.leftNavDrawerWidth },
    background: Design.Alias.Color.neutral100,
    paddingTop: Design.Primitives.Spacing.md,
    paddingBottom: Design.Primitives.Spacing.md,
    paddingLeft: Design.Primitives.Spacing.sm,
    paddingRight: Design.Primitives.Spacing.sm,
    boxSizing: 'border-box'
  },
  navList: {
    width: '100%',
    overflowY: 'auto',
    overflowX: 'visible',
    maxHeight: 'calc(100vh - 300px)',
    padding: 0
  },
  userInfoBox: {
    mt: isMobile ? '0' : 'auto',
    mb: Design.Primitives.Spacing.sm
  },
  userProfile: {
    display: 'flex',
    alignItems: 'center',
    padding: Design.Primitives.Spacing.sm,
    gap: Design.Primitives.Spacing.xxxs,
    marginLeft: '6px',
    borderRadius: '8px',
    backgroundColor:
      window.location.pathname === ROUTES.ACCOUNT
        ? Design.Primitives.Color.Blue.oasis100
        : 'transparent',
    color:
      window.location.pathname === ROUTES.ACCOUNT
        ? Design.Primitives.Color.Blue.oasis
        : Design.Alias.Color.accent900,
    '&:hover': {
      backgroundColor: Design.Primitives.Color.Blue.oasis100
    }
  },
  ItemIcon: {
    color:
      window.location.pathname === ROUTES.ACCOUNT
        ? Design.Primitives.Color.Blue.oasis
        : Design.Alias.Color.accent900,
    minWidth: '32px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
  },
  userName: {
    ...Design.Alias.Text.BodyUniversal.MdBold,
    color:
      window.location.pathname === ROUTES.ACCOUNT
        ? Design.Primitives.Color.Blue.oasis
        : Design.Alias.Color.accent900
  },
  userEmail: {
    ...Design.Alias.Text.BodyUniversal.Sm,
    color: Design.Primitives.Color.Blue.oasis,
    wordBreak: 'break-all',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
    maxWidth: '180px'
  },
  logoutButton: {
    display: 'flex',
    alignItems: 'center',
    padding: Design.Primitives.Spacing.sm,
    cursor: 'pointer',
    textDecoration: 'none',
    gap: Design.Primitives.Spacing.xxxs,
    marginLeft: '6px'
  },
  logoutText: {
    ...Design.Alias.Text.Heading.Desktop.Xs,
    fontWeight: Design.Alias.Text.BodyUniversal.Xxs.fontWeight,
    color: Design.Alias.Color.neutral700,
    textDecoration: 'none'
  }
});
