import React, { ReactNode } from 'react';
import { AppBar, Grid, SwipeableDrawer, Toolbar } from '@mui/material';
import { Box } from '@mui/system';
import { Design } from '../../../../helpers/Design';
import { MenuToggleIcon } from '../../../NavigationBar/MenuToggleIcon';
import { ENV_VARS } from '../../../../environment';

interface Props {
  children: ReactNode;
}

export const LeftSideMobileNavigation: React.FC<Props> = ({ children }: Props) => {
  const [isMenuOpen, setIsMenuOpen] = React.useState(false);
  const [isClosing, setIsClosing] = React.useState(false);

  const handleDrawerClose = () => {
    setIsClosing(true);
    setIsMenuOpen(false);
  };

  const handleDrawerTransitionEnd = () => {
    setIsClosing(false);
  };

  const handleDrawerToggle = () => {
    if (!isClosing) {
      setIsMenuOpen(!isMenuOpen);
    }
  };

  return (
    <>
      <AppBar position="fixed" {...styles.appBar}>
        <Toolbar {...styles.imageGrid}>
          <MenuToggleIcon onClickToggle={handleDrawerToggle} isMenuOpen={isMenuOpen} />
          <img
            src={`${ENV_VARS.ASSETS_BASE_URL}/pods-secondary-logo-rgb-1.webp`}
            alt="pods-logo"
            {...styles.image}
          />
        </Toolbar>
      </AppBar>
      <SwipeableDrawer
        {...styles.drawer}
        variant="temporary"
        anchor="top"
        open={isMenuOpen}
        onOpen={handleDrawerToggle}
        onClose={handleDrawerClose}
        onTransitionEnd={handleDrawerTransitionEnd}>
        <Box sx={{ pt: '52px' }}>{children}</Box>
        <Grid {...styles.swipeBar}>
          <Box {...styles.indicator} />
        </Grid>
      </SwipeableDrawer>
    </>
  );
};

const styles = {
  imageGrid: {
    sx: {
      width: '100%',
      height: '52px',
      flexShrink: '0',
      display: 'flex',
      alignItems: 'center',
      background: Design.Alias.Color.neutral100,
      justifyContent: 'flex-start',
      borderRight: `1px solid ${Design.Alias.Color.neutral200}`,
      borderBottom: `1px solid ${Design.Alias.Color.neutral200}`,
      paddingLeft: '16px'
    }
  },
  image: {
    style: {
      width: '100px',
      height: '22px',
      flexShrink: 0
    }
  },
  appBar: {
    zindex: Design.Alias.ZIndex.appBar,
    gap: Design.Primitives.Spacing.md
  },
  drawer: {
    sx: {
      width: '100%',
      height: '100%',
      zIndex: Design.Alias.ZIndex.leftNavDrawer,
      '.MuiDrawer-paper': {
        height: '100%',
        backgroundColor: Design.Alias.Color.neutral200
      }
    }
  },
  swipeBar: {
    sx: {
      position: 'absolute',
      bottom: 0,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      width: '100%',
      height: '72px',
      backgroundColor: Design.Alias.Color.neutral100,
      left: 0
    }
  },
  indicator: {
    sx: {
      width: '135px',
      height: '5px',
      borderRadius: '100px',
      background: Design.Primitives.Color.NeutralDark.black
    }
  }
};
