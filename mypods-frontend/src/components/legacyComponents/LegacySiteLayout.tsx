import React, { ReactNode } from 'react';
import { Grid, useMediaQuery } from '@mui/material';
import { theme } from '../../PodsTheme';
import { Design } from '../../helpers/Design';
import { LegacyNavigationBar } from './NavigationBar/LegacyNavigationBar';
import { LegacyFooter } from './Footer/LegacyFooter';
import { LEFT_NAV_ENABLED, useFeatureFlags } from '../../helpers/useFeatureFlags';
import { LegacyLeftSideNavSiteLayout } from './LegacyLeftSideNavSiteLayout';
import { ScrollRestoration } from '../../routes/ScrollRestoration';

interface LayoutProps {
  children: ReactNode;
  showHeaderFooter?: boolean;
  globalBanners?: ReactNode;
}

export const LegacySiteLayout: React.FC<LayoutProps> = ({
  children,
  showHeaderFooter = true,
  globalBanners
}: LayoutProps) => {
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = siteLayoutStyles(isMobile);
  const { isLeftNavEnabled } = useFeatureFlags([LEFT_NAV_ENABLED]);

  return isLeftNavEnabled() ? (
    <LegacyLeftSideNavSiteLayout>{children}</LegacyLeftSideNavSiteLayout>
  ) : (
    <Grid {...styles.container}>
      {showHeaderFooter && <LegacyNavigationBar />}
      <ScrollRestoration>
        <Grid {...styles.belowHeaderContent}>
          {globalBanners}
          <Grid {...styles.pageLayoutContent}>{children}</Grid>
          {showHeaderFooter && <LegacyFooter />}
        </Grid>
      </ScrollRestoration>
    </Grid>
  );
};

// -- styles --
const siteLayoutStyles = (isMobile: boolean) => {
  const navBarHeight = isMobile ? '52px' : '62px'; // heights are set in the PodsTheme
  return {
    container: {
      sx: {
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
        alignItems: 'center',
        minHeight: `calc(100vh - ${navBarHeight})`,
        marginTop: navBarHeight
      }
    },
    belowHeaderContent: {
      sx: {
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
        overflowY: 'auto',
        width: '100%',
        alignItems: 'center',
        flexGrow: 1
      }
    },
    pageLayoutContent: {
      sx: {
        paddingTop: {
          xs: Design.Primitives.Spacing.md,
          md: Design.Primitives.Spacing.lgPlus
        },
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        flex: 1
      }
    }
  };
};
