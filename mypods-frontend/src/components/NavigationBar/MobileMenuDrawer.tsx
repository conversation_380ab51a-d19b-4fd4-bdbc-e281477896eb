import React from 'react';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import Drawer from '@mui/material/Drawer';
import { useLocation, useNavigate } from 'react-router';
import { useTranslation } from 'react-i18next';
import Divider from '@mui/material/Divider';
import { MobileNavigationItem } from '../ComponentTypes';
import { MenuListItem, SupportListItem } from './MenuListItem';
import { TranslationKeys } from '../../locales/TranslationKeys';
import { ROUTES } from '../../Routes';
import { AccountIcon } from '../icons/AccountIcon';
import { BillingIcon } from '../icons/BillingIcon';
import { DocumentIcon } from '../icons/DocumentIcon';
import { ExclamationIcon } from '../icons/ExclamationIcon';
import { HomeIcon } from '../icons/HomeIcon';
import { LogoutIcon } from '../icons/LogoutIcon';
import { SupportIcon } from '../icons/SupportIcon';
import { isNonProdEnv } from '../../pages/DebugPage/DebugPage';
import { ENV_VARS } from '../../environment';
import { Design } from '../../helpers/Design';

interface Props {
  isOpen: boolean;
  setIsMobileMenuOpen: (isMobileMenuOpen: boolean) => void;
  customerEmail?: string | undefined;
}

export const MobileMenuDrawer: React.FC<Props> = ({
  isOpen,
  setIsMobileMenuOpen,
  customerEmail
}: Props) => {
  // -- hooks --
  const navigate = useNavigate();
  const location = useLocation();
  const { t: translate } = useTranslation();

  // -- constants --
  const currentPath = location.pathname;

  // -- handlers --
  const onItemClicked = (item: MobileNavigationItem) => {
    setIsMobileMenuOpen(false);
    if (item.path.match(/^http(s?):\/\//)) {
      window.location.replace(item.path);
      return;
    }

    navigate(item.path);
  };

  const defaultNavItems: MobileNavigationItem[] = [
    {
      title: translate(TranslationKeys.Navigation.HOME),
      path: ROUTES.HOME,
      icon: <HomeIcon {...styles.baseIcon} />
    },
    {
      title: translate(TranslationKeys.Navigation.BILLING),
      path: ROUTES.BILLING,
      icon: <BillingIcon {...styles.baseIcon} />
    },
    {
      title: translate(TranslationKeys.DocumentsPage.HEADER),
      path: ROUTES.DOCUMENT,
      icon: <DocumentIcon {...styles.baseIcon} />
    }
  ];

  const SupportNavItem: MobileNavigationItem = {
    title: translate(TranslationKeys.Navigation.SUPPORT_PHONE_NUMBER_MOBILE_LABEL),
    subtitle: translate(TranslationKeys.Navigation.SUPPORT_PHONE_NUMBER),
    path: 'tel:************',
    icon: <SupportIcon {...styles.baseIcon} />
  };

  const accountNavItems: MobileNavigationItem[] = [
    {
      title: translate(TranslationKeys.Navigation.ACCOUNT),
      subtitle: customerEmail,
      path: ROUTES.ACCOUNT,
      icon: <AccountIcon {...styles.baseIcon} />
    },
    {
      title: translate(TranslationKeys.Navigation.Dropdown.LOGOUT),
      path: ENV_VARS.LOGOUT,
      icon: <LogoutIcon {...styles.baseIcon} />
    }
  ];

  const debugNavItem: MobileNavigationItem = {
    title: 'Debug',
    path: ROUTES.DEBUG,
    icon: <ExclamationIcon {...styles.baseIcon} />
  };

  return (
    <Drawer
      anchor="top"
      variant="temporary"
      open={isOpen}
      hideBackdrop
      transitionDuration={300}
      PaperProps={{ elevation: 0 }}
      onClick={() => {
        setIsMobileMenuOpen(false);
      }}
      data-testid="drawer"
      {...styles.drawer}>
      <List {...styles.list}>
        <MobileMenuSection
          items={defaultNavItems}
          onItemClicked={onItemClicked}
          currentPath={currentPath}
        />
        <Divider {...styles.divider} />
        <SupportListItem item={SupportNavItem} selectedItem={currentPath === SupportNavItem.path} />
        <Divider {...styles.divider} />
        <MobileMenuSection
          items={accountNavItems}
          onItemClicked={onItemClicked}
          currentPath={currentPath}
        />

        {isNonProdEnv() && (
          <MobileMenuSection
            items={[debugNavItem]}
            onItemClicked={onItemClicked}
            currentPath={currentPath}
            key="debug"
          />
        )}
      </List>
    </Drawer>
  );
};

interface SectionProps {
  items: MobileNavigationItem[];
  onItemClicked: (item: MobileNavigationItem) => void;
  currentPath: string;
}

const MobileMenuSection: React.FC<SectionProps> = ({ items, onItemClicked, currentPath }) => (
  <>
    {items.map((item, index) => (
      <ListItem {...styles.section.listItem} key={`${item}-${index}`}>
        <MenuListItem
          item={item}
          onClick={() => {
            onItemClicked(item);
          }}
          selectedItem={currentPath === item.path}
        />
      </ListItem>
    ))}
  </>
);

const styles = {
  drawer: {
    sx: {
      zIndex: Design.Alias.ZIndex.leftNavDrawer,
      '.MuiPaper-root': {
        position: 'relative',
        marginTop: '52px'
      }
    }
  },
  list: {
    style: {
      padding: 0,
      margin: 0,
      height: '99vh'
    }
  },
  listItem: {
    style: {
      padding: 0,
      margin: 0,
      height: '72px'
    }
  },
  divider: {
    style: {
      marginTop: '12px',
      marginBottom: '12px'
    }
  },
  baseIcon: {
    style: {
      width: Design.Primitives.Spacing.md
    }
  },
  section: {
    listItem: {
      style: {
        padding: 0,
        margin: 0,
        height: '72px'
      }
    },
    divider: {
      style: {
        marginTop: '12px',
        marginBottom: '12px'
      }
    }
  }
};
