import * as React from 'react';
import { useState } from 'react';
import Button from '@mui/material/Button';
import Menu from '@mui/material/Menu';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';
import { Box } from '@mui/material';
import { Design } from '../../../helpers/Design';
import { Customer } from '../../../networkRequests/responseEntities/CustomerEntities';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { AccountMenuItem, AccountMenuOption } from './AccountMenuOption';
import { ROUTES } from '../../../Routes';
import { ENV_VARS } from '../../../environment';

interface Props {
  customer: Customer;
}

export const NavigationDropdown: React.FC<Props> = ({ customer }: Props) => {
  const [anchorWidth, setAnchorWidth] = useState<number>(180);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const styles = navigationDropdownStyles(open, anchorWidth);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorWidth(event.currentTarget.getBoundingClientRect().width);
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  const dropDownOptions: AccountMenuItem[] = [
    {
      text: TranslationKeys.Navigation.Dropdown.ACCOUNT,
      subtext: customer.email?.address,
      path: ROUTES.ACCOUNT
    },
    {
      text: TranslationKeys.Navigation.Dropdown.LOGOUT,
      path: ENV_VARS.LOGOUT,
      endsSection: true
    }
  ];

  return (
    <Box {...styles.navigationDropdown}>
      <Button
        {...styles.dropdownButton}
        id="customer-dropdown-button"
        aria-controls={open ? 'customer-dropdown-menu' : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
        variant="text"
        disableElevation
        onClick={handleClick}
        endIcon={open ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}>
        {`${customer.firstName} ${customer.lastName.charAt(0)}.`}
      </Button>
      <Menu
        {...styles.accountMenu}
        id="customer-dropdown-menu"
        MenuListProps={{
          'aria-labelledby': 'customer-dropdown-menu-item'
        }}
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}>
        {dropDownOptions.map((option) => (
          <AccountMenuOption option={option} handleClose={handleClose} key={option.text} />
        ))}
      </Menu>
    </Box>
  );
};

// -- styles --
const navigationDropdownStyles = (isOpen: boolean, anchorWidth: number) => ({
  navigationDropdown: {
    sx: {
      fontFamily: 'Open Sans',
      display: 'flex',
      justifyContent: 'flex-end',
      alignItems: 'flex-start'
    }
  },
  dropdownButton: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.Md,
      fontWeight: 600,
      textTransform: 'none',
      borderRadius: '0',
      borderBottom: isOpen ? `${Design.Alias.Color.secondary500} solid 2px` : '',
      paddingBottom: isOpen ? `0px` : '2px',
      color: isOpen ? Design.Alias.Color.secondary500 : Design.Alias.Color.neutral800
    }
  },
  accountMenu: {
    sx: {
      '.MuiPaper-root': { width: anchorWidth, minWidth: '160px' },
      '.MuiMenuList-root': { width: '100%' },
      anchorOrigin: {
        vertical: 'bottom',
        horizontal: 'right'
      },
      transformOrigin: {
        vertical: 'top',
        horizontal: 'right'
      },
      top: '4px'
    }
  }
});
