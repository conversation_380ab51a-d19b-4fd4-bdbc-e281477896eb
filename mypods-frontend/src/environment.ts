import { <PERSON><PERSON>anagerArgs } from 'react-gtm-module';
import {
  SALESFORCE_BAUTEST_VARIABLES,
  SALESFORCE_TEST_VARIABLES,
  SALESFORCE_TEST_VARIABLES_COMMERCIAL,
  SALESFORCE_PROD_VARIABLES,
  SALESFORCE_PROD_VARIABLES_COMMERCIAL,
  SFLiveChatVariables_Commercial,
  SFLiveChatVariables_Residential,
  SALESFORCE_STAGE_VARIABLES,
  SALESFORCE_STAGE_COMMERCIAL_VARIABLES
} from './chat/SalesforceLiveChatVariables';

export enum Environment {
  LOCAL = 'local',
  DEV = 'dev',
  TEST = 'test',
  BAUTEST = 'bautest',
  STAGE = 'stage',
  PROD = 'prod'
}

let environment: Environment;

const { hostname } = window.location;

switch (hostname) {
  case '127.0.0.1':
  case 'dev.my.pods.com':
  case 'int.my.pods.com':
    environment = Environment.DEV;
    break;
  case 'test.my.pods.com':
    environment = Environment.TEST;
    break;
  case 'bautest.my.pods.com':
    environment = Environment.BAUTEST;
    break;
  case 'stage.my.pods.com':
    environment = Environment.STAGE;
    break;
  case 'my.pods.com':
    environment = Environment.PROD;
    break;
  default:
    environment = Environment.LOCAL;
    break;
}

interface ApigeeOAuthVariables {
  APIGEE_CLIENT_ID: string;
  APIGEE_CLIENT_SECRET: string;
  APIGEE_OAUTH_URL: string;
}

export interface CommonEnvironmentVariables {
  readonly ENVIRONMENT: Environment;
  // Backend API
  readonly APIGEE_API_URL: string;
  readonly APIGEE_AUTH_TOKEN: string;
  readonly GOOGLE_PLACE_AND_MAPS_API_KEY: string;
  readonly BRAINTREE_TOKENIZATION_KEY: string;
  readonly ASSETS_BASE_URL: string;
  readonly RBF_ASSETS_BASE_URL: string;
  readonly LOGOUT: string;
  readonly MYPODS_LOGIN: string;
  readonly GTM_ARGS: TagManagerArgs;
  readonly DATADOG?: {
    readonly APPLICATION_ID: string;
    readonly CLIENT_TOKEN: string;
    readonly ENVIRONMENT_NAME: string;
  };
  readonly ACORN_FINANCE_WIDGET: string;
  readonly ACORN_DEPLOYMENT_ENVIRONMENT_NAME: string;
  readonly ACORN_DISCLOSURE_WIDGET: string;
  readonly SPLIT_CLIENT_KEY: string;
}

export type EnvironmentVariables = CommonEnvironmentVariables &
  SFLiveChatVariables_Residential &
  SFLiveChatVariables_Commercial &
  ApigeeOAuthVariables;

const APIGEE_AUTH_TOKEN_NON_PROD = '0hQh5uI2kcCxfE6mYGUZbAUFBTnJWMBiHMHfRalwdxX7dGaP';
const APIGEE_AUTH_TOKEN_PROD = 'foobar';

const APIGEE_NON_PROD_VARIABLES = {
  APIGEE_CLIENT_ID: '0hQh5uI2kcCxfE6mYGUZbAUFBTnJWMBiHMHfRalwdxX7dGaP',
  APIGEE_CLIENT_SECRET: '8Jux0qUQZXES6GLIrGciLMDtz2oEkeRjDqMlDeLZGWT7J9i41uCfc1C1pNT05z2h'
};

const APIGEE_PROD_VARIABLES = {
  APIGEE_CLIENT_ID: '6UATOy62263lGPVnPSje7PLAxG5GXAZAjpRzluxVSHcdgEJf',
  APIGEE_CLIENT_SECRET: '5CYhs8UfNamYNWaTkk6oAaUJwA4ZpPz6l0yw7ygwff7mRFVYbQIBpvkG1xKtq8bx'
};

const APIGEE_API_URLS = {
  local: 'http://localhost:8081/mypods',
  developmentOrInt: 'https://int.apigw.pods.com/mypods',
  test: 'https://test.apigw.pods.com/mypods',
  bau: 'https://bautest.apigw.pods.com/mypods-bau',
  stage: 'https://stage.apigw.pods.com/mypods',
  production: 'https://apigw.pods.com/mypods'
};

const APIGEE_OAUTH_URLS = {
  developmentOrInt: 'https://int.apigw.pods.com/pods-auth/getTokenCors',
  test: 'https://test.apigw.pods.com/pods-auth/getTokenCors',
  bau: 'https://bautest.apigw.pods.com/pods-auth/getTokenCors',
  stage: 'https://stage.apigw.pods.com/pods-auth/getTokenCors',
  production: 'https://apigw.pods.com/pods-auth/getTokenCors'
};

const NP_GOOGLE_PLACE_AND_MAPS_API_KEY = 'AIzaSyCzTjDACyPrREsuNeE1WU23imlD33AAxp0';

const NP_BRAINTREE_TOKENIZATION_KEY = 'sandbox_d52sqcp2_m56f9t9vgm3x9v72';

const NP_ASSET_BLOB_URL = 'https://salrseuspodsmypodsnp01.blob.core.windows.net/mypods';

const PD_ASSET_BLOB_URL = 'https://sagrseuspodsmypodspd01.blob.core.windows.net/mypods';

const NP_RBF_ASSETS_BASE_URL =
  'https://st-rbf-storage-account-aac5h9bwg8aycwen.z01.azurefd.net/rbf';

const ACORN_FINANCE_WIDGETS = {
  uat: 'https://uat.widgets-cdn.acornfinance.com/paw/v5/dist/af-pods-mypods-v2.min.js',
  qa: 'https://qa.widgets-cdn.acornfinance.com/paw/v5/dist/af-pods-mypods-v2.min.js',
  production: 'https://widgets-cdn.acornfinance.com/paw/v5/dist/af-pods-mypods-v2.min.js'
};

const ACORN_DISCLOSURE_WIDGETS = {
  uat: 'https://uat.widgets-cdn.acornfinance.com/paw/v5/dist/af-pods-lender-disclosures.min.js',
  qa: 'https://qa.widgets-cdn.acornfinance.com/paw/v5/dist/af-pods-lender-disclosures.min.js',
  production: 'https://widgets-cdn.acornfinance.com/paw/v5/dist/af-pods-lender-disclosures.min.js'
};

const NP_GTM_ARGS: TagManagerArgs = {
  gtmId: 'GTM-M76DKFXL',
  auth: 'EoSCyqU5i4oJgowdMWocfA',
  preview: 'env-3'
};
const PD_GTM_ARGS: TagManagerArgs = { gtmId: 'GTM-M76DKFXL' };

const SPLIT_ARGS = {
  developmentOrInt: 'd3k0p5ap9f4rjf884ssbaglo9c2317snvqfh',
  test: 'esj36lbsoff3qasg5rho2050gmn07covqv2i',
  bau: 'tcalngd20e1lplg5sin59lfepvokq8b6auph',
  stage: 'gb9k6kmdoq8njuojmkt13sb1281leslsctps',
  production: '8mdkjnbj0jop1ivpkisb8fss2fbh1qblmrlf'
};

const AUTH_BASE_URLS = {
  stage: 'https://stage.auth.pods.com',
  developmentOrInt: 'https://int.auth.pods.com',
  test: 'https://test.auth.pods.com',
  bau: 'https://bautest.auth.pods.com',
  production: 'https://auth.pods.com',
  local: 'http://localhost:3000',
  local_debug: 'http://localhost:3001/debug'
};

const LocalEnvironment: EnvironmentVariables = {
  ENVIRONMENT: Environment.LOCAL,
  APIGEE_API_URL: APIGEE_API_URLS.local,
  APIGEE_AUTH_TOKEN: APIGEE_AUTH_TOKEN_NON_PROD,
  APIGEE_OAUTH_URL: APIGEE_OAUTH_URLS.test,
  GOOGLE_PLACE_AND_MAPS_API_KEY: NP_GOOGLE_PLACE_AND_MAPS_API_KEY,
  BRAINTREE_TOKENIZATION_KEY: NP_BRAINTREE_TOKENIZATION_KEY,
  ASSETS_BASE_URL: NP_ASSET_BLOB_URL,
  RBF_ASSETS_BASE_URL: NP_RBF_ASSETS_BASE_URL,
  LOGOUT: AUTH_BASE_URLS.local_debug,
  MYPODS_LOGIN: `${AUTH_BASE_URLS.local}/login`,
  GTM_ARGS: NP_GTM_ARGS,
  SPLIT_CLIENT_KEY: SPLIT_ARGS.test,
  ACORN_FINANCE_WIDGET: ACORN_FINANCE_WIDGETS.qa,
  ACORN_DEPLOYMENT_ENVIRONMENT_NAME: 'QA',
  ACORN_DISCLOSURE_WIDGET: ACORN_DISCLOSURE_WIDGETS.qa,
  ...APIGEE_NON_PROD_VARIABLES,
  ...SALESFORCE_TEST_VARIABLES,
  ...SALESFORCE_TEST_VARIABLES_COMMERCIAL
};

const DevEnvironment: EnvironmentVariables = {
  ENVIRONMENT: Environment.DEV,
  APIGEE_API_URL: APIGEE_API_URLS.developmentOrInt,
  APIGEE_AUTH_TOKEN: APIGEE_AUTH_TOKEN_NON_PROD,
  APIGEE_OAUTH_URL: APIGEE_OAUTH_URLS.developmentOrInt,
  GOOGLE_PLACE_AND_MAPS_API_KEY: NP_GOOGLE_PLACE_AND_MAPS_API_KEY,
  BRAINTREE_TOKENIZATION_KEY: NP_BRAINTREE_TOKENIZATION_KEY,
  ASSETS_BASE_URL: NP_ASSET_BLOB_URL,
  RBF_ASSETS_BASE_URL: NP_RBF_ASSETS_BASE_URL,
  LOGOUT: `${AUTH_BASE_URLS.developmentOrInt}/logout`,
  MYPODS_LOGIN: `${AUTH_BASE_URLS.developmentOrInt}/login`,
  ACORN_FINANCE_WIDGET: ACORN_FINANCE_WIDGETS.qa,
  ACORN_DEPLOYMENT_ENVIRONMENT_NAME: 'QA',
  ACORN_DISCLOSURE_WIDGET: ACORN_DISCLOSURE_WIDGETS.qa,
  SPLIT_CLIENT_KEY: SPLIT_ARGS.developmentOrInt,
  GTM_ARGS: NP_GTM_ARGS,
  ...APIGEE_NON_PROD_VARIABLES,
  ...SALESFORCE_TEST_VARIABLES,
  ...SALESFORCE_TEST_VARIABLES_COMMERCIAL
};

const TestEnvironment: EnvironmentVariables = {
  ENVIRONMENT: Environment.TEST,
  APIGEE_API_URL: APIGEE_API_URLS.test,
  APIGEE_AUTH_TOKEN: APIGEE_AUTH_TOKEN_NON_PROD,
  APIGEE_OAUTH_URL: APIGEE_OAUTH_URLS.test,
  GOOGLE_PLACE_AND_MAPS_API_KEY: NP_GOOGLE_PLACE_AND_MAPS_API_KEY,
  BRAINTREE_TOKENIZATION_KEY: NP_BRAINTREE_TOKENIZATION_KEY,
  ASSETS_BASE_URL: NP_ASSET_BLOB_URL,
  RBF_ASSETS_BASE_URL: NP_RBF_ASSETS_BASE_URL,
  LOGOUT: `${AUTH_BASE_URLS.test}/logout`,
  MYPODS_LOGIN: `${AUTH_BASE_URLS.test}/login`,
  GTM_ARGS: NP_GTM_ARGS,
  ACORN_FINANCE_WIDGET: ACORN_FINANCE_WIDGETS.qa,
  ACORN_DEPLOYMENT_ENVIRONMENT_NAME: 'QA',
  ACORN_DISCLOSURE_WIDGET: ACORN_DISCLOSURE_WIDGETS.qa,
  SPLIT_CLIENT_KEY: SPLIT_ARGS.test,
  DATADOG: {
    APPLICATION_ID: 'dd492ccc-be03-4a13-9cda-8455aab1dd8e',
    CLIENT_TOKEN: 'pub3c15617a6f45fa194fc4b6caf8f4bfd9',
    ENVIRONMENT_NAME: 'te'
  },
  ...APIGEE_NON_PROD_VARIABLES,
  ...SALESFORCE_TEST_VARIABLES,
  ...SALESFORCE_TEST_VARIABLES_COMMERCIAL
};

const BAUTestEnvironment: EnvironmentVariables = {
  ENVIRONMENT: Environment.BAUTEST,
  APIGEE_API_URL: APIGEE_API_URLS.bau,
  APIGEE_AUTH_TOKEN: APIGEE_AUTH_TOKEN_NON_PROD,
  APIGEE_OAUTH_URL: APIGEE_OAUTH_URLS.bau,
  GOOGLE_PLACE_AND_MAPS_API_KEY: NP_GOOGLE_PLACE_AND_MAPS_API_KEY,
  BRAINTREE_TOKENIZATION_KEY: NP_BRAINTREE_TOKENIZATION_KEY,
  ASSETS_BASE_URL: NP_ASSET_BLOB_URL,
  RBF_ASSETS_BASE_URL: NP_RBF_ASSETS_BASE_URL,
  LOGOUT: `${AUTH_BASE_URLS.bau}/logout`,
  MYPODS_LOGIN: `${AUTH_BASE_URLS.bau}/login`,
  GTM_ARGS: NP_GTM_ARGS,
  ACORN_FINANCE_WIDGET: ACORN_FINANCE_WIDGETS.qa,
  ACORN_DEPLOYMENT_ENVIRONMENT_NAME: 'QA',
  ACORN_DISCLOSURE_WIDGET: ACORN_DISCLOSURE_WIDGETS.qa,
  SPLIT_CLIENT_KEY: SPLIT_ARGS.bau,
  ...APIGEE_NON_PROD_VARIABLES,
  ...SALESFORCE_BAUTEST_VARIABLES,
  ...SALESFORCE_TEST_VARIABLES_COMMERCIAL // No commercial chat in BAU, so just using a placeholder
};

const StageEnvironment: EnvironmentVariables = {
  ENVIRONMENT: Environment.STAGE,
  APIGEE_API_URL: APIGEE_API_URLS.stage,
  APIGEE_AUTH_TOKEN: APIGEE_AUTH_TOKEN_NON_PROD,
  APIGEE_OAUTH_URL: APIGEE_OAUTH_URLS.stage,
  GOOGLE_PLACE_AND_MAPS_API_KEY: NP_GOOGLE_PLACE_AND_MAPS_API_KEY,
  BRAINTREE_TOKENIZATION_KEY: NP_BRAINTREE_TOKENIZATION_KEY,
  ASSETS_BASE_URL: NP_ASSET_BLOB_URL,
  RBF_ASSETS_BASE_URL: NP_RBF_ASSETS_BASE_URL,
  LOGOUT: `${AUTH_BASE_URLS.stage}/logout`,
  MYPODS_LOGIN: `${AUTH_BASE_URLS.stage}/login`,
  GTM_ARGS: NP_GTM_ARGS,
  ACORN_FINANCE_WIDGET: ACORN_FINANCE_WIDGETS.uat,
  ACORN_DEPLOYMENT_ENVIRONMENT_NAME: 'UAT',
  ACORN_DISCLOSURE_WIDGET: ACORN_DISCLOSURE_WIDGETS.uat,
  SPLIT_CLIENT_KEY: SPLIT_ARGS.stage,
  DATADOG: {
    APPLICATION_ID: 'dd492ccc-be03-4a13-9cda-8455aab1dd8e',
    CLIENT_TOKEN: 'pub3c15617a6f45fa194fc4b6caf8f4bfd9',
    ENVIRONMENT_NAME: 'st'
  },
  ...APIGEE_NON_PROD_VARIABLES,
  ...SALESFORCE_STAGE_VARIABLES,
  ...SALESFORCE_STAGE_COMMERCIAL_VARIABLES
};

const ProdEnvironment: EnvironmentVariables = {
  ENVIRONMENT: Environment.PROD,
  APIGEE_API_URL: APIGEE_API_URLS.production,
  APIGEE_AUTH_TOKEN: APIGEE_AUTH_TOKEN_PROD,
  APIGEE_OAUTH_URL: APIGEE_OAUTH_URLS.production,
  GOOGLE_PLACE_AND_MAPS_API_KEY: 'AIzaSyBP_MJ7Xsht1NblDf8VhL66hr1Eci_Ey9c',
  BRAINTREE_TOKENIZATION_KEY: 'production_s9zy8gvr_h5fcng6zcprpxx9s',
  ASSETS_BASE_URL: PD_ASSET_BLOB_URL,
  RBF_ASSETS_BASE_URL: 'https://pd-rbf-storage-account-erdcfncfdba4btda.z01.azurefd.net/rbf',
  LOGOUT: `${AUTH_BASE_URLS.production}/logout`,
  MYPODS_LOGIN: `${AUTH_BASE_URLS.production}/login`,
  GTM_ARGS: PD_GTM_ARGS,
  ACORN_FINANCE_WIDGET: ACORN_FINANCE_WIDGETS.production,
  ACORN_DEPLOYMENT_ENVIRONMENT_NAME: 'Prod',
  ACORN_DISCLOSURE_WIDGET: ACORN_DISCLOSURE_WIDGETS.production,
  SPLIT_CLIENT_KEY: SPLIT_ARGS.production,
  DATADOG: {
    APPLICATION_ID: 'dd492ccc-be03-4a13-9cda-8455aab1dd8e',
    CLIENT_TOKEN: 'pub3c15617a6f45fa194fc4b6caf8f4bfd9',
    ENVIRONMENT_NAME: 'pd'
  },
  ...SALESFORCE_PROD_VARIABLES,
  ...SALESFORCE_PROD_VARIABLES_COMMERCIAL,
  ...APIGEE_PROD_VARIABLES
};

let environmentVariables: EnvironmentVariables;

switch (environment) {
  case Environment.DEV:
    environmentVariables = DevEnvironment;
    break;
  case Environment.TEST:
    environmentVariables = TestEnvironment;
    break;
  case Environment.BAUTEST:
    environmentVariables = BAUTestEnvironment;
    break;
  case Environment.STAGE:
    environmentVariables = StageEnvironment;
    break;
  case Environment.PROD:
    environmentVariables = ProdEnvironment;
    break;
  case Environment.LOCAL:
  default:
    environmentVariables = LocalEnvironment;
    break;
}

export const ENVIRONMENT = environment;
export const ENV_VARS = environmentVariables;
